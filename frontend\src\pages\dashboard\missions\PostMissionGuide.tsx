import React, { useState, useEffect } from 'react';
import {
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Stepper,
  Step,
  StepLabel,
  FormControlLabel,
  Checkbox,
  IconButton,
  Paper,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';
import { setCookie, getCookie } from '@/utils/cookieUtils';
import ModalPortal from '@/components/ModalPortal';

const COOKIE_NAME = 'postMissionGuideShown';

const StyledModal = styled('div')(({ theme }) => ({
  position: 'fixed',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  borderRadius: '16px',
  padding: '4px',
  backgroundColor: '#FFF8F3',
  maxWidth: '1280px',
  width: '94%',
  margin: '0px',
  maxHeight: '90vh',
  overflow: 'hidden',
  display: 'flex',
  flexDirection: 'column',
  zIndex: 1100,
}));

const StyledModalTitle = styled('div')({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  fontSize: '1.3rem',
  fontWeight: 'bold',
  color: '#FF6B2C',
  padding: '16px',
  '& .MuiIconButton-root': {
    color: '#FF6B2C',
  },
});

const StyledStepLabel = styled(StepLabel)({
  '& .MuiStepLabel-label': {
    color: '#FF6B2C',
    '&.Mui-active': {
      color: '#FF6B2C',
    },
  },
  '& .MuiStepIcon-root': {
    color: '#FFD1B8',
    '&.Mui-active': {
      color: '#FF6B2C',
    },
    '&.Mui-completed': {
      color: '#FF965E',
    },
  },
});

const ContentBox = styled(Box)({
  backgroundColor: '#FFFFFF',
  borderRadius: '8px',
  padding: '16px',
  marginTop: '16px',
  border: '1px solid #FFE4BA',
});

interface StepContent {
  label: string;
  title: string;
  description: string;
  details: string[];
  tips: string[];
}

const steps: StepContent[] = [
  {
    label: 'Choisissez une catégorie',
    title: 'Sélectionnez la bonne catégorie',
    description: 'La première étape est cruciale pour trouver le bon jobbeur.',
    details: [
      'Jardinage : Pour l\'entretien de votre jardin, plantation, tonte...',
      'Bricolage : Pour les petits travaux, montage de meubles, peinture...',
      'Garde d\'animaux : Pour la garde de vos animaux de compagnie',
    ],
    tips: [
      'Choisissez la catégorie qui correspond le mieux à votre besoin principal',
      'Si votre mission combine plusieurs catégories, sélectionnez celle qui représente la majorité du travail',
      'Les sous-catégories vous permettront d\'être plus précis ensuite',
    ],
  },
  {
    label: 'Décrivez votre mission',
    title: 'Détaillez votre besoin',
    description: 'Cette étape est essentielle pour attirer les meilleurs jobbeurs. Une description détaillée permet aux jobbeurs de comprendre exactement vos attentes et de vous faire des propositions adaptées.',
    details: [
      'Donnez un titre clair et accrocheur qui résume bien votre besoin',
      'Décrivez précisément les tâches à effectuer, leur ordre et leur importance',
      'Mentionnez le matériel nécessaire, ce que vous fournissez et ce que le jobbeur doit apporter',
      'Indiquez si des compétences ou qualifications particulières sont requises',
      'Précisez l\'état actuel (pour le jardinage par exemple : hauteur de la pelouse, surface à tondre)',
      'Ajoutez des photos si possible pour illustrer la situation'
    ],
    tips: [
      'Soyez le plus précis possible dans votre description pour éviter les malentendus',
      'N\'oubliez pas de mentionner les contraintes particulières (accès difficile, animaux présents...)',
      'Plus votre description est détaillée, plus les devis seront précis et adaptés',
      'Évitez les descriptions trop vagues comme "petit travail de jardinage"'
    ],
  },
  {
    label: 'Définissez la zone',
    title: 'Localisez votre mission',
    description: 'La localisation est cruciale pour trouver des jobbeurs disponibles dans votre secteur et optimiser les déplacements.',
    details: [
      'Utilisez la géolocalisation pour plus de précision et gagner du temps',
      'Vous pouvez rechercher une adresse manuellement si vous préférez',
      'Précisez s\'il y a des particularités d\'accès (parking, ascenseur...)'
    ],
    tips: [
      'Vérifiez que l\'adresse est bien exacte pour éviter tout problème le jour J',
      'Pensez à ajouter des indications d\'accès précises si nécessaire',
      'Les jobbeurs ne verront que votre ville pour protéger votre vie privée'
    ],
  },
  {
    label: 'Planifiez les horaires',
    title: 'Choisissez vos créneaux',
    description: 'Une bonne planification temporelle est essentielle pour le succès de votre mission. Plus vous êtes flexible, plus vous aurez de propositions.',
    details: [
      'Ajoutez plusieurs créneaux possibles pour maximiser vos chances',
      'Précisez la durée estimée de chaque tâche de la mission',
      'Indiquez si les horaires sont flexibles ou stricts',
      'Mentionnez si la mission est urgente ou peut être reportée',
      'Précisez si la mission peut être réalisée en plusieurs fois',
      'Indiquez vos préférences (matin, après-midi, soirée)'
    ],
    tips: [
      'Proposer plusieurs créneaux augmente significativement vos chances de trouver un jobbeur',
      'Pensez à inclure des créneaux en soirée ou le week-end pour plus de flexibilité',
      'Estimez la durée avec une marge raisonnable pour éviter les dépassements',
      'Tenez compte des conditions météo pour les missions en extérieur'
    ],
  },
  {
    label: 'Fixez votre budget',
    title: 'Définissez votre budget en Jobis',
    description: 'Le budget est un élément clé qui détermine la qualité des propositions que vous recevrez. Un budget juste et bien calculé attire les meilleurs jobbeurs.',
    details: [
      'Fixez un budget réaliste en fonction de la complexité de la mission',
      'Tenez compte de la durée estimée et des compétences requises',
      'Incluez les frais éventuels (matériel, déplacement, produits)',
      'Précisez si le budget est négociable ou fixe',
      'Considérez les tarifs moyens du marché pour ce type de service',
      'Pensez à la possibilité de missions régulières'
    ],
    tips: [
      'Consultez les prix moyens des missions similaires sur la plateforme',
      'Un budget trop bas risque de ne pas attirer de jobbeurs qualifiés',
      'Vous pouvez ajuster le budget selon les devis reçus et négocier',
      'Pensez à prévoir une marge pour les imprévus',
      'Les missions régulières peuvent bénéficier de tarifs préférentiels'
    ],
  },
];

interface PostMissionGuideProps {
  open: boolean;
  onClose: () => void;
}

const PostMissionGuide: React.FC<PostMissionGuideProps> = ({ open, onClose }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [dontShowAgain, setDontShowAgain] = useState(false);

  useEffect(() => {
    if (open) {
      setActiveStep(0);
    }
  }, [open]);

  const handleClose = () => {
    if (dontShowAgain) {
      setCookie(COOKIE_NAME, 'true', 365 * 24 * 60 * 60); // Cookie valable 1 an
    }
    onClose();
  };

  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  if (!open) return null;

  return (
    <ModalPortal onBackdropClick={handleClose}>
      <StyledModal onClick={e => e.stopPropagation()}>
        <StyledModalTitle>
          Guide de création de mission
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </StyledModalTitle>

        <Box sx={{ 
          flex: 1, 
          overflowY: 'auto', 
          padding: '0 16px'
        }}>
          <Stepper activeStep={activeStep} orientation="vertical" sx={{ mt: 2 }}>
            {steps.map((step, index) => (
              <Step key={step.label}>
                <StyledStepLabel>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                    {step.label}
                  </Typography>
                </StyledStepLabel>
                {activeStep === index && (
                  <ContentBox>
                    <Typography variant="h6" color="#FF6B2C" gutterBottom>
                      {step.title}
                    </Typography>
                    <Typography variant="body1" component="div" sx={{ mb: 2 }}>
                      {step.description}
                    </Typography>
                    
                    <Typography variant="subtitle2" color="#FF6B2C" gutterBottom>
                      Points clés :
                    </Typography>
                    <Box component="ul" sx={{ mb: 2 }}>
                      {step.details.map((detail, i) => (
                        <Typography component="li" key={i} sx={{ mb: 1 }}>
                          {detail}
                        </Typography>
                      ))}
                    </Box>

                    <Paper 
                      elevation={0} 
                      sx={{ 
                        backgroundColor: '#FFF8F3', 
                        p: 2, 
                        border: '1px solid #FFE4BA',
                        borderRadius: '8px'
                      }}
                    >
                      <Typography variant="subtitle2" color="#FF6B2C" gutterBottom>
                        Conseils :
                      </Typography>
                      <Box component="ul" sx={{ mb: 0 }}>
                        {step.tips.map((tip, i) => (
                          <Typography component="li" key={i} sx={{ mb: 1 }}>
                            {tip}
                          </Typography>
                        ))}
                      </Box>
                    </Paper>
                  </ContentBox>
                )}
              </Step>
            ))}
          </Stepper>
        </Box>

        <Box sx={{ 
          display: 'flex', 
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between', 
          alignItems: 'center',
          padding: '16px',
          borderTop: '1px solid #FFE4BA',
          backgroundColor: '#FFF8F3'
        }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={dontShowAgain}
                onChange={(e) => setDontShowAgain(e.target.checked)}
                sx={{
                  color: '#FF6B2C',
                  '&.Mui-checked': {
                    color: '#FF6B2C',
                  },
                }}
              />
            }
            label="Ne plus afficher ce guide"
          />
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              onClick={handleBack}
              disabled={activeStep === 0}
              sx={{ color: '#FF6B2C' }}
            >
              Précédent
            </Button>
            {activeStep === steps.length - 1 ? (
              <Button
                onClick={handleClose}
                variant="contained"
                sx={{
                  backgroundColor: '#FF6B2C',
                  '&:hover': {
                    backgroundColor: '#FF965E',
                  },
                }}
              >
                Terminer
              </Button>
            ) : (
              <Button
                onClick={handleNext}
                variant="contained"
                sx={{
                  backgroundColor: '#FF6B2C',
                  '&:hover': {
                    backgroundColor: '#FF965E',
                  },
                }}
              >
                Suivant
              </Button>
            )}
          </Box>
        </Box>
      </StyledModal>
    </ModalPortal>
  );
};

export default PostMissionGuide;
