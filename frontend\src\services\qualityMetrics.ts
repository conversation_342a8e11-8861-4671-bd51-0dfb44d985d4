import { API_CONFIG } from '../config/api';
import { api } from '../services/api';
import logger from '../utils/logger';

export interface QualityMetric {
  name: string;
  score: number;
  target: number;
  status: 'above' | 'below';
  count: number;
}

export interface Review {
  id: string;
  client: string;
  rating: number;
  comment: string;
  date: string;
  service: string;
  qualites: string[];
  defauts: string[];
}

export interface QualityMetricsResponse {
  success: boolean;
  metrics: QualityMetric[];
  reviews: Review[];
}

export const qualityMetricsService = {
  /**
   * Récupère les métriques de qualité de l'utilisateur connecté
   */
  async getQualityMetrics(): Promise<QualityMetricsResponse> {
    try {
      const response = await api.get<QualityMetricsResponse>(
        `${API_CONFIG.baseURL}/api/quality-metrics`
      );
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la récupération des métriques de qualité:', error);
      throw error;
    }
  },

  /**
   * Génère un rapport PDF pour les métriques de qualité
   * @param timeRange Plage de temps pour les statistiques (en jours)
   */
  async generateQualityReport(timeRange: string = '30'): Promise<void> {
    try {
      const response = await api.get(
        `${API_CONFIG.baseURL}/api/quality-metrics/report?timeRange=${timeRange}`,
        { responseType: 'blob' }
      );
      
      // Créer un URL pour le blob et télécharger le PDF
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      
      // Créer un lien de téléchargement temporaire
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `rapport-qualite-${new Date().toISOString().split('T')[0]}.pdf`);
      
      // Déclencher le téléchargement
      document.body.appendChild(link);
      link.click();
      
      // Nettoyer
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      logger.error('Erreur lors de la génération du rapport PDF:', error);
      throw error;
    }
  }
}; 