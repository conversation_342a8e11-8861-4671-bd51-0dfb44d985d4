import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Chip,
  LinearProgress,
  Avatar,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  Link,
  Tooltip,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  styled,
  useTheme,
  useMediaQuery,
  CircularProgress
} from '@mui/material';
import {
  WorkspacePremium as PremiumIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Schedule as ScheduleIcon,
  Euro as EuroIcon,
  CalendarToday as CalendarIcon,
  Autorenew as AutorenewIcon,
  Star as StarIcon,
  Diamond as DiamondIcon,
  Business as BusinessIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Pause as PauseIcon,
  PlayArrow as PlayArrowIcon,
  History as HistoryIcon,
  Payment as PaymentIcon,
  Settings as SettingsIcon,
  Launch as LaunchIcon,
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  CreditCard as CreditCardIcon,
  Receipt as ReceiptIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { getCommonHeaders } from '../../utils/headers';
import { fetchCsrfToken } from '../../services/csrf';
import { API_CONFIG } from '../../config/api';
import { motion } from 'framer-motion';

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C',
  secondary: '#FF7A35',
  tertiary: '#FF965E',
  background: '#FFF8F3',
  accent: '#FFE4BA',
  success: '#4CAF50',
  error: '#F44336',
  warning: '#FFC107',
  info: '#2196F3',
  neutral: '#64748B',
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

// Styles personnalisés
const StyledCard = styled(Card)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  height: '100%',
  padding: '14px',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const PageTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.75rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: theme.spacing(1),
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: COLORS.primary,
    borderRadius: '2px',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.5rem',
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.25rem',
  fontWeight: 600,
  marginBottom: theme.spacing(2),
  color: '#2D3748',
  position: 'relative',
  paddingLeft: theme.spacing(1.5),
  '&::before': {
    content: '""',
    position: 'absolute',
    left: 0,
    top: '50%',
    transform: 'translateY(-50%)',
    width: '4px',
    height: '24px',
    backgroundColor: COLORS.primary,
    borderRadius: '2px',
  },
}));

const StyledChip = styled(Chip)(({ theme }) => ({
  borderRadius: '8px',
  fontWeight: 500,
  fontSize: '0.75rem',
  height: '24px',
}));

const MetricCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-start',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: 'none',
  '& .MuiTable-root': {
    borderCollapse: 'separate',
    borderSpacing: '0 4px',
  },
  '& .MuiTableHead-root .MuiTableCell-root': {
    backgroundColor: COLORS.lightGray,
    fontWeight: 600,
    padding: theme.spacing(1.5),
    color: '#475569',
    border: 'none',
    fontSize: '0.875rem',
  },
  '& .MuiTableBody-root .MuiTableRow-root': {
    backgroundColor: COLORS.white,
    boxShadow: '0 1px 3px 0 rgba(0,0,0,0.05)',
    transition: 'background-color 0.2s',
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.04)',
    },
  },
  '& .MuiTableBody-root .MuiTableCell-root': {
    padding: theme.spacing(1.5),
    border: 'none',
    borderBottom: `1px solid ${COLORS.borderColor}`,
  }
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  boxShadow: 'none',
  fontWeight: 600,
  padding: '8px 16px',
  '&.MuiButton-contained': {
    backgroundColor: COLORS.primary,
    '&:hover': {
      backgroundColor: COLORS.secondary,
      boxShadow: '0 4px 10px rgba(255, 107, 44, 0.3)',
    },
  },
  '&.MuiButton-outlined': {
    borderColor: COLORS.borderColor,
    color: '#475569',
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.04)',
      borderColor: COLORS.primary,
    },
  },
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  textTransform: 'none',
  fontWeight: 600,
  fontSize: '0.9rem',
  minHeight: '48px',
  padding: '12px 16px',
  color: '#64748B',
  '&.Mui-selected': {
    color: COLORS.primary,
  },
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${COLORS.borderColor}`,
  '& .MuiTabs-indicator': {
    backgroundColor: COLORS.primary,
    height: '3px',
    borderRadius: '3px 3px 0 0',
  },
}));

const IconBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: COLORS.white,
  borderRadius: '50%',
  padding: theme.spacing(1),
  border: `2px solid ${COLORS.primary}`,
  boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
  color: COLORS.primary,
  position: 'absolute',
  top: '-15px',
  right: '20px',
}));

interface Subscription {
  id: string;
  type: 'gratuit' | 'basic' | 'premium' | 'pro';
  status: 'actif' | 'inactif' | 'en_attente' | 'suspendu';
  start_date: string;
  end_date: string;
  auto_renew: boolean;
  price: number;
  features: string[];
  created_at: string;
  cancelled_at?: string;
  cancellation_reason?: string;
  stripe_subscription_id?: string;
  stripe_customer_id?: string;
  payment_method_id?: string;
  last_payment_status?: string;
  last_payment_date?: string;
  options?: any;
}

interface UserSubscriptionManagementProps {
  userId: string;
  subscription?: Subscription;
  subscriptionHistory: Subscription[];
  onUpdate: () => void;
}

const UserSubscriptionManagement: React.FC<UserSubscriptionManagementProps> = ({
  userId,
  subscription,
  subscriptionHistory,
  onUpdate
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [actionDialogOpen, setActionDialogOpen] = useState(false);
  const [action, setAction] = useState('');
  const [newSubscriptionType, setNewSubscriptionType] = useState('');
  const [duration, setDuration] = useState(1);
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [subscriptionConfig, setSubscriptionConfig] = useState<any>(null);
  const [detailedSubscription, setDetailedSubscription] = useState<any>(null);
  const [paymentHistory, setPaymentHistory] = useState<any[]>([]);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  // Configuration des types d'abonnements avec les vraies données
  const subscriptionTypes = {
    gratuit: {
      name: 'Gratuit',
      price: 0,
      color: COLORS.neutral,
      icon: <StarIcon />,
      features: ['2 services', '1 galerie', '15km zone intervention', '2 conversations', '5 candidatures/mois']
    },
    basic: {
      name: 'Basic',
      price: 9.99,
      color: COLORS.info,
      icon: <StarIcon />,
      features: ['5 services', '2 galeries', '25km zone intervention', '10 conversations', '15 candidatures/mois']
    },
    premium: {
      name: 'Premium',
      price: 19.99,
      color: COLORS.warning,
      icon: <PremiumIcon />,
      features: ['6 services', '3 galeries', '30km zone intervention', '15 conversations', 'Candidatures illimitées', '5 crédits IA/mois']
    },
    pro: {
      name: 'Pro',
      price: 39.99,
      color: COLORS.primary,
      icon: <DiamondIcon />,
      features: ['Services illimités', 'Galeries illimitées', 'Zone France entière', 'Conversations illimitées', 'Candidatures illimitées', '10 crédits IA/mois']
    }
  };

  // Récupérer les détails complets de l'abonnement
  const fetchSubscriptionDetails = async () => {
    try {
      setLoading(true);
      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${userId}/subscription-details`,
        {
          method: 'GET',
          headers: headers,
          credentials: 'include'
        }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setDetailedSubscription(data.data.subscription);
          setSubscriptionConfig(data.data.config);
          setPaymentHistory(data.data.paymentHistory || []);
        }
      } else if (response.status === 404) {
        // L'API n'existe pas encore, on utilise les données de base
        // logger.info('API de détails d\'abonnement non disponible, utilisation des données de base');
        setDetailedSubscription(subscription);
        setSubscriptionConfig(null);
        setPaymentHistory([]);
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des détails:', error);
      // En cas d'erreur, on utilise les données de base
      setDetailedSubscription(subscription);
      setSubscriptionConfig(null);
      setPaymentHistory([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (userId) {
      fetchSubscriptionDetails();
    }
  }, [userId]);

  const handleSubscriptionAction = (actionType: string) => {
    setAction(actionType);
    setActionDialogOpen(true);
  };

  const executeAction = async () => {
    if (!action) return;

    // Pour le moment, toutes les actions d'abonnement sont désactivées
    setError('Cette fonctionnalité est en cours de développement et sera bientôt disponible.');
    
    // Fermer le dialog après 2 secondes
    setTimeout(() => {
      setActionDialogOpen(false);
      setAction('');
      setNewSubscriptionType('');
      setDuration(1);
      setReason('');
      setError(null);
    }, 2000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'actif':
        return 'success';
      case 'en_attente':
        return 'warning';
      case 'inactif':
      case 'suspendu':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'actif':
        return 'Actif';
      case 'en_attente':
        return 'En attente';
      case 'inactif':
        return 'Inactif';
      case 'suspendu':
        return 'Suspendu';
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Non disponible';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const getDaysRemaining = (endDate: string) => {
    if (!endDate) return 0;
    const end = new Date(endDate);
    const now = new Date();
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getActionLabel = () => {
    switch (action) {
      case 'create':
        return 'Créer un abonnement';
      case 'upgrade':
        return 'Mettre à niveau';
      case 'extend':
        return 'Prolonger l\'abonnement';
      case 'cancel':
        return 'Annuler l\'abonnement';
      case 'suspend':
        return 'Suspendre l\'abonnement';
      case 'reactivate':
        return 'Réactiver l\'abonnement';
      default:
        return 'Action';
    }
  };

  const renderSubscriptionOverview = () => (
    <motion.div
      variants={container}
      initial="hidden"
      animate="show"
    >
      <Grid container spacing={3}>
        {/* Statistiques rapides */}
        {subscription && (
          <Grid size={{ xs: 12 }}>
            <Grid container spacing={2}>
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <motion.div variants={item}>
                  <MetricCard>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ bgcolor: `${COLORS.primary}20`, color: COLORS.primary, mr: 1.5 }}>
                        <CalendarIcon />
                      </Avatar>
                      <Typography variant="subtitle2" color="text.secondary">
                        Jours restants
                      </Typography>
                    </Box>
                    <Typography variant="h4" fontWeight="bold" color="text.primary">
                      {getDaysRemaining(subscription.end_date)}
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={Math.max(0, Math.min(100, (getDaysRemaining(subscription.end_date) / 30) * 100))}
                      sx={{ 
                        mt: 1,
                        height: 6, 
                        borderRadius: 3,
                        backgroundColor: `${COLORS.primary}20`,
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: COLORS.primary
                        }
                      }}
                    />
                  </MetricCard>
                </motion.div>
              </Grid>
              
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <motion.div variants={item}>
                  <MetricCard>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ bgcolor: `${COLORS.info}20`, color: COLORS.info, mr: 1.5 }}>
                        <EuroIcon />
                      </Avatar>
                      <Typography variant="subtitle2" color="text.secondary">
                        Montant mensuel
                      </Typography>
                    </Box>
                    <Typography variant="h4" fontWeight="bold" color="text.primary">
                      {formatCurrency(subscription.price)}
                    </Typography>
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                      Prochain paiement: {subscription.auto_renew ? formatDate(subscription.end_date) : 'Pas de renouvellement'}
                    </Typography>
                  </MetricCard>
                </motion.div>
              </Grid>
              
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <motion.div variants={item}>
                  <MetricCard>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ bgcolor: `${subscriptionTypes[subscription.type].color}20`, color: subscriptionTypes[subscription.type].color, mr: 1.5 }}>
                        {subscriptionTypes[subscription.type].icon}
                      </Avatar>
                      <Typography variant="subtitle2" color="text.secondary">
                        Type d'abonnement
                      </Typography>
                    </Box>
                    <Typography variant="h4" fontWeight="bold" color="text.primary">
                      {subscriptionTypes[subscription.type].name}
                    </Typography>
                    <Box sx={{ mt: 1 }}>
                      <StyledChip
                        label={getStatusLabel(subscription.status)}
                        color={getStatusColor(subscription.status) as any}
                        size="small"
                      />
                    </Box>
                  </MetricCard>
                </motion.div>
              </Grid>
              
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <motion.div variants={item}>
                  <MetricCard>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ bgcolor: `${COLORS.success}20`, color: COLORS.success, mr: 1.5 }}>
                        <ScheduleIcon />
                      </Avatar>
                      <Typography variant="subtitle2" color="text.secondary">
                        Période d'abonnement
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                      <Typography variant="body2">
                        <strong>Début:</strong> {formatDate(subscription.start_date).split(' ')[0]}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Fin:</strong> {formatDate(subscription.end_date).split(' ')[0]}
                      </Typography>
                    </Box>
                    <Box sx={{ mt: 1 }}>
                      {subscription.auto_renew && (
                        <StyledChip
                          label="Renouvellement auto"
                          color="info"
                          size="small"
                          icon={<AutorenewIcon />}
                        />
                      )}
                    </Box>
                  </MetricCard>
                </motion.div>
              </Grid>
            </Grid>
          </Grid>
        )}

        {/* Abonnement actuel */}
        <Grid size={{ xs: 12, lg: 12 }}>
          <motion.div variants={item}>
            <StyledCard>
              <CardContent sx={{ p: 3 }}>
                <SectionTitle>Abonnement Actuel</SectionTitle>
                
                {subscription ? (
                  <Box>
                    {/* En-tête de l'abonnement */}
                    <Box sx={{ 
                      display: 'flex', 
                      flexDirection: isMobile ? 'column' : 'row',
                      alignItems: isMobile ? 'flex-start' : 'center', 
                      gap: 3, 
                      mb: 3,
                      p: 3,
                      borderRadius: '12px',
                      backgroundColor: `${subscriptionTypes[subscription.type].color}10`,
                      border: `1px solid ${subscriptionTypes[subscription.type].color}30`
                    }}>
                      <Avatar sx={{ 
                        bgcolor: subscriptionTypes[subscription.type].color, 
                        width: 64, 
                        height: 64 
                      }}>
                        {subscriptionTypes[subscription.type].icon}
                      </Avatar>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="h4" fontWeight="bold" sx={{ mb: 1 }}>
                          {subscriptionTypes[subscription.type].name}
                        </Typography>
                        <Typography variant="h5" color="primary" fontWeight="bold" sx={{ mb: 1 }}>
                          {formatCurrency(subscription.price)}/mois
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center', flexWrap: 'wrap' }}>
                          <StyledChip
                            label={getStatusLabel(subscription.status)}
                            color={getStatusColor(subscription.status) as any}
                            size="small"
                          />
                          {subscription.auto_renew && (
                            <StyledChip
                              label="Renouvellement auto"
                              color="info"
                              size="small"
                              icon={<AutorenewIcon />}
                            />
                          )}
                        </Box>
                      </Box>
                      
                      {/* Liens Stripe */}
                      {subscription.stripe_subscription_id && (
                        <Box sx={{ textAlign: isMobile ? 'left' : 'right', mt: isMobile ? 2 : 0 }}>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                            Liens Stripe
                          </Typography>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                            <Link
                              href={`https://dashboard.stripe.com/subscriptions/${subscription.stripe_subscription_id}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              sx={{ display: 'flex', alignItems: 'center', gap: 0.5, fontSize: '0.75rem' }}
                            >
                              <LaunchIcon sx={{ fontSize: 12 }} />
                              Abonnement
                            </Link>
                            {subscription.stripe_customer_id && (
                              <Link
                                href={`https://dashboard.stripe.com/customers/${subscription.stripe_customer_id}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                sx={{ display: 'flex', alignItems: 'center', gap: 0.5, fontSize: '0.75rem' }}
                              >
                                <LaunchIcon sx={{ fontSize: 12 }} />
                                Client
                              </Link>
                            )}
                          </Box>
                        </Box>
                      )}
                    </Box>

                    {/* Informations détaillées */}
                    <Grid container spacing={3} sx={{ mb: 3 }}>
                      <Grid size={{ xs: 12, md: 6 }}>
                        <Paper sx={{ p: 2.5, backgroundColor: COLORS.background, borderRadius: '12px' }}>
                          <Typography variant="subtitle2" gutterBottom sx={{ color: COLORS.primary, fontWeight: 600 }}>
                            Période d'abonnement
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            <CalendarIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                            <Typography variant="body2">
                              <strong>Début:</strong> {formatDate(subscription.start_date)}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            <CalendarIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                            <Typography variant="body2">
                              <strong>Fin:</strong> {formatDate(subscription.end_date)}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <ScheduleIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                            <Typography variant="body2">
                              <strong>Créé le:</strong> {formatDate(subscription.created_at)}
                            </Typography>
                          </Box>
                        </Paper>
                      </Grid>
                      
                      <Grid size={{ xs: 12, md: 6 }}>
                        <Paper sx={{ p: 2.5, backgroundColor: COLORS.background, borderRadius: '12px' }}>
                          <Typography variant="subtitle2" gutterBottom sx={{ color: COLORS.primary, fontWeight: 600 }}>
                            Informations de paiement
                          </Typography>
                          {subscription.last_payment_date && (
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                              <PaymentIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                              <Typography variant="body2">
                                <strong>Dernier paiement:</strong> {formatDate(subscription.last_payment_date)}
                              </Typography>
                            </Box>
                          )}
                          {subscription.last_payment_status && (
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                              <CreditCardIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                              <Typography variant="body2">
                                <strong>Statut paiement:</strong> {subscription.last_payment_status}
                              </Typography>
                            </Box>
                          )}
                          {subscription.payment_method_id && (
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <ReceiptIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                              <Typography variant="body2">
                                <strong>Méthode:</strong> {subscription.payment_method_id}
                              </Typography>
                            </Box>
                          )}
                        </Paper>
                      </Grid>
                    </Grid>

                    {/* Temps restant */}
                    {subscription.status === 'actif' && (
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="subtitle2" gutterBottom sx={{ color: COLORS.primary, fontWeight: 600 }}>
                          Temps restant
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Typography variant="body1" fontWeight="bold">
                            {getDaysRemaining(subscription.end_date)} jours
                          </Typography>
                          <LinearProgress
                            variant="determinate"
                            value={Math.max(0, Math.min(100, (getDaysRemaining(subscription.end_date) / 30) * 100))}
                            sx={{ 
                              flex: 1, 
                              height: 8, 
                              borderRadius: 4,
                              backgroundColor: `${COLORS.primary}20`,
                              '& .MuiLinearProgress-bar': {
                                backgroundColor: COLORS.primary
                              }
                            }}
                          />
                        </Box>
                      </Box>
                    )}

                    {/* Fonctionnalités incluses */}
                    <Typography variant="subtitle2" gutterBottom sx={{ color: COLORS.primary, fontWeight: 600 }}>
                      Fonctionnalités incluses
                    </Typography>
                    <Grid container spacing={1}>
                      {subscriptionTypes[subscription.type].features.map((feature, index) => (
                        <Grid size={{ xs: 12, sm: 6 }} key={index}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, py: 0.5 }}>
                            <CheckCircleIcon sx={{ fontSize: 16, color: COLORS.success }} />
                            <Typography variant="body2">{feature}</Typography>
                          </Box>
                        </Grid>
                      ))}
                    </Grid>

                    {/* Options personnalisées */}
                    {subscription.options && Object.keys(subscription.options).length > 0 && (
                      <Box sx={{ mt: 3 }}>
                        <Typography variant="subtitle2" gutterBottom sx={{ color: COLORS.primary, fontWeight: 600 }}>
                          Options personnalisées
                        </Typography>
                        <Paper sx={{ p: 2.5, backgroundColor: COLORS.background, borderRadius: '12px' }}>
                          <pre style={{ fontSize: '0.75rem', margin: 0, whiteSpace: 'pre-wrap' }}>
                            {JSON.stringify(subscription.options, null, 2)}
                          </pre>
                        </Paper>
                      </Box>
                    )}
                  </Box>
                ) : (
                  <Box sx={{ textAlign: 'center', py: 6 }}>
                    <PremiumIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                    <Typography variant="h6" color="text.secondary" gutterBottom>
                      Aucun abonnement actif
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      Cet utilisateur n'a pas d'abonnement en cours
                    </Typography>
                    <Tooltip title="Fonctionnalité en cours de développement">
                      <span>
                        <StyledButton
                          variant="contained"
                          startIcon={<AddIcon />}
                          disabled
                        >
                          Créer un abonnement
                        </StyledButton>
                      </span>
                    </Tooltip>
                  </Box>
                )}
              </CardContent>
              
              {subscription && (
                <CardActions sx={{ justifyContent: 'space-between', px: 3, pb: 3, flexWrap: 'wrap', gap: 1 }}>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Tooltip title="Fonctionnalité en cours de développement">
                      <span>
                        <StyledButton
                          variant="outlined"
                          startIcon={<EditIcon />}
                          disabled
                          size="small"
                        >
                          Modifier
                        </StyledButton>
                      </span>
                    </Tooltip>
                    <Tooltip title="Fonctionnalité en cours de développement">
                      <span>
                        <StyledButton
                          variant="outlined"
                          startIcon={<CalendarIcon />}
                          disabled
                          size="small"
                        >
                          Prolonger
                        </StyledButton>
                      </span>
                    </Tooltip>
                    <StyledButton
                      variant="outlined"
                      startIcon={<RefreshIcon />}
                      onClick={fetchSubscriptionDetails}
                      size="small"
                    >
                      Actualiser
                    </StyledButton>
                  </Box>
                  
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    {subscription.status === 'actif' && (
                      <>
                        <Tooltip title="Fonctionnalité en cours de développement">
                          <span>
                            <StyledButton
                              variant="outlined"
                              color="warning"
                              startIcon={<PauseIcon />}
                              disabled
                              size="small"
                            >
                              Suspendre
                            </StyledButton>
                          </span>
                        </Tooltip>
                        <Tooltip title="Fonctionnalité en cours de développement">
                          <span>
                            <StyledButton
                              variant="outlined"
                              color="error"
                              startIcon={<CancelIcon />}
                              disabled
                              size="small"
                            >
                              Annuler
                            </StyledButton>
                          </span>
                        </Tooltip>
                      </>
                    )}
                    
                    {subscription.status === 'suspendu' && (
                      <Tooltip title="Fonctionnalité en cours de développement">
                        <span>
                          <StyledButton
                            variant="contained"
                            color="success"
                            startIcon={<PlayArrowIcon />}
                            disabled
                            size="small"
                          >
                            Réactiver
                          </StyledButton>
                        </span>
                      </Tooltip>
                    )}
                  </Box>
                </CardActions>
              )}
            </StyledCard>
          </motion.div>
        </Grid>

        
      </Grid>
    </motion.div>
  );

  const renderSubscriptionHistory = () => (
    <motion.div
      variants={container}
      initial="hidden"
      animate="show"
    >
      <motion.div variants={item}>
        <StyledCard>
          <CardContent sx={{ p: 3 }}>
            <SectionTitle>Historique des Abonnements</SectionTitle>
            
            {subscriptionHistory && subscriptionHistory.length > 0 ? (
              <StyledTableContainer sx={{ mt: 2, boxShadow: 'none' }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Type</TableCell>
                      <TableCell>Statut</TableCell>
                      <TableCell>Début</TableCell>
                      <TableCell>Fin</TableCell>
                      <TableCell align="right">Prix</TableCell>
                      <TableCell align="center">Auto-renouv.</TableCell>
                      <TableCell>Stripe</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {subscriptionHistory.map((sub) => (
                      <TableRow key={sub.id}>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Avatar sx={{ 
                              bgcolor: subscriptionTypes[sub.type]?.color || COLORS.neutral, 
                              width: 28, 
                              height: 28 
                            }}>
                              {subscriptionTypes[sub.type]?.icon || <StarIcon />}
                            </Avatar>
                            <Typography variant="body2">
                              {subscriptionTypes[sub.type]?.name || sub.type}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <StyledChip
                            label={getStatusLabel(sub.status)}
                            color={getStatusColor(sub.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{formatDate(sub.start_date).split(' ')[0]}</TableCell>
                        <TableCell>{formatDate(sub.end_date).split(' ')[0]}</TableCell>
                        <TableCell align="right">{formatCurrency(sub.price)}</TableCell>
                        <TableCell align="center">
                          {sub.auto_renew ? (
                            <CheckCircleIcon sx={{ color: COLORS.success, fontSize: 18 }} />
                          ) : (
                            <CancelIcon sx={{ color: COLORS.error, fontSize: 18 }} />
                          )}
                        </TableCell>
                        <TableCell>
                          {sub.stripe_subscription_id && (
                            <Link
                              href={`https://dashboard.stripe.com/subscriptions/${sub.stripe_subscription_id}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
                            >
                              <LaunchIcon sx={{ fontSize: 14 }} />
                              Voir
                            </Link>
                          )}
                        </TableCell>
                        <TableCell>
                          <Tooltip title="Voir les détails">
                            <IconButton size="small">
                              <InfoIcon sx={{ fontSize: 18 }} />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </StyledTableContainer>
            ) : (
              <Box sx={{ textAlign: 'center', py: 4, backgroundColor: COLORS.background, borderRadius: '12px', mt: 2 }}>
                <HistoryIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary">
                  Aucun historique d'abonnement
                </Typography>
              </Box>
            )}
          </CardContent>
        </StyledCard>
      </motion.div>
    </motion.div>
  );

  const renderPaymentHistory = () => (
    <motion.div
      variants={container}
      initial="hidden"
      animate="show"
    >
      <motion.div variants={item}>
        <StyledCard>
          <CardContent sx={{ p: 3 }}>
            <SectionTitle>Historique des Paiements</SectionTitle>
            
            {paymentHistory && paymentHistory.length > 0 ? (
              <StyledTableContainer sx={{ mt: 2, boxShadow: 'none' }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Date</TableCell>
                      <TableCell>Montant</TableCell>
                      <TableCell>Statut</TableCell>
                      <TableCell>Méthode</TableCell>
                      <TableCell>Stripe</TableCell>
                      <TableCell>Description</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {paymentHistory.map((payment, index) => (
                      <TableRow key={index}>
                        <TableCell>{formatDate(payment.date)}</TableCell>
                        <TableCell>{formatCurrency(payment.amount)}</TableCell>
                        <TableCell>
                          <StyledChip
                            label={payment.status}
                            color={payment.status === 'succeeded' ? 'success' : 
                                  payment.status === 'pending' ? 'warning' : 'error'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{payment.method}</TableCell>
                        <TableCell>
                          {payment.stripe_payment_id && (
                            <Link
                              href={`https://dashboard.stripe.com/payments/${payment.stripe_payment_id}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
                            >
                              <LaunchIcon sx={{ fontSize: 14 }} />
                              Voir
                            </Link>
                          )}
                        </TableCell>
                        <TableCell>{payment.description}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </StyledTableContainer>
            ) : (
              <Box sx={{ textAlign: 'center', py: 4, backgroundColor: COLORS.background, borderRadius: '12px', mt: 2 }}>
                <PaymentIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary">
                  Aucun historique de paiement
                </Typography>
              </Box>
            )}
          </CardContent>
        </StyledCard>
      </motion.div>
    </motion.div>
  );

  const renderConfigurationDetails = () => (
    <motion.div
      variants={container}
      initial="hidden"
      animate="show"
    >
      <motion.div variants={item}>
        <StyledCard>
          <CardContent sx={{ p: 3 }}>
            <SectionTitle>Configuration Détaillée</SectionTitle>
            
            {subscriptionConfig ? (
              <Box>
                {Object.entries(subscriptionConfig).map(([planKey, planConfig]: [string, any]) => (
                  <Accordion 
                    key={planKey} 
                    sx={{ 
                      mb: 2, 
                      borderRadius: '12px', 
                      overflow: 'hidden',
                      boxShadow: 'none',
                      border: `1px solid ${COLORS.borderColor}`,
                      '&:before': {
                        display: 'none',
                      },
                    }}
                  >
                    <AccordionSummary 
                      expandIcon={<ExpandMoreIcon />}
                      sx={{ 
                        backgroundColor: subscription?.type === planKey ? `${subscriptionTypes[planKey as keyof typeof subscriptionTypes]?.color}10` : COLORS.lightGray,
                        '&.Mui-expanded': {
                          minHeight: '48px',
                        },
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ 
                          bgcolor: subscriptionTypes[planKey as keyof typeof subscriptionTypes]?.color || COLORS.neutral, 
                          width: 32, 
                          height: 32 
                        }}>
                          {subscriptionTypes[planKey as keyof typeof subscriptionTypes]?.icon || <StarIcon />}
                        </Avatar>
                        <Typography variant="h6">
                          Plan {subscriptionTypes[planKey as keyof typeof subscriptionTypes]?.name || planKey}
                        </Typography>
                        <Typography variant="body2" color="primary" fontWeight="bold">
                          {formatCurrency(planConfig.prixDeBase || 0)}/mois
                        </Typography>
                      </Box>
                    </AccordionSummary>
                    <AccordionDetails sx={{ p: 3 }}>
                      <Grid container spacing={2}>
                        {Object.entries(planConfig).map(([featureKey, featureConfig]: [string, any]) => {
                          if (featureKey === 'prixDeBase') return null;
                          
                          return (
                            <Grid size={{ xs: 12, md: 6 }} key={featureKey}>
                              <Paper sx={{ p: 2.5, backgroundColor: COLORS.background, borderRadius: '12px' }}>
                                <Typography variant="subtitle2" gutterBottom sx={{ color: COLORS.primary }}>
                                  {featureKey.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                                </Typography>
                                <Typography variant="body2" sx={{ mb: 1 }}>
                                  <strong>Inclus:</strong> {featureConfig.included}
                                </Typography>
                                {featureConfig.additionalCost > 0 && (
                                  <Typography variant="body2" sx={{ mb: 1 }}>
                                    <strong>Coût supplémentaire:</strong> {formatCurrency(featureConfig.additionalCost)}
                                  </Typography>
                                )}
                                <Typography variant="caption" color="text.secondary">
                                  {featureConfig.description}
                                </Typography>
                              </Paper>
                            </Grid>
                          );
                        })}
                      </Grid>
                    </AccordionDetails>
                  </Accordion>
                ))}
              </Box>
            ) : (
              <Box sx={{ textAlign: 'center', py: 4, backgroundColor: COLORS.background, borderRadius: '12px', mt: 2 }}>
                <SettingsIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary">
                  Configuration non disponible
                </Typography>
              </Box>
            )}
          </CardContent>
        </StyledCard>
      </motion.div>
    </motion.div>
  );

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3, flexWrap: 'wrap', gap: 2 }}>
        <Box>
          <PageTitle variant="h4" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <PremiumIcon sx={{ color: COLORS.primary }} />
            Gestion des Abonnements
          </PageTitle>
          <Typography variant="body1" color="text.secondary" sx={{ mt: 2 }}>
            Gérez les abonnements, consultez l'historique et modifiez les configurations
          </Typography>
        </Box>
        
        {subscription && (
          <StyledButton
            variant="contained"
            startIcon={<RefreshIcon />}
            onClick={fetchSubscriptionDetails}
            disabled={loading}
          >
            Actualiser
          </StyledButton>
        )}
      </Box>

      {/* Message d'information sur les fonctionnalités en développement */}
      <Alert 
        severity="info" 
        sx={{ 
          mb: 3, 
          borderRadius: '12px',
          border: `1px solid ${COLORS.info}20`
        }}
      >
        <Typography variant="body2">
          <strong>Information :</strong> Les fonctionnalités de modification, prolongation, suspension et annulation des abonnements sont actuellement en cours de développement. Seule la consultation des informations est disponible.
        </Typography>
      </Alert>

      {error && (
        <Alert 
          severity="error" 
          sx={{ 
            mb: 3, 
            borderRadius: '12px',
            border: `1px solid ${COLORS.error}20`
          }}
        >
          {error}
        </Alert>
      )}

      {loading && !subscription && !subscriptionHistory ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', my: 8, height: '200px' }}>
          <CircularProgress sx={{ color: COLORS.primary }} />
        </Box>
      ) : (
        <>
          {/* Onglets */}
          <Box sx={{ mb: 3 }}>
            <StyledTabs 
              value={activeTab} 
              onChange={(e, newValue) => setActiveTab(newValue)}
              variant="scrollable"
              scrollButtons="auto"
            >
              <StyledTab label="Vue d'ensemble" icon={<PremiumIcon />} iconPosition="start" />
              <StyledTab label="Historique" icon={<HistoryIcon />} iconPosition="start" />
              <StyledTab label="Paiements" icon={<PaymentIcon />} iconPosition="start" />
              <StyledTab label="Configuration" icon={<SettingsIcon />} iconPosition="start" />
            </StyledTabs>
          </Box>

          {/* Contenu des onglets */}
          {activeTab === 0 && renderSubscriptionOverview()}
          {activeTab === 1 && renderSubscriptionHistory()}
          {activeTab === 2 && renderPaymentHistory()}
          {activeTab === 3 && renderConfigurationDetails()}
        </>
      )}

      {/* Dialog d'action */}
      <Dialog 
        open={actionDialogOpen} 
        onClose={() => setActionDialogOpen(false)} 
        maxWidth="sm" 
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '16px',
            boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
          }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Typography variant="h5" fontWeight="bold">
            {getActionLabel()}
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 2 }}>
          {(action === 'create' || action === 'upgrade') && (
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Type d'abonnement</InputLabel>
              <Select
                value={newSubscriptionType}
                label="Type d'abonnement"
                onChange={(e) => setNewSubscriptionType(e.target.value)}
              >
                {Object.entries(subscriptionTypes).map(([key, plan]) => (
                  <MenuItem key={key} value={key}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Avatar sx={{ bgcolor: plan.color, width: 24, height: 24 }}>
                        {plan.icon}
                      </Avatar>
                      <Typography>
                        {plan.name} - {formatCurrency(plan.price)}/mois
                      </Typography>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}

          {(action === 'create' || action === 'extend') && (
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Durée (mois)</InputLabel>
              <Select
                value={duration}
                label="Durée (mois)"
                onChange={(e) => setDuration(Number(e.target.value))}
              >
                <MenuItem value={1}>1 mois</MenuItem>
                <MenuItem value={3}>3 mois</MenuItem>
                <MenuItem value={6}>6 mois</MenuItem>
                <MenuItem value={12}>12 mois</MenuItem>
              </Select>
            </FormControl>
          )}
          
          <TextField
            fullWidth
            label="Motif (optionnel)"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            multiline
            rows={3}
            placeholder="Expliquez la raison de cette action..."
            sx={{ 
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
              }
            }}
          />
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <StyledButton onClick={() => setActionDialogOpen(false)}>
            Annuler
          </StyledButton>
          <StyledButton
            onClick={executeAction}
            variant="contained"
            disabled={loading || ((action === 'create' || action === 'upgrade') && !newSubscriptionType)}
            startIcon={loading && <CircularProgress size={16} color="inherit" />}
          >
            {loading ? 'En cours...' : 'Confirmer'}
          </StyledButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserSubscriptionManagement;
