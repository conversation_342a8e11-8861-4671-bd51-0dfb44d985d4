import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { logUserActivity, getIpFromRequest } from '../utils/activityLogger';
import { addMonths, isBefore } from 'date-fns';

const REDIS_CONSENT_PREFIX = 'ai_consent:';
const CONSENT_EXPIRY_MONTHS = 6; // Durée de validité du consentement en mois

/**
 * Enregistre le consentement d'un utilisateur pour l'utilisation de l'IA
 */
export const saveAiConsent = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    const userId = req.user.userId;
    const { firstName, lastName } = req.body;

    // Validation des données
    if (!firstName || !lastName) {
      return res.status(400).json({
        success: false,
        message: 'Le prénom et le nom sont requis'
      });
    }

    // Sanitize les entrées
    const sanitizedFirstName = firstName;
    const sanitizedLastName = lastName;

    // Vérifier si un consentement existe déjà
    const { data: existingConsent, error: checkError } = await supabase
      .from('ai_user_consents')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = not found
      logger.error('Erreur lors de la vérification du consentement IA:', checkError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification du consentement'
      });
    }

    let result;
    const now = new Date();
    const timestamp = now.toISOString();
    const expiryDate = addMonths(now, CONSENT_EXPIRY_MONTHS).toISOString();

    if (existingConsent) {
      // Mettre à jour le consentement existant
      const { data, error } = await supabase
        .from('ai_user_consents')
        .update({
          first_name: sanitizedFirstName,
          last_name: sanitizedLastName,
          updated_at: timestamp,
          expiry_date: expiryDate,
          is_active: true, // S'assurer que le consentement est actif
          deactivated_at: null, // Réinitialiser la date de désactivation
          deactivation_reason: null // Réinitialiser la raison de désactivation
        })
        .eq('id', existingConsent.id)
        .select();

      if (error) {
        logger.error('Erreur lors de la mise à jour du consentement IA:', error);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la mise à jour du consentement'
        });
      }

      result = data;
    } else {
      // Créer un nouveau consentement
      const { data, error } = await supabase
        .from('ai_user_consents')
        .insert({
          user_id: userId,
          first_name: sanitizedFirstName,
          last_name: sanitizedLastName,
          created_at: timestamp,
          updated_at: timestamp,
          expiry_date: expiryDate,
          is_active: true, // Le consentement est actif par défaut
          ip_address: getIpFromRequest(req)
        })
        .select();

      if (error) {
        logger.error('Erreur lors de l\'enregistrement du consentement IA:', error);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de l\'enregistrement du consentement'
        });
      }

      result = data;
    }

    // Mettre en cache le consentement dans Redis
    await redis.set(`${REDIS_CONSENT_PREFIX}${userId}`, 'true', 'EX', 86400 * 365); // 1 an

    // Journaliser l'activité
    await logUserActivity(
      userId,
      'ai_consent',
      undefined, // resourceId
      undefined, // resourceType
      {
        firstName: sanitizedFirstName,
        lastName: sanitizedLastName
      },
      getIpFromRequest(req)
    );

    return res.status(200).json({
      success: true,
      message: 'Consentement enregistré avec succès',
      data: result
    });
  } catch (error) {
    logger.error('Erreur lors de l\'enregistrement du consentement IA:', error);
    return res.status(500).json({
      success: false,
      message: 'Une erreur est survenue lors de l\'enregistrement du consentement'
    });
  }
};

/**
 * Vérifie si un utilisateur a donné son consentement pour l'utilisation de l'IA
 */
export const checkAiConsent = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    const userId = req.user.userId;

    // Vérifier d'abord dans Redis pour des performances optimales
    const cachedConsent = await redis.get(`${REDIS_CONSENT_PREFIX}${userId}`);

    if (cachedConsent === 'true') {
      return res.status(200).json({
        success: true,
        hasConsent: true
      });
    }

    // Si pas en cache, vérifier en base de données
    const { data, error } = await supabase
      .from('ai_user_consents')
      .select('id, created_at, expiry_date, is_active')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = not found
      logger.error('Erreur lors de la vérification du consentement IA:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification du consentement'
      });
    }

    // Vérifier si le consentement existe, est actif et n'est pas expiré
    let hasConsent = false;
    if (data) {
      // Vérifier d'abord si le consentement est actif
      if (data.is_active === false) {
        // Le consentement a été désactivé, on le considère comme non valide
        await redis.del(`${REDIS_CONSENT_PREFIX}${userId}`);
      } else {
        const now = new Date();
        const expiryDate = data.expiry_date ? new Date(data.expiry_date) : null;

        // Si la date d'expiration est définie et n'est pas dépassée
        if (expiryDate && isBefore(now, expiryDate)) {
          hasConsent = true;
        } else if (!expiryDate) {
          // Pour les anciens consentements sans date d'expiration, on les considère valides
          // mais on met à jour leur date d'expiration
          const newExpiryDate = addMonths(now, CONSENT_EXPIRY_MONTHS).toISOString();
          await supabase
            .from('ai_user_consents')
            .update({
              expiry_date: newExpiryDate,
              is_active: true // S'assurer que le consentement est marqué comme actif
            })
            .eq('id', data.id);

          hasConsent = true;
        } else {
          // Le consentement est expiré, on le marque comme inactif et on le supprime du cache Redis
          await supabase
            .from('ai_user_consents')
            .update({
              is_active: false,
              deactivated_at: now.toISOString(),
              deactivation_reason: 'Expiration automatique après 6 mois'
            })
            .eq('id', data.id);

          await redis.del(`${REDIS_CONSENT_PREFIX}${userId}`);
        }
      }
    }

    // Si consentement trouvé, mettre en cache
    if (hasConsent) {
      await redis.set(`${REDIS_CONSENT_PREFIX}${userId}`, 'true', 'EX', 86400 * 365); // 1 an
    }

    return res.status(200).json({
      success: true,
      hasConsent
    });
  } catch (error) {
    logger.error('Erreur lors de la vérification du consentement IA:', error);
    return res.status(500).json({
      success: false,
      message: 'Une erreur est survenue lors de la vérification du consentement'
    });
  }
};

/**
 * Vérifie si un utilisateur a donné son consentement (fonction utilitaire pour d'autres contrôleurs)
 */
export const hasUserConsented = async (userId: string): Promise<boolean> => {
  try {
    // Vérifier d'abord dans Redis
    const cachedConsent = await redis.get(`${REDIS_CONSENT_PREFIX}${userId}`);

    if (cachedConsent === 'true') {
      return true;
    }

    // Si pas en cache, vérifier en base de données
    const { data, error } = await supabase
      .from('ai_user_consents')
      .select('id, expiry_date, is_active')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = not found
      logger.error('Erreur lors de la vérification du consentement IA (utilitaire):', error);
      return false;
    }

    // Vérifier si le consentement existe, est actif et n'est pas expiré
    let hasConsent = false;
    if (data) {
      // Vérifier d'abord si le consentement est actif
      if (data.is_active === false) {
        // Le consentement a été désactivé, on le considère comme non valide
        await redis.del(`${REDIS_CONSENT_PREFIX}${userId}`);
      } else {
        const now = new Date();
        const expiryDate = data.expiry_date ? new Date(data.expiry_date) : null;

        // Si la date d'expiration est définie et n'est pas dépassée
        if (expiryDate && isBefore(now, expiryDate)) {
          hasConsent = true;
        } else if (!expiryDate) {
          // Pour les anciens consentements sans date d'expiration, on les considère valides
          // mais on met à jour leur date d'expiration
          const newExpiryDate = addMonths(now, CONSENT_EXPIRY_MONTHS).toISOString();
          await supabase
            .from('ai_user_consents')
            .update({
              expiry_date: newExpiryDate,
              is_active: true // S'assurer que le consentement est marqué comme actif
            })
            .eq('id', data.id);

          hasConsent = true;
        } else {
          // Le consentement est expiré, on le marque comme inactif et on le supprime du cache Redis
          await supabase
            .from('ai_user_consents')
            .update({
              is_active: false,
              deactivated_at: now.toISOString(),
              deactivation_reason: 'Expiration automatique après 6 mois'
            })
            .eq('id', data.id);

          await redis.del(`${REDIS_CONSENT_PREFIX}${userId}`);
        }
      }
    }

    // Si consentement trouvé, mettre en cache
    if (hasConsent) {
      await redis.set(`${REDIS_CONSENT_PREFIX}${userId}`, 'true', 'EX', 86400 * 365); // 1 an
    }

    return hasConsent;
  } catch (error) {
    logger.error('Erreur lors de la vérification du consentement IA (utilitaire):', error);
    return false;
  }
};

/**
 * Récupère les détails du consentement IA d'un utilisateur
 */
export const getAiConsentDetails = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    const userId = req.user.userId;

    // Vérifier d'abord si l'utilisateur a donné son consentement
    const hasConsent = await hasUserConsented(userId);

    // Récupérer les détails du consentement
    const { data, error } = await supabase
      .from('ai_user_consents')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      logger.error('Erreur lors de la récupération des détails du consentement IA:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des détails du consentement'
      });
    }

    if (!data) {
      return res.status(200).json({
        success: true,
        hasConsent: false
      });
    }

    return res.status(200).json({
      success: true,
      hasConsent,
      firstName: data.first_name,
      lastName: data.last_name,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      expiryDate: data.expiry_date,
      isActive: data.is_active,
      deactivatedAt: data.deactivated_at,
      deactivationReason: data.deactivation_reason
    });
  } catch (error) {
    logger.error('Erreur lors de la récupération des détails du consentement IA:', error);
    return res.status(500).json({
      success: false,
      message: 'Une erreur est survenue lors de la récupération des détails du consentement'
    });
  }
};

/**
 * Marque les consentements expirés comme inactifs dans la base de données
 * Cette fonction est destinée à être exécutée périodiquement par une tâche planifiée
 * Les consentements ne sont pas supprimés pour des raisons légales et de traçabilité
 */
export const cleanupExpiredConsents = async (): Promise<void> => {
  try {
    const now = new Date().toISOString();

    // Récupérer les consentements expirés qui sont encore actifs
    const { data, error } = await supabase
      .from('ai_user_consents')
      .select('id, user_id')
      .lt('expiry_date', now)
      .eq('is_active', true);

    if (error) {
      logger.error('Erreur lors de la récupération des consentements expirés:', error);
      return;
    }

    if (!data || data.length === 0) {
      logger.info('Aucun consentement expiré à désactiver');
      return;
    }

    logger.info(`${data.length} consentements expirés trouvés, désactivation en cours...`);

    // Supprimer les consentements expirés de Redis (pour empêcher leur utilisation)
    for (const consent of data) {
      await redis.del(`${REDIS_CONSENT_PREFIX}${consent.user_id}`);
    }

    // Marquer les consentements comme inactifs dans la base de données
    const ids = data.map(consent => consent.id);
    const { error: updateError } = await supabase
      .from('ai_user_consents')
      .update({
        is_active: false,
        deactivated_at: now,
        deactivation_reason: 'Expiration automatique après 6 mois'
      })
      .in('id', ids);

    if (updateError) {
      logger.error('Erreur lors de la désactivation des consentements expirés:', updateError);
      return;
    }

    logger.info(`${data.length} consentements expirés ont été désactivés avec succès`);
  } catch (error) {
    logger.error('Erreur lors de la désactivation des consentements expirés:', error);
  }
};

/**
 * Permet à l'utilisateur de supprimer (désactiver) son consentement IA
 */
export const deleteAiConsent = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    const userId = req.user.userId;
    const now = new Date().toISOString();

    // Vérifier si un consentement existe et est actif
    const { data: existingConsent, error } = await supabase
      .from('ai_user_consents')
      .select('id, is_active')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      logger.error('Erreur lors de la recherche du consentement IA à supprimer:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la recherche du consentement à supprimer'
      });
    }

    if (!existingConsent || existingConsent.is_active === false) {
      return res.status(404).json({
        success: false,
        message: 'Aucun consentement actif à supprimer.'
      });
    }

    // Désactiver le consentement
    const { error: updateError } = await supabase
      .from('ai_user_consents')
      .update({
        is_active: false,
        deactivated_at: now,
        deactivation_reason: 'Suppression par l\'utilisateur'
      })
      .eq('id', existingConsent.id);

    if (updateError) {
      logger.error('Erreur lors de la désactivation du consentement IA:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la suppression du consentement'
      });
    }

    // Supprimer du cache Redis
    await redis.del(`${REDIS_CONSENT_PREFIX}${userId}`);

    // Journaliser l'activité
    await logUserActivity(
      userId,
      'ai_consent_deleted',
      undefined,
      undefined,
      {},
      getIpFromRequest(req)
    );

    return res.status(200).json({
      success: true,
      message: 'Consentement supprimé avec succès.'
    });
  } catch (error) {
    logger.error('Erreur lors de la suppression du consentement IA:', error);
    return res.status(500).json({
      success: false,
      message: 'Une erreur est survenue lors de la suppression du consentement.'
    });
  }
};
