import React, { useState, useEffect } from 'react';
import { TextField, Button, IconButton, Tooltip } from '@mui/material';
import { MessageCircle, X, Sparkles, HelpCircle } from 'lucide-react';
import ModalPortal from './ModalPortal';
import { notify } from './Notification';
import DOMPurify from 'dompurify';
import { motion } from 'framer-motion';
import axios from 'axios';
import { API_CONFIG } from '@/config/api';
import { getCommonHeaders } from '@/utils/headers';
import AiGenerationSystem from './ai/AiGenerationSystem';

interface ReviewReplyModalProps {
  isOpen: boolean;
  onClose: () => void;
  reviewId: string;
  existingReply?: string;
  onReplySubmitted: () => void;
  reviewData?: {
    note?: number;
    commentaire?: string;
    qualites?: string[];
    defauts?: string[];
    mission_titre?: string;
  };
}

const ReviewReplyModal: React.FC<ReviewReplyModalProps> = ({
  isOpen,
  onClose,
  reviewId,
  existingReply,
  onReplySubmitted,
  reviewData
}) => {
  const [reply, setReply] = useState(existingReply || '');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAiConfirmModalOpen, setIsAiConfirmModalOpen] = useState(false);

  // Mettre à jour la réponse si existingReply change
  useEffect(() => {
    setReply(existingReply ? decodeHtmlEntities(existingReply) : '');
  }, [existingReply]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      const sanitizedReply = DOMPurify.sanitize(reply);

      let headers = await getCommonHeaders();
      headers = { ...headers, 'Content-Type': 'application/json' };
      const response = await axios({
        method: existingReply ? 'put' : 'post',
        url: `${API_CONFIG.baseURL}/api/reviews/${reviewId}/reply`,
        data: { reponse: sanitizedReply },
        headers,
        withCredentials: true
      });

      if (response.data.success) {
        notify(existingReply ? 'Votre réponse a été modifiée avec succès' : 'Votre réponse a été enregistrée avec succès', 'success');
        onReplySubmitted();
        onClose();
      }
    } catch (error: any) {
      setError(error.response?.data?.message || 'Une erreur est survenue lors de l\'envoi de la réponse');
      notify(error.response?.data?.message || 'Une erreur est survenue lors de l\'envoi de la réponse', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Fonction pour extraire le texte brut (sans HTML)
  const stripHtml = (html: string) => {
    if (!html) return '';
    const tmp = document.createElement('div');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
  };

  // Fonction pour décoder les entités HTML
  const decodeHtmlEntities = (text: string) => {
    if (!text) return '';
    const textarea = document.createElement('textarea');
    textarea.innerHTML = text;
    return textarea.value;
  };

  return (
    <>
      <ModalPortal isOpen={isOpen} onBackdropClick={onClose}>
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="bg-white rounded-2xl shadow-[0_8px_30px_rgb(0,0,0,0.12)] w-full max-w-[600px] relative max-h-[90vh] overflow-y-auto"
        >
          <div className="flex items-center justify-between p-8 pb-4 border-b border-gray-100 sticky top-0 bg-white z-10">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-gradient-to-br from-[#FFF8F3] to-[#FFE4BA] rounded-xl shadow-sm">
                <MessageCircle className="h-7 w-7 text-[#FF6B2C]" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-800">
                  {existingReply ? 'Modifier la réponse' : 'Répondre à l\'avis'}
                </h2>
                <p className="text-sm text-gray-500 mt-1">
                  {existingReply ? 'Modifiez votre réponse à cet avis' : 'Répondez à cet avis de manière constructive'}
                </p>
              </div>
            </div>
            <IconButton
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all duration-200 rounded-full"
            >
              <X size={24} />
            </IconButton>
          </div>

          <form onSubmit={handleSubmit} className="p-8">
            <TextField
              multiline
              minRows={4}
              maxRows={10}
              fullWidth
              placeholder="Votre réponse..."
              value={reply}
              onChange={(e) => {
                if (e.target.value.length <= 700) {
                  setReply(e.target.value);
                }
              }}
              error={!!error}
              helperText={error || `${reply.length}/700 caractères`}
              inputProps={{
                maxLength: 700
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: '12px',
                  backgroundColor: '#F8FAFC',
                  '&:hover fieldset': {
                    borderColor: '#FFE4BA',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#FF6B2C',
                  },
                },
                '& .MuiFormHelperText-root': {
                  textAlign: 'right',
                  marginTop: '8px',
                  color: reply.length >= 700 ? '#FF6B2C' : 'inherit'
                }
              }}
            />

            <div className="flex flex-col sm:flex-row justify-between mt-6 gap-3">
              <Button
                onClick={() => setIsAiConfirmModalOpen(true)}
                disabled={isSubmitting}
                className="flex items-center gap-2 px-4 py-2 bg-[#FFF8F3] text-[#FF6B2C] rounded-lg hover:bg-[#FFE4BA] transition-colors"
                startIcon={<Sparkles className="h-4 w-4" />}
                sx={{
                  backgroundColor: '#FFF8F3',
                  color: '#FF6B2C',
                  '&:hover': {
                    backgroundColor: '#FFE4BA',
                  },
                  '&.Mui-disabled': {
                    color: 'rgba(255, 107, 44, 0.5)',
                    backgroundColor: 'rgba(255, 248, 243, 0.5)',
                  }
                }}
              >
                {reply.length > 0 ? "Améliorer avec IA" : "Générer avec IA"}
                <Tooltip
                  title={reply.length > 0
                    ? "Améliorer le contenu existant avec l'IA"
                    : "Générer du nouveau contenu avec l'IA"}
                  sx={{ ml: 1 }}
                >
                  <HelpCircle className="h-4 w-4 ml-1" />
                </Tooltip>
              </Button>

              <div className="flex justify-end gap-3">
                <Button
                  onClick={onClose}
                  variant="outlined"
                  sx={{
                    borderColor: '#E2E8F0',
                    color: '#FF6B2C',
                    '&:hover': {
                      borderColor: '#FF6B2C',
                      backgroundColor: '#FFF8F3',
                    },
                  }}
                >
                  Annuler
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={isSubmitting || !reply.trim()}
                  sx={{
                    background: 'linear-gradient(to right, #FF6B2C, #FF7A35)',
                    color: 'white',
                    boxShadow: '0 4px 14px 0 rgba(255, 107, 44, 0.39)',
                    '&:hover': {
                      background: 'linear-gradient(to right, #FF7A35, #FF965E)',
                      boxShadow: '0 6px 20px rgba(255, 107, 44, 0.39)',
                    },
                    '&.Mui-disabled': {
                      background: 'linear-gradient(to right, #FFE4BA, #FFF8F3)',
                      color: '#FF6B2C',
                    },
                  }}
                >
                  {isSubmitting ? 'Envoi...' : existingReply ? 'Modifier' : 'Répondre'}
                </Button>
              </div>
            </div>
          </form>
        </motion.div>
      </ModalPortal>

      {/* Système de génération IA pour la réponse à l'avis */}
      {isAiConfirmModalOpen && (
        <AiGenerationSystem
          type="review_response"
          prompt={`
            Informations de l'avis:
            - Note donnée: ${reviewData?.note || 'Non spécifiée'}/5
            - Commentaire: ${reviewData?.commentaire ? stripHtml(reviewData.commentaire) : 'Non spécifié'}
            - Qualités mentionnées: ${reviewData?.qualites?.join(', ') || 'Aucune'}
            - Points à améliorer: ${reviewData?.defauts?.join(', ') || 'Aucun'}
            - Mission concernée: ${reviewData?.mission_titre || 'Non spécifiée'}
            ${reply.length > 0 ?
              `- Action: Améliorer le texte existant
            - Texte à améliorer: ${decodeHtmlEntities(reply)}
            - Instructions: Conserve le sens et les informations de la réponse originale, mais améliore le style, la clarté et le professionnalisme. Ne réinvente pas complètement le contenu.`
              :
              `- Action: Générer une nouvelle réponse à l'avis
            - Instructions: Crée une réponse professionnelle, sincère et constructive à cet avis client.
            - Contexte: L'avis concerne la mission "${reviewData?.mission_titre || 'Non spécifiée'}" avec une note de ${reviewData?.note || 'Non spécifiée'}/5.
            - Ton: Professionnel mais chaleureux, reconnaissant pour le feedback.
            - Structure: Remerciement, reconnaissance des points positifs, réponse aux points à améliorer, conclusion positive.`
            }
          `}
          originalPrompt={reply.length > 0 ? decodeHtmlEntities(reply) : undefined}
          onComplete={(content) => {
            setIsAiConfirmModalOpen(false);

            if (content) {
              // Mettre à jour le contenu de la réponse avec le contenu généré
              setReply(content);
            }
          }}
          onCancel={() => {
            setIsAiConfirmModalOpen(false);
          }}
          maxDuration={30000}
        />
      )}
    </>
  );
};

export default ReviewReplyModal;