import React, { useState, useEffect } from 'react';
import { TextField, Button, IconButton, CircularProgress, Tooltip } from '@mui/material';
import { X, Edit2, Trash2, Plus, Save, MoveVertical, Check } from 'lucide-react';
import { motion, Reorder } from 'framer-motion';
import { missionResponsesApi, MissionResponse } from '../services/missionResponsesApi';
import { notify } from './Notification';
import ModalPortal from './ModalPortal';
import logger from '@/utils/logger';

interface MissionResponsesManagerProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectResponse?: (content: string) => void;
}

const MissionResponsesManager: React.FC<MissionResponsesManagerProps> = ({
  isOpen,
  onClose,
  onSelectResponse
}) => {
  const [responses, setResponses] = useState<MissionResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [isReordering, setIsReordering] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(isOpen);

  // Synchroniser l'état local avec la prop isOpen
  useEffect(() => {
    if (isOpen) {
      setIsModalVisible(true);
    }
  }, [isOpen]);

  // Fonction pour fermer proprement la modale
  const handleClose = () => {
    setIsModalVisible(false);
    // Réinitialiser les états
    setIsReordering(false);
    setIsAdding(false);
    setEditingId(null);
    // Appeler la fonction onClose après un court délai pour permettre à l'animation de se terminer
    setTimeout(() => {
      onClose();
    }, 200);
  };

  // Charger les réponses prédéfinies
  useEffect(() => {
    if (isOpen) {
      loadResponses();
    }
  }, [isOpen]);

  const loadResponses = async () => {
    setLoading(true);
    try {
      const data = await missionResponsesApi.getUserResponses();
      setResponses(data);
    } catch (error) {
      logger.error('Erreur lors du chargement des réponses:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddNew = () => {
    setIsAdding(true);
    setTitle('');
    setContent('');
    setEditingId(null);
  };

  const handleEdit = (response: MissionResponse) => {
    setIsAdding(false);
    setEditingId(response.id);
    setTitle(response.title);
    setContent(response.content);
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette réponse prédéfinie ?')) {
      try {
        const success = await missionResponsesApi.deleteResponse(id);
        if (success) {
          setResponses(responses.filter(r => r.id !== id));
        }
      } catch (error) {
        logger.error('Erreur lors de la suppression:', error);
      }
    }
  };

  const handleSave = async () => {
    if (title.trim().length < 3 || content.trim().length < 10) {
      notify('Le titre doit contenir au moins 3 caractères et le contenu au moins 10 caractères.', 'error');
      return;
    }

    setIsSaving(true);
    try {
      if (isAdding) {
        const newResponse = await missionResponsesApi.createResponse(title, content);
        if (newResponse) {
          setResponses([...responses, newResponse]);
          setIsAdding(false);
        }
      } else if (editingId) {
        const updatedResponse = await missionResponsesApi.updateResponse(editingId, title, content);
        if (updatedResponse) {
          setResponses(responses.map(r => r.id === editingId ? updatedResponse : r));
          setEditingId(null);
        }
      }
      setTitle('');
      setContent('');
    } catch (error) {
      logger.error('Erreur lors de la sauvegarde:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setIsAdding(false);
    setEditingId(null);
    setTitle('');
    setContent('');
  };

  const handleReorderComplete = async () => {
    try {
      const items = responses.map((response, index) => ({
        id: response.id,
        order_index: index
      }));
      
      await missionResponsesApi.reorderResponses(items);
      setIsReordering(false);
      handleClose(); // Utiliser handleClose au lieu de onClose directement
    } catch (error) {
      logger.error('Erreur lors de la réorganisation:', error);
      notify('Erreur lors de la réorganisation des réponses', 'error');
    }
  };

  const handleSelectResponse = (response: MissionResponse) => {
    if (onSelectResponse) {
      onSelectResponse(response.content);
      handleClose(); // Utiliser handleClose au lieu de onClose directement
    }
  };

  return (
    <ModalPortal isOpen={isModalVisible} onBackdropClick={handleClose} closeOnBackdropClick={true}>
      {/* <div className="fixed inset-0 flex items-center justify-center z-50 p-4"> */}
      <div className="fixed bg-white rounded-xl w-full max-w-[700px] max-h-[90vh] overflow-y-auto mx-auto z-50">
          {/* En-tête */}
          <div className="flex justify-between items-center p-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-800">Gérer vos réponses prédéfinies</h2>
            <button 
              onClick={handleClose}
              className="p-1 rounded-full hover:bg-gray-100 transition-colors"
              aria-label="Fermer"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Contenu */}
          <div className="flex-1 overflow-y-auto p-4">
            {loading ? (
              <div className="flex justify-center items-center h-40">
                <CircularProgress size={40} sx={{ color: '#FF6B2C' }} />
              </div>
            ) : (
              <>
                {/* Actions */}
                <div className="flex justify-between mb-4">
                  <div className="flex gap-2">
                    <Button 
                      variant="contained" 
                      startIcon={<Plus size={18} />}
                      onClick={handleAddNew}
                      disabled={isAdding || editingId !== null || responses.length >= 10}
                      sx={{ 
                        bgcolor: '#FF6B2C', 
                        '&:hover': { bgcolor: '#FF7A35' },
                        '&.Mui-disabled': { bgcolor: '#FFD0B5', color: 'white' }
                      }}
                    >
                      Ajouter
                    </Button>
                    <Button 
                      variant="outlined" 
                      startIcon={<MoveVertical size={18} />}
                      onClick={() => {
                        if (isReordering) {
                          // Si on termine la réorganisation, on ferme la modale
                          setIsReordering(false);
                          handleClose(); // Utiliser handleClose au lieu de onClose directement
                        } else {
                          // Sinon on active le mode réorganisation
                          setIsReordering(true);
                        }
                      }}
                      disabled={isAdding || editingId !== null || responses.length < 2}
                      sx={{ 
                        borderColor: '#FF6B2C', 
                        color: '#FF6B2C',
                        '&:hover': { borderColor: '#FF7A35', bgcolor: 'rgba(255, 107, 44, 0.04)' }
                      }}
                    >
                      {isReordering ? 'Terminer' : 'Réorganiser'}
                    </Button>
                  </div>
                  {isReordering && (
                    <Button 
                      variant="contained" 
                      startIcon={<Check size={18} />}
                      onClick={handleReorderComplete}
                      sx={{ 
                        bgcolor: '#FF6B2C', 
                        '&:hover': { bgcolor: '#FF7A35' }
                      }}
                    >
                      Enregistrer l'ordre
                    </Button>
                  )}
                </div>

                {/* Formulaire d'ajout/édition */}
                {(isAdding || editingId !== null) && (
                  <div className="mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
                    <h3 className="text-lg font-medium mb-3">
                      {isAdding ? 'Ajouter une réponse' : 'Modifier la réponse'}
                    </h3>
                    <div className="space-y-4">
                      <div>
                        <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                          Titre
                        </label>
                        <TextField
                          id="title"
                          fullWidth
                          value={title}
                          onChange={(e) => setTitle(e.target.value)}
                          placeholder="Titre court et descriptif"
                          size="small"
                          error={title.length > 0 && title.length < 3}
                          helperText={`${title.length}/50 caractères - Minimum 3 caractères`}
                          inputProps={{ maxLength: 50 }}
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              '&.Mui-focused fieldset': {
                                borderColor: '#FF6B2C',
                              },
                            },
                          }}
                        />
                      </div>
                      <div>
                        <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
                          Contenu
                        </label>
                        <TextField
                          id="content"
                          multiline
                          minRows={4}
                          maxRows={8}
                          fullWidth
                          value={content}
                          onChange={(e) => setContent(e.target.value)}
                          placeholder="Contenu de votre réponse prédéfinie..."
                          size="small"
                          error={content.length > 0 && content.length < 10}
                          helperText={`${content.length}/500 caractères - Minimum 10 caractères`}
                          inputProps={{ maxLength: 500 }}
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              '&.Mui-focused fieldset': {
                                borderColor: '#FF6B2C',
                              },
                            },
                          }}
                        />
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button 
                          variant="outlined" 
                          onClick={handleCancel}
                          disabled={isSaving}
                          sx={{ 
                            borderColor: 'gray', 
                            color: 'gray',
                            '&:hover': { borderColor: 'darkgray', bgcolor: 'rgba(0, 0, 0, 0.04)' }
                          }}
                        >
                          Annuler
                        </Button>
                        <Button 
                          variant="contained" 
                          startIcon={<Save size={18} />}
                          onClick={handleSave}
                          disabled={isSaving || title.length < 3 || content.length < 10}
                          sx={{ 
                            bgcolor: '#FF6B2C', 
                            '&:hover': { bgcolor: '#FF7A35' },
                            '&.Mui-disabled': { bgcolor: '#FFD0B5', color: 'white' }
                          }}
                        >
                          {isSaving ? (
                            <CircularProgress size={24} sx={{ color: 'white' }} />
                          ) : (
                            'Enregistrer'
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Liste des réponses */}
                {responses.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500 mb-4">Vous n'avez pas encore de réponses prédéfinies.</p>
                    <Button 
                      variant="contained" 
                      startIcon={<Plus size={18} />}
                      onClick={handleAddNew}
                      sx={{ 
                        bgcolor: '#FF6B2C', 
                        '&:hover': { bgcolor: '#FF7A35' }
                      }}
                    >
                      Créer votre première réponse
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {isReordering ? (
                      <Reorder.Group 
                        axis="y" 
                        values={responses} 
                        onReorder={setResponses}
                        className="space-y-3"
                      >
                        {responses.map((response) => (
                          <Reorder.Item 
                            key={response.id} 
                            value={response}
                            className="bg-white border border-gray-200 rounded-lg p-4 cursor-move hover:shadow-md transition-shadow"
                          >
                            <div className="flex items-center gap-2">
                              <MoveVertical className="h-5 w-5 text-gray-400" />
                              <h4 className="font-medium">{response.title}</h4>
                            </div>
                          </Reorder.Item>
                        ))}
                      </Reorder.Group>
                    ) : (
                      responses.map((response) => (
                        <div 
                          key={response.id} 
                          className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                        >
                          <div className="flex justify-between items-start mb-2">
                            <h4 className="font-medium">{response.title}</h4>
                            <div className="flex gap-1">
                              <Tooltip title="Utiliser cette réponse" arrow>
                                <IconButton 
                                  size="small" 
                                  onClick={() => handleSelectResponse(response)}
                                  sx={{ color: '#FF6B2C' }}
                                >
                                  <Check size={18} />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Modifier" arrow>
                                <IconButton 
                                  size="small" 
                                  onClick={() => handleEdit(response)}
                                  disabled={isAdding || editingId !== null}
                                >
                                  <Edit2 size={18} />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Supprimer" arrow>
                                <IconButton 
                                  size="small" 
                                  onClick={() => handleDelete(response.id)}
                                  disabled={isAdding || editingId !== null}
                                  sx={{ color: 'red' }}
                                >
                                  <Trash2 size={18} />
                                </IconButton>
                              </Tooltip>
                            </div>
                          </div>
                          <p className="text-gray-600 text-sm line-clamp-2">{response.content}</p>
                        </div>
                      ))
                    )}
                  </div>
                )}
              </>
            )}
          </div>

          {/* Pied de page */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-500">
                {responses.length}/10 réponses prédéfinies
              </div>
              <Button 
                variant="contained" 
                onClick={handleClose}
                sx={{ 
                  bgcolor: '#FF6B2C', 
                  '&:hover': { bgcolor: '#FF7A35' }
                }}
              >
                Fermer
              </Button>
            </div>
          </div>
      </div>
    </ModalPortal>
  );
};

export default MissionResponsesManager; 