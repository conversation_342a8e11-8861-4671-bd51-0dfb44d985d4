import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  Search, 
  Filter, 
  FileText, 
  FileCheck, 
  CreditCard,
  ChevronDown,
  X
} from 'lucide-react';
import DocumentDetails from './DocumentDetails';
import DocumentForm from './DocumentForm';
import { 
  getDocuments, 
  getClients, 
  saveDocument, 
  deleteDocument 
} from '../../../services/invoice';
import { invoiceService } from '../../../services/invoiceService';
import type { Document, Client } from '../../../services/billingService';
import { notify } from '@/components/Notification';
import { useCompanyValidation } from '../../../hooks/invoices/useCompanyValidation';
const BillingDocuments: React.FC = () => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isCompanyInfoComplete, getValidationMessage } = useCompanyValidation();
  
  // UI states
  const [activeTab, setActiveTab] = useState<'devis' | 'facture' | 'avoir'>('devis');
  const [viewMode, setViewMode] = useState<'list' | 'details' | 'edit' | 'create'>('list');
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  
  // Load initial data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [documentsData, clientsData] = await Promise.all([
          getDocuments(),
          getClients()
        ]);
        
        setDocuments(documentsData);
        setClients(clientsData);
        setError(null);
      } catch (err) {
        setError('Erreur lors du chargement des données');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);
  
  // Filter documents based on active tab, search term and filter status
  const filteredDocuments = documents
    .filter(doc => doc.type === activeTab)
    .filter(doc => {
      if (!searchTerm) return true;
      const searchLower = searchTerm.toLowerCase();
      return (
        doc.number?.toLowerCase().includes(searchLower) ||
        doc.client_name.toLowerCase().includes(searchLower) ||
        doc.description.toLowerCase().includes(searchLower)
      );
    })
    .filter(doc => {
      if (!filterStatus) return true;
      return doc.statut === filterStatus;
    });
  
  // Get document statuses for current tab (for filter dropdown)
  const availableStatuses = Array.from(
    new Set(documents.filter(doc => doc.type === activeTab).map(doc => doc.statut))
  );
  
  // Handle document selection for viewing details
  const handleViewDocument = (document: Document) => {
    setSelectedDocument(document);
    setViewMode('details');
  };
  
  // Handle creating a new document
  const handleCreateDocument = () => {
    // Vérifier d'abord si les informations d'entreprise sont complètes
    if (!isCompanyInfoComplete()) {
      notify(getValidationMessage(), 'error');
      return;
    }

    setSelectedDocument(null);
    setViewMode('create');
  };
  
  // Handle editing a document
  const handleEditDocument = (document: Document) => {
    setSelectedDocument(document);
    setViewMode('edit');
  };
  
  // Handle saving a document
  const handleSaveDocument = async (document: Document) => {
    try {
      const isNewDocument = !document.id;
      
      // For new documents, generate a number
      if (isNewDocument) {
        const prefix = document.type === 'devis' ? 'D' : document.type === 'facture' ? 'F' : 'A';
        const date = new Date();
        const year = date.getFullYear().toString();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        
        // Count documents of the same type to generate number
        const sameTypeCount = documents.filter(doc => doc.type === document.type).length + 1;
        const counter = sameTypeCount.toString().padStart(3, '0');
        
        document.number = `${prefix}-${year}${month}-${counter}`;
      }
      
      const savedDoc = await saveDocument(document);
      
      if (isNewDocument) {
        setDocuments([...documents, savedDoc]);
      } else {
        setDocuments(documents.map(doc => 
          doc.id === savedDoc.id ? savedDoc : doc
        ));
      }
      
      setViewMode('list');
      setSelectedDocument(null);
      notify(isNewDocument ? 'Document créé avec succès' : 'Document modifié avec succès', 'success');
    } catch (err) {
      notify('Erreur lors de l\'enregistrement du document', 'error');
      console.error(err);
    }
  };
  
  // Handle duplicating a document
  const handleDuplicateDocument = async (document: Document) => {
    // Vérifier d'abord si les informations d'entreprise sont complètes
    if (!isCompanyInfoComplete()) {
      notify(getValidationMessage(), 'error');
      return;
    }

    try {
      const duplicatedDoc = {
        ...document,
        id: undefined,
        number: `${document.number || ''}-copie`,
        statut: 'brouillon',
        date_creation: new Date().toISOString()
      };
      
      const savedDoc = await saveDocument(duplicatedDoc);
      setDocuments([...documents, savedDoc]);
      notify('Document dupliqué avec succès', 'success');
    } catch (err) {
      notify('Erreur lors de la duplication du document', 'error');
      console.error(err);
    }
  };
  
  // Handle deleting a document
  const handleDeleteDocument = async (documentId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce document ?')) {
      try {
        await deleteDocument(documentId);
        setDocuments(documents.filter(doc => doc.id !== documentId));
        
        if (viewMode === 'details' && selectedDocument?.id === documentId) {
          setViewMode('list');
          setSelectedDocument(null);
        }
        
        notify('Document supprimé avec succès', 'success');
      } catch (err) {
        notify('Erreur lors de la suppression du document', 'error');
        console.error(err);
      }
    }
  };
  
  // Handle downloading a document
  const handleDownloadDocument = (document: Document) => {
    notify('Téléchargement du document en cours...', 'info');
    // Implement actual download functionality
  };
  
  // Handle sending a document by email
  const handleSendEmail = (document: Document) => {
    notify('Préparation de l\'email en cours...', 'info');
    // Implement email sending functionality
  };
  
  // Handle converting a quote to an invoice
  const handleConvertToInvoice = async (document: Document) => {
    if (document.type !== 'devis') {
      notify('Seuls les devis peuvent être convertis en factures', 'error');
      return;
    }
    
    if (document.statut !== 'accepte') {
      notify('Seuls les devis acceptés peuvent être convertis en facture', 'warning');
      return;
    }

    // Vérifier si les informations d'entreprise sont complètes
    if (!isCompanyInfoComplete()) {
      notify(getValidationMessage(), 'error');
      return;
    }
    
    if (!document.id) {
      notify('ID du document manquant', 'error');
      return;
    }
    
    try {
      const invoiceDocument = await invoiceService.convertToInvoice(document.id);
      
      // Reload data to ensure consistency
      const [documentsData, clientsData] = await Promise.all([
        getDocuments(),
        getClients()
      ]);
      setDocuments(documentsData);
      setClients(clientsData);
      
      // Update selectedDocument with the fresh data from the reloaded list
      if (selectedDocument && selectedDocument.id === document.id) {
        const updatedDocument = documentsData.find((doc: Document) => doc.id === invoiceDocument.id);
        if (updatedDocument) {
          setSelectedDocument(updatedDocument);
        }
      }
      
      notify('Devis converti en facture avec succès', 'success');
    } catch (err: any) {
      console.error('Erreur lors de la conversion du devis:', err);
      const errorMessage = err.response?.data?.message || 'Erreur lors de la conversion du devis en facture';
      notify(errorMessage, 'error');
    }
  };
  
  // Handle creating a credit note from an invoice
  const handleCreateCreditNote = async (document: Document) => {
    if (document.type !== 'facture') {
      notify('Seules les factures peuvent générer des avoirs', 'error');
      return;
    }

    // Vérifier si les informations d'entreprise sont complètes
    if (!isCompanyInfoComplete()) {
      notify(getValidationMessage(), 'error');
      return;
    }
    
    if (!document.id) {
      notify('ID du document manquant', 'error');
      return;
    }
    
    try {
      const creditNote = await invoiceService.createCreditNote(document.id);
      
      // Reload data to ensure consistency
      const [documentsData, clientsData] = await Promise.all([
        getDocuments(),
        getClients()
      ]);
      setDocuments(documentsData);
      setClients(clientsData);
      
      // Update selectedDocument with the fresh data from the reloaded list
      if (selectedDocument && selectedDocument.id === document.id) {
        const updatedDocument = documentsData.find((doc: Document) => doc.id === creditNote.id);
        if (updatedDocument) {
          setSelectedDocument(updatedDocument);
        }
      }
      
      notify('Avoir créé avec succès', 'success');
    } catch (err: any) {
      console.error('Erreur lors de la création de l\'avoir:', err);
      const errorMessage = err.response?.data?.message || 'Erreur lors de la création de l\'avoir';
      notify(errorMessage, 'error');
    }
  };
  
  // Render loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF7A35]"></div>
      </div>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-96">
        <div className="text-red-500 text-xl mb-4">{error}</div>
        <button 
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-[#FF7A35] text-white rounded-md hover:bg-[#ff6b2c]"
        >
          Réessayer
        </button>
      </div>
    );
  }
  
  // Render document form
  if (viewMode === 'create' || viewMode === 'edit') {
    return (
      <div className="container mx-auto p-4">
        <DocumentForm
          document={selectedDocument || undefined}
          documentType={activeTab}
          clients={clients}
          onSave={handleSaveDocument as any}
          onCancel={() => {
            setViewMode('list');
            setSelectedDocument(null);
          }}
        />
      </div>
    );
  }
  
  // Render document details
  if (viewMode === 'details' && selectedDocument) {
    // Ensure that selectedDocument has non-undefined required properties before passing to DocumentDetails
    const documentWithRequiredProps = {
      ...selectedDocument,
      id: selectedDocument.id || '',  // Convert undefined to empty string
      number: selectedDocument.number || '',  // Ensure number is also defined
    };
    
    return (
      <div className="container mx-auto p-4">
        <DocumentDetails
          document={documentWithRequiredProps}
          onEdit={handleEditDocument}
          onDelete={() => handleDeleteDocument(selectedDocument.id || '')}
          onDuplicate={() => handleDuplicateDocument(selectedDocument)}
          onDownload={() => handleDownloadDocument(selectedDocument)}
          onSendEmail={() => handleSendEmail(selectedDocument)}
          onConvertToInvoice={() => handleConvertToInvoice(selectedDocument)}
          onCreateCreditNote={() => handleCreateCreditNote(selectedDocument)}
          onBack={() => {
            setViewMode('list');
            setSelectedDocument(null);
          }}
        />
      </div>
    );
  }
  
  // Render document list
  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">
          {activeTab === 'devis' ? 'Devis' : activeTab === 'facture' ? 'Factures' : 'Avoirs'}
        </h1>
        <button
          onClick={handleCreateDocument}
          className="px-4 py-2 bg-[#FF7A35] text-white rounded-md hover:bg-[#ff6b2c] inline-flex items-center"
        >
          <Plus size={18} className="mr-1" />
          Créer un {activeTab === 'devis' ? 'devis' : activeTab === 'facture' ? 'une facture' : 'un avoir'}
        </button>
      </div>
      
      <div className="bg-white rounded-lg shadow-sm mb-6">
        <div className="flex border-b">
          <button
            onClick={() => setActiveTab('devis')}
            className={`px-6 py-3 text-sm font-medium inline-flex items-center ${
              activeTab === 'devis'
                ? 'text-[#FF7A35] border-b-2 border-[#FF7A35]'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <FileText size={18} className="mr-2" />
            Devis
          </button>
          <button
            onClick={() => setActiveTab('facture')}
            className={`px-6 py-3 text-sm font-medium inline-flex items-center ${
              activeTab === 'facture'
                ? 'text-[#FF7A35] border-b-2 border-[#FF7A35]'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <FileCheck size={18} className="mr-2" />
            Factures
          </button>
          <button
            onClick={() => setActiveTab('avoir')}
            className={`px-6 py-3 text-sm font-medium inline-flex items-center ${
              activeTab === 'avoir'
                ? 'text-[#FF7A35] border-b-2 border-[#FF7A35]'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <CreditCard size={18} className="mr-2" />
            Avoirs
          </button>
        </div>
        
        <div className="p-4">
          <div className="flex flex-col md:flex-row gap-3 mb-4">
            <div className="relative flex-grow">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={18} className="text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Rechercher par numéro, client ou description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
              />
            </div>
            
            <div className="relative">
              <button
                onClick={() => setIsFilterOpen(!isFilterOpen)}
                className={`px-4 py-2 border ${
                  filterStatus ? 'border-[#FF7A35] bg-orange-50 text-[#FF7A35]' : 'border-gray-300 bg-white text-gray-700'
                } rounded-md inline-flex items-center hover:bg-gray-50`}
              >
                <Filter size={18} className="mr-2" />
                {filterStatus ? `Statut: ${filterStatus}` : 'Filtrer par statut'}
                <ChevronDown size={16} className="ml-2" />
              </button>
              
              {isFilterOpen && (
                <div className="absolute z-10 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg">
                  <div className="p-2">
                    <button
                      onClick={() => {
                        setFilterStatus(null);
                        setIsFilterOpen(false);
                      }}
                      className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 rounded-md mb-1 inline-flex items-center justify-between"
                    >
                      <span>Tous les statuts</span>
                      {!filterStatus && <span className="text-[#FF7A35]">✓</span>}
                    </button>
                    
                    {availableStatuses.map(status => (
                      <button
                        key={status}
                        onClick={() => {
                          setFilterStatus(status);
                          setIsFilterOpen(false);
                        }}
                        className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 rounded-md mb-1 inline-flex items-center justify-between"
                      >
                        <span>{status}</span>
                        {filterStatus === status && <span className="text-[#FF7A35]">✓</span>}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            {filterStatus && (
              <button
                onClick={() => setFilterStatus(null)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md inline-flex items-center hover:bg-gray-50"
              >
                <X size={18} className="mr-1" />
                Effacer les filtres
              </button>
            )}
          </div>
          
          {filteredDocuments.length === 0 ? (
            <div className="p-8 text-center">
              <div className="text-gray-500 mb-2">Aucun document trouvé</div>
              <p className="text-gray-400 text-sm">
                {searchTerm || filterStatus 
                  ? "Essayez de modifier vos critères de recherche ou filtres" 
                  : `Créez votre premier ${activeTab === 'devis' ? 'devis' : activeTab === 'facture' ? 'facture' : 'avoir'}`}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      N°
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Client
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Montant
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Statut
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredDocuments.map((document) => (
                    <tr 
                      key={document.id}
                      onClick={() => handleViewDocument(document)}
                      className="hover:bg-gray-50 cursor-pointer"
                    >
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-[#FF7A35]">
                        {document.number}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {new Date(document.date_creation).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {document.client_name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700">
                        {document.total_ttc !== undefined ? document.total_ttc.toFixed(2) : '0.00'} €
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          document.statut === 'brouillon' ? 'bg-gray-100 text-gray-600' :
                          document.statut === 'émis' || document.statut === 'envoyé' ? 'bg-blue-100 text-blue-700' :
                          document.statut === 'accepté' || document.statut === 'payé' ? 'bg-green-100 text-green-700' :
                          document.statut === 'refusé' ? 'bg-red-100 text-red-700' :
                          document.statut === 'à payer' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-gray-100 text-gray-600'
                        }`}>
                          {document.statut}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button 
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewDocument(document);
                          }}
                          className="text-[#FF7A35] hover:text-[#ff6b2c] mr-3"
                        >
                          Voir
                        </button>
                        <button 
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditDocument(document);
                          }}
                          className="text-gray-600 hover:text-gray-900"
                        >
                          Modifier
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BillingDocuments;