import { z } from 'zod';

export interface Mission {
  id: string;
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  date: string;
  mission: {
    id: string;
    title: string;
    description?: string;
  };
  // Informations sur l'utilisateur propriétaire
  user?: {
    id: string;
    prenom: string;
    nom: string;
    photo_url?: string;
  };
  // Informations sur la proposition/offre acceptée
  proposition?: {
    id: string;
    statut: 'en_attente' | 'acceptée' | 'refusée' | 'contre_offre' | 'contre_offre_jobbeur';
    montant_propose: number;
    message?: string;
    montant_contre_offre?: number;
    message_contre_offre?: string;
    montant_contre_offre_jobbeur?: number;
    message_contre_offre_jobbeur?: string;
    payment_status?: 'pending' | 'completed' | 'manual';
    payment_date?: string;
    montant_paiement?: number;
    jobbeur_id?: string;
    payment_method?: 'jobi_only' | 'both' | 'direct_only';
  };
  // Pour les missions manuelles
  montant_propose?: number;
  payment_method?: 'jobi_only' | 'both' | 'direct_only';
}

export const planningFormSchema = z.object({
  mission_id: z.string().uuid().optional(),
  title: z.string().min(1, "Le titre est requis").max(120, "Le titre ne doit pas dépasser 120 caractères"),
  description: z.string().max(1200, "La description ne doit pas dépasser 1200 caractères").optional(),
  start_time: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Format d'heure invalide"),
  end_time: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Format d'heure invalide"),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Format de date invalide"),
  montant_propose: z.coerce.number().min(0).optional(),
  payment_method: z.enum(['jobi_only', 'both', 'direct_only']).optional()
}).refine(
  (data) => {
    // Convertir les heures en nombre de minutes depuis minuit pour faciliter la comparaison
    const startMinutes = convertTimeToMinutes(data.start_time);
    const endMinutes = convertTimeToMinutes(data.end_time);
    
    // Vérifier que l'heure de fin est après l'heure de début
    return endMinutes > startMinutes;
  },
  {
    message: "L'heure de fin doit être postérieure à l'heure de début",
    path: ["end_time"] // Associe l'erreur au champ end_time
  }
);

// Fonction utilitaire pour convertir un format d'heure HH:MM en minutes
const convertTimeToMinutes = (timeString: string): number => {
  if (!timeString || !timeString.includes(':')) return 0;
  
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
};

export type PlanningFormData = z.infer<typeof planningFormSchema>; 