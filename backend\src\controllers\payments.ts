import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import { redis } from '../config/redis';
import { z } from 'zod';
import logger from '../utils/logger';
import PDFDocument from 'pdfkit';
import { subscriptions } from '../config/ConfigSubscriptions';
import { getUserSubscriptionLimits } from '../routes/configSubscriptions';
import { decryptProfilDataAsync } from '../utils/encryption';

// Schéma de validation pour les transactions
const transactionSchema = z.object({
  date: z.string(),
  type: z.enum(['paiement', 'remboursement', 'commission', 'abonnement', 'transfert']),
  montant: z.number(),
  client: z.string().optional(),
  mission: z.string().optional(),
  description: z.string().optional(),
  statut: z.enum(['en_attente', 'complete', 'refuse']),
  methode: z.enum(['jobi', 'carte', 'virement', 'especes', 'cheque', 'autre']),
  reference: z.string().optional(),
  categorie: z.enum(['mission', 'transfert', 'abonnement', 'rechargement', 'remboursement', 'facture', 'autre'])
});

type Transaction = z.infer<typeof transactionSchema>;

// Interfaces pour le typage des données
interface MissionClient {
  user_profil: {
    nom: string;
    prenom: string;
  }[];
}

interface Mission {
  titre: string;
  user_id: string;
  client: MissionClient;
}

interface MissionPayment {
  id: string;
  statut: string;
  date_acceptation: string | null;
  montant_paiement: number | null;
  mission: {
    titre: string;
    user_id: string;
    client: MissionClient;
  };
}

// Constantes pour le cache Redis
const CACHE_TTL = 3600; // 1 heure en secondes
const CACHE_KEYS = {
  USER_TRANSACTIONS: (userId: string, page: number = 0, limit: number = 10) => 
    `transactions:user:${userId}:page:${page}:limit:${limit}`,
  USER_TRANSACTIONS_COUNT: (userId: string) => `transactions:user:${userId}:count`,
  USER_REVENUS: (userId: string, period: number) => `transactions:revenus:${userId}:period:${period}`
};

// Fonction utilitaire pour la gestion du cache des transactions
async function updateTransactionsCache(userId: string) {
  try {
    // Supprimer toutes les clés liées aux transactions de l'utilisateur
    const pattern = `transactions:user:${userId}*`;
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
      logger.info(`Cache des transactions supprimé: ${keys.length} clés`);
    }
    
    // Supprimer aussi le cache des revenus
    const revenusPattern = `transactions:revenus:${userId}*`;
    const revenusKeys = await redis.keys(revenusPattern);
    if (revenusKeys.length > 0) {
      await redis.del(...revenusKeys);
      logger.info(`Cache des revenus supprimé: ${revenusKeys.length} clés`);
    }
  } catch (error) {
    logger.error('Erreur lors de la mise à jour du cache des transactions:', error);
  }
}

export class PaymentsController {
  // Récupérer toutes les transactions
  async getTransactions(req: Request, res: Response) {
    try {
      if (!req.user?.userId) {
        res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
        return;
      }

      const userId = req.user.userId;
      const page = parseInt(req.query.page as string) || 0;
      const limit = parseInt(req.query.limit as string) || 10;
      const offset = page * limit;
      
      // Récupérer les filtres depuis la requête
      const {
        dateDebut,
        dateFin,
        type,
        categorie,
        methode,
        statut,
        montantMin,
        montantMax,
        searchTerm
      } = req.query;
      
      // Générer une clé de cache unique basée sur tous les paramètres
      const cacheKey = `transactions:user:${userId}:page:${page}:limit:${limit}:filters:${JSON.stringify({
        dateDebut,
        dateFin,
        type,
        categorie,
        methode,
        statut,
        montantMin,
        montantMax,
        searchTerm
      })}`;
      
      const cachedData = await redis.get(cacheKey);
      
      if (cachedData) {
        logger.info('Transactions: GetTransactions Données récupérées depuis le cache');
        return res.json(JSON.parse(cachedData));
      }

      // Construire la requête de base
      let query = supabase
        .from('user_transac')
        .select('*', { count: 'exact' })
        .eq('user_id', userId);

      // Appliquer les filtres
      if (dateDebut) {
        query = query.gte('date', dateDebut as string);
      }

      if (dateFin) {
        query = query.lte('date', dateFin as string);
      }

      if (type) {
        const typeArray = (type as string).split(',');
        if (typeArray.length > 0) {
          query = query.in('type', typeArray);
        }
      }

      if (categorie) {
        const categorieArray = (categorie as string).split(',');
        if (categorieArray.length > 0) {
          query = query.in('categorie', categorieArray);
        }
      }

      if (methode) {
        const methodeArray = (methode as string).split(',');
        if (methodeArray.length > 0) {
          query = query.in('methode', methodeArray);
        }
      }

      if (statut) {
        const statutArray = (statut as string).split(',');
        if (statutArray.length > 0) {
          query = query.in('statut', statutArray);
        }
      }

      if (montantMin) {
        query = query.gte('montant', parseFloat(montantMin as string));
      }

      if (montantMax) {
        query = query.lte('montant', parseFloat(montantMax as string));
      }

      if (searchTerm) {
        query = query.or(`description.ilike.%${searchTerm}%,mission.ilike.%${searchTerm}%,client.ilike.%${searchTerm}%`);
      }

      // Ajouter la pagination
      query = query.range(offset, offset + limit - 1).order('date', { ascending: false });

      // Exécuter la requête
      const { data: transactions, error, count } = await query;

      if (error) throw error;

      // Mettre en cache les résultats
      const result = {
        success: true,
        transactions,
        total: count
      };
      
      await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(result));
      await redis.setex(CACHE_KEYS.USER_TRANSACTIONS_COUNT(userId), CACHE_TTL, count?.toString() || '0');

      return res.json(result);

    } catch (error) {
      logger.error('Erreur lors de la récupération des transactions:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des transactions'
      });
    }
  }

  // Récupérer les transactions à importer
  async getTransactionsToImport(req: Request, res: Response) {
    try {
      if (!req.user?.userId) {
        res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
        return;
      }

      const userId = req.user.userId;
      let offset: number;
      let page: number;
      const limit = parseInt(req.query.limit as string) || 20;
      const type = req.query.type as string || 'missions';

      // logger.info('Début getTransactionsToImport:', { userId, type, limit });

      // Gestion de la pagination avec offset ou page
      if (req.query.offset) {
        offset = parseInt(req.query.offset as string);
        page = Math.floor(offset / limit);
      } else {
        page = parseInt(req.query.page as string) || 0;
        offset = page * limit;
      }

      // logger.info('Paramètres de pagination:', { offset, page, limit });

      // Récupérer d'abord les références des transactions déjà importées
      const { data: existingTransactions, error: existingError } = await supabase
        .from('user_transac')
        .select('reference')
        .eq('user_id', userId);

      if (existingError) {
        logger.error('Erreur lors de la récupération des références existantes:', existingError);
        throw existingError;
      }

      const existingReferences = new Set(existingTransactions?.map(t => t.reference) || []);
      // logger.info('Références existantes:', { count: existingReferences.size });

      let transactions = [];
      let total = 0;

      if (type === 'missions') {
        // Récupérer les paiements des missions depuis user_mission_candidature
        const { data: missionPayments, error: missionError, count } = await supabase
          .from('user_mission_candidature')
          .select(`
            id,
            statut,
            montant_paiement,
            date_acceptation,
            mission:mission_id (
              titre,
              user_id,
              client:user_id (
                user_profil (
                  nom,
                  prenom
                )
              )
            )
          `, { count: 'exact' })
          .eq('jobbeur_id', userId)
          .not('montant_paiement', 'is', null)
          .order('date_acceptation', { ascending: false })
          .range(offset, offset + limit - 1);

        if (missionError) throw missionError;

        total = count || 0;

        // Transformer les paiements de missions en format de transaction
        const filteredPayments = (missionPayments || [])
          .filter(payment => {
            const reference = `MISSION-${payment.id}`;
            return !existingReferences.has(reference);
          });

        transactions = await Promise.all(filteredPayments.map(async (payment) => {
          const typedPayment = payment as unknown as MissionPayment;
          const clientProfile = typedPayment.mission?.client?.user_profil?.[0];

          // Déchiffrer les données du profil client
          const decryptedClientProfile = clientProfile ? await decryptProfilDataAsync(clientProfile) : null;

          const date = typedPayment.date_acceptation ? new Date(typedPayment.date_acceptation) : new Date();
          return {
            id: typedPayment.id,
            date: date.toISOString().split('T')[0],
            display_date: date.toLocaleDateString('fr-FR', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit'
            }),
            type: 'paiement',
            montant: typedPayment.montant_paiement || 0,
            client: decryptedClientProfile ? `${decryptedClientProfile.prenom} ${decryptedClientProfile.nom}` : '',
            mission: typedPayment.mission?.titre || '',
            description: `Paiement pour la mission: ${typedPayment.mission?.titre || 'Sans titre'}`,
            statut: typedPayment.statut === 'acceptée' ? 'complete' : 'en_attente',
            methode: 'jobi',
            reference: `MISSION-${typedPayment.id}`,
            categorie: 'mission'
          };
        }));
      } else if (type === 'jobi') {
        // logger.info('Récupération des transactions Jobi');
        
        // D'abord, récupérer toutes les références existantes pour Jobi
        const existingJobiRefs = Array.from(existingReferences)
          .filter(ref => ref.startsWith('JOBI-'))
          .map(ref => ref.replace('JOBI-', ''));

        // logger.info('Références Jobi existantes:', { 
        //   count: existingJobiRefs.length,
        //   sample: existingJobiRefs.slice(0, 3)
        // });

        // Récupérer toutes les transactions Jobi disponibles
        const { data: allTransactions, error: countError, count: totalCount } = await supabase
          .from('user_jobi_historique')
          .select(`
            id,
            montant,
            date_creation,
            titre,
            description,
            message
          `, { count: 'exact' })
          .eq('user_id', userId)
          .order('date_creation', { ascending: false });

        if (countError) {
          logger.error('Erreur lors du comptage des transactions Jobi:', countError);
          throw countError;
        }

        // Filtrer les transactions non importées
        const availableTransactions = (allTransactions || [])
          .filter(transaction => !existingJobiRefs.includes(transaction.id));

        // logger.info('Transactions disponibles:', {
        //   total: totalCount,
        //   nonImportees: availableTransactions.length,
        //   dejaImportees: existingJobiRefs.length
        // });

        // Appliquer la pagination sur les transactions disponibles
        const start = offset;
        const end = offset + limit;
        const paginatedTransactions = availableTransactions.slice(start, end);

        // Transformer les transactions Jobi
        transactions = paginatedTransactions.map(transaction => {
          const date = transaction.date_creation ? new Date(transaction.date_creation) : new Date();
          // logger.info('Nouvelle transaction trouvée:', { 
          //   id: transaction.id,
          //   date: transaction.date_creation
          // });
          return {
            id: transaction.id,
            date: date.toISOString().split('T')[0],
            display_date: date.toLocaleDateString('fr-FR', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit'
            }),
            type: 'transfert',
            montant: transaction.montant || 0,
            description: transaction.description || transaction.titre || 'Transaction Jobi',
            statut: 'complete',
            methode: 'jobi',
            reference: `JOBI-${transaction.id}`,
            categorie: 'transfert',
            message: transaction.message
          };
        });

        // Le total est le nombre de transactions non importées
        total = availableTransactions.length;

        // logger.info('Résumé des transactions Jobi:', {
        //   totalDispo: total,
        //   recuperees: paginatedTransactions.length,
        //   offset,
        //   limit,
        //   existingCount: existingJobiRefs.length,
        //   plage: `${offset + 1} à ${offset + limit}`
        // });
      } else if (type === 'factures') {
        // Récupérer les factures payées ou partiellement payées
        const { data: invoices, error: invoicesError, count } = await supabase
          .from('invoices')
          .select('*', { count: 'exact' })
          .eq('user_id', userId)
          .in('statut', ['paye', 'partiellement_paye'])
          .not('date_paiement', 'is', null)
          .order('date_paiement', { ascending: false })
          .range(offset, offset + limit - 1);

        if (invoicesError) throw invoicesError;

        total = count || 0;

        // Transformer les factures en format de transaction
        transactions = (invoices || [])
          .filter(invoice => {
            const reference = `FACTURE-${invoice.id}`;
            return !existingReferences.has(reference);
          })
          .map(invoice => {
            const date = invoice.date_paiement ? new Date(invoice.date_paiement) : new Date();
            return {
              id: invoice.id,
              date: date.toISOString().split('T')[0],
              display_date: date.toLocaleDateString('fr-FR', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
              }),
              type: 'paiement',
              montant: invoice.total_ttc || 0,
              client: invoice.client_name || '',
              description: `Paiement de la facture: ${invoice.number || ''}`,
              statut: 'complete',
              methode: (invoice.mode_paiement === 'jobi') ? 'jobi' : (invoice.mode_paiement || 'autre'),
              reference: `FACTURE-${invoice.id}`,
              categorie: 'facture'
            };
          });
      } else {
        // Récupérer les autres transactions
        const { data: otherTransactions, error, count } = await supabase
          .from('user_transac')
          .select('*', { count: 'exact' })
          .eq('user_id', userId)
          .eq('imported', false)
          .order('created_at', { ascending: false })
          .range(offset, offset + limit - 1);

        if (error) throw error;

        total = count || 0;

        // Formater les dates des transactions
        transactions = (otherTransactions || []).map(transaction => ({
          ...transaction,
          display_date: new Date(transaction.date).toLocaleDateString('fr-FR', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
          })
        }));
      }

      return res.json({
        success: true,
        transactions,
        total,
        page,
        limit
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération des transactions à importer:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des transactions à importer'
      });
    }
  }

  // Importer les transactions
  async importTransactions(req: Request, res: Response) {
    try {
      if (!req.user?.userId) {
        res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
        return;
      }

      const userId = req.user.userId;
      const { missions = [], jobiTransactions = [], otherTransactions = [], factureTransactions = [] } = req.body;
      
      // Combiner toutes les transactions à importer
      const transactions = [...missions, ...jobiTransactions, ...otherTransactions, ...factureTransactions];

      if (!Array.isArray(transactions) || transactions.length === 0) {
        res.status(400).json({
          success: false,
          message: 'Format de données invalide ou aucune transaction à importer'
        });
      }

      // Vérifier les transactions existantes
      const { data: existingTransactions, error: existingError } = await supabase
        .from('user_transac')
        .select('reference')
        .eq('user_id', userId)
        .in('reference', transactions.map(t => t.reference));

      if (existingError) throw existingError;

      const existingReferences = new Set(existingTransactions?.map(t => t.reference) || []);
      const newTransactions = transactions.filter(t => !existingReferences.has(t.reference));

      if (newTransactions.length === 0) {
        return res.json({
          success: true,
          message: 'Toutes les transactions ont déjà été importées',
          importedCount: 0
        });
      }

      // Vérifier la limite d'abonnement
      const { isPremium, options, serviceLimit, galleriesLimit, interventionAreasLimit, conversationsLimit } = await getUserSubscriptionLimits(userId);
      
      // Utiliser les limites de transaction selon le plan
      const transactionLimit = isPremium ? 
        subscriptions.premium.transactions.included : 
        subscriptions.gratuit.transactions.included;

      // Compter le nombre de transactions existantes
      const { count: existingCount, error: countError } = await supabase
        .from('user_transac')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      if (countError) throw countError;

      const currentCount = existingCount || 0;
      const remainingSlots = Math.max(0, transactionLimit - currentCount);

      if (newTransactions.length > remainingSlots) {
        res.status(400).json({
          success: false,
          message: `Impossible d'importer ${newTransactions.length} transactions. Votre plan ${isPremium ? 'premium' : 'gratuit'} permet un maximum de ${transactionLimit} transactions. Il vous reste ${remainingSlots} emplacements disponibles.`
        });
      }

      // Formater et valider les nouvelles transactions
      const formattedTransactions = newTransactions.map(transaction => {
        // Convertir la date au format ISO si elle est au format français
        let date = transaction.date;
        if (date.includes('/')) {
          const [day, month, year] = date.split('/');
          date = `${year}-${month}-${day}`;
        }

        return {
          user_id: userId,
          date,
          type: transaction.type,
          montant: transaction.montant,
          client: transaction.client,
          mission: transaction.mission,
          description: transaction.description,
          statut: transaction.statut,
          methode: transaction.methode,
          reference: transaction.reference,
          categorie: transaction.categorie,
          imported: true,
          created_at: new Date().toISOString()
        };
      });

      // Valider chaque transaction avec le schéma Zod
      const validTransactions = formattedTransactions.filter(transaction => {
        try {
          transactionSchema.parse(transaction);
          return true;
        } catch (error) {
          logger.error(`Validation error for transaction ${transaction.reference}:`, error);
          return false;
        }
      });

      if (validTransactions.length === 0) {
        res.status(400).json({
          success: false,
          message: 'Aucune transaction valide à importer'
        });
      }

      // Insérer les nouvelles transactions
      const { data, error } = await supabase
        .from('user_transac')
        .insert(validTransactions)
        .select();

      if (error) throw error;

      // Mettre à jour le cache
      await updateTransactionsCache(userId);

      return res.json({
        success: true,
        message: `${validTransactions.length} transaction(s) importée(s) avec succès`,
        importedCount: validTransactions.length,
        transactions: data
      });

    } catch (error) {
      logger.error('Erreur lors de l\'importation des transactions:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'importation des transactions'
      });
    }
  }

  // Créer une transaction
  async createTransaction(req: Request, res: Response) {
    try {
      if (!req.user?.userId) {
        res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
        return;
      }

      const userId = req.user.userId;
      const transactionData = transactionSchema.parse(req.body);

      // Vérifier la limite d'abonnement
      const { isPremium } = await getUserSubscriptionLimits(userId);
      
      // Récupérer la limite de transactions selon le plan
      const transactionLimit = isPremium ? 
        subscriptions.premium.transactions.included : 
        subscriptions.gratuit.transactions.included;

      // Compter le nombre de transactions existantes
      const { count: existingCount, error: countError } = await supabase
        .from('user_transac')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      if (countError) throw countError;

      const currentCount = existingCount || 0;
      
      // Vérifier si l'utilisateur a atteint sa limite
      if (currentCount >= transactionLimit) {
        res.status(400).json({
          success: false,
          message: `Impossible de créer une nouvelle transaction. Votre plan ${isPremium ? 'premium' : 'gratuit'} permet un maximum de ${transactionLimit} transactions.`
        });
      }

      // Générer une référence unique si elle n'est pas fournie
      const reference = transactionData.reference || `TRANSAC-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;

      const { data, error } = await supabase
        .from('user_transac')
        .insert([
          {
            ...transactionData,
            reference,
            user_id: userId,
            imported: true
          }
        ])
        .select()
        .single();

      if (error) throw error;

      // Mettre à jour le cache
      await updateTransactionsCache(userId);

      return res.json({
        success: true,
        message: 'Transaction créée avec succès',
        transaction: data
      });

    } catch (error) {
      logger.error('Erreur lors de la création de la transaction:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la création de la transaction'
      });
    }
  }

  // Mettre à jour une transaction
  async updateTransaction(req: Request, res: Response) {
    try {
      if (!req.user?.userId) {
        res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
        return;
      }

      const userId = req.user.userId;
      const { id } = req.params;
      const transaction = transactionSchema.parse(req.body);

      const { data, error } = await supabase
        .from('user_transac')
        .update(transaction)
        .eq('id', id)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;

      // Mettre à jour le cache
      await updateTransactionsCache(userId);

      return res.json({
        success: true,
        message: 'Transaction mise à jour avec succès',
        transaction: data
      });

    } catch (error) {
      logger.error('Erreur lors de la mise à jour de la transaction:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour de la transaction'
      });
    }
  }

  // Supprimer une transaction
  async deleteTransaction(req: Request, res: Response) {
    try {
      if (!req.user?.userId) {
        res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
        return;
      }

      const userId = req.user.userId;
      const { id } = req.params;

      const { error } = await supabase
        .from('user_transac')
        .delete()
        .eq('id', id)
        .eq('user_id', userId);

      if (error) throw error;

      // Mettre à jour le cache
      await updateTransactionsCache(userId);

      return res.json({
        success: true,
        message: 'Transaction supprimée avec succès'
      });

    } catch (error) {
      logger.error('Erreur lors de la suppression de la transaction:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la suppression de la transaction'
      });
    }
  }

  // Supprimer plusieurs transactions en lot
  async batchDeleteTransactions(req: Request, res: Response) {
    try {
      if (!req.user?.userId) {
        res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
        return;
      }

      const userId = req.user.userId;
      const { ids } = req.body;

      if (!Array.isArray(ids) || ids.length === 0) {
        res.status(400).json({
          success: false,
          message: 'Liste d\'identifiants de transactions invalide'
        });
      }

      const { error } = await supabase
        .from('user_transac')
        .delete()
        .in('id', ids)
        .eq('user_id', userId);

      if (error) throw error;

      // Mettre à jour le cache
      await updateTransactionsCache(userId);

      return res.json({
        success: true,
        message: `${ids.length} transaction(s) supprimée(s) avec succès`
      });

    } catch (error) {
      logger.error('Erreur lors de la suppression en masse des transactions:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la suppression en masse des transactions'
      });
    }
  }

  // Récupérer les revenus et statistiques
  async getRevenus(req: Request, res: Response) {
    try {
      if (!req.user?.userId) {
        res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
        return;
      }

      const userId = req.user.userId;
      const period = parseInt(req.query.period as string) || 6;
      
      // Vérifier si les données sont en cache
      const cacheKey = `transactions:revenus:${userId}:period:${period}`;
      const cachedData = await redis.get(cacheKey);
      
      if (cachedData) {
        logger.info('Transactions: GetRevenus Données récupérées depuis le cache');
        return res.json(JSON.parse(cachedData));
      }

      // Valider la période
      if (period < 1 || period > 12) {
        res.status(400).json({
          success: false,
          message: 'La période doit être comprise entre 1 et 12 mois'
        });
      }

      // Calculer la date de début pour la période
      const monthsAgo = new Date();
      monthsAgo.setMonth(monthsAgo.getMonth() - period + 1);
      monthsAgo.setDate(1);
      monthsAgo.setHours(0, 0, 0, 0);
      
      const startDate = monthsAgo.toISOString().split('T')[0];

      // Récupérer le solde Jobi
      const { data: jobiData, error: jobiError } = await supabase
        .from('user_jobi')
        .select('montant')
        .eq('user_id', userId)
        .single();

      if (jobiError) throw jobiError;

      // Récupérer les transactions en attente
      const { data: enAttente, error: enAttenteError } = await supabase
        .from('user_transac')
        .select('montant')
        .eq('user_id', userId)
        .eq('statut', 'en_attente')
        .filter('date', 'gte', startDate)
        .not('montant', 'is', null);

      if (enAttenteError) throw enAttenteError;

      // Calculer le montant en attente
      const montantEnAttente = enAttente.reduce((acc, curr) => acc + curr.montant, 0);

      // Calculer les revenus par mois
      const { data: revenusData, error: revenusError } = await supabase
        .from('user_transac')
        .select('date, montant')
        .eq('user_id', userId)
        .eq('statut', 'complete')
        .gte('date', startDate)
        .order('date', { ascending: true });

      if (revenusError) throw revenusError;

      // Générer tous les mois de la période
      interface MonthRevenue {
        mois: string;
        montant: number;
      }
      
      const allMonths: MonthRevenue[] = [];
      const currentDate = new Date(monthsAgo);
      const endDate = new Date();
      
      while (currentDate <= endDate) {
        const mois = currentDate.toLocaleString('fr-FR', { month: 'long', year: 'numeric' });
        allMonths.push({ mois, montant: 0 });
        currentDate.setMonth(currentDate.getMonth() + 1);
      }

      // Ajouter les montants aux mois correspondants
      revenusData.forEach(transaction => {
        const date = new Date(transaction.date);
        const mois = date.toLocaleString('fr-FR', { month: 'long', year: 'numeric' });
        const monthData = allMonths.find(m => m.mois === mois);
        if (monthData) {
          monthData.montant += transaction.montant;
        }
      });

      const result = {
        success: true,
        solde: jobiData?.montant || 0,
        enAttente: montantEnAttente,
        revenus: allMonths
      };

      // Mettre en cache les résultats
      await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(result));

      return res.json(result);

    } catch (error) {
      logger.error('Erreur lors de la récupération des revenus:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des revenus'
      });
    }
  }

  // Exporter les transactions en PDF
  async exportTransactionsPDF(req: Request, res: Response) {
    try {
      if (!req.user?.userId) {
        res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
        return;
      }

      const userId = req.user.userId;
      const {
        dateDebut,
        dateFin,
        type,
        categorie,
        methode,
        statut,
        montantMin,
        montantMax,
        searchTerm,
        period
      } = req.query;

      // Calculer les dates de début et fin si elles ne sont pas spécifiées
      let startDate = dateDebut as string;
      let endDate = dateFin as string;

      if (!startDate || !endDate) {
        const today = new Date();
        endDate = today.toISOString().split('T')[0];
        
        // Si une période est spécifiée (en mois)
        if (period) {
          const start = new Date();
          start.setMonth(start.getMonth() - parseInt(period as string));
          startDate = start.toISOString().split('T')[0];
        } else {
          // Par défaut, on prend le début du mois en cours
          const start = new Date();
          start.setDate(1);
          startDate = start.toISOString().split('T')[0];
        }
      }

      // Créer une requête de base
      let query = supabase
        .from('user_transac')
        .select('*')
        .eq('user_id', userId)
        .order('date', { ascending: false });

      // Appliquer les filtres de date
      query = query.gte('date', startDate).lte('date', endDate);

      // Appliquer les autres filtres
      if (type) {
        const typeArray = (type as string).split(',');
        if (typeArray.length > 0) {
          query = query.in('type', typeArray);
        }
      }
      if (categorie) {
        const categorieArray = (categorie as string).split(',');
        if (categorieArray.length > 0) {
          query = query.in('categorie', categorieArray);
        }
      }
      if (methode) {
        const methodeArray = (methode as string).split(',');
        if (methodeArray.length > 0) {
          query = query.in('methode', methodeArray);
        }
      }
      if (statut) {
        const statutArray = (statut as string).split(',');
        if (statutArray.length > 0) {
          query = query.in('statut', statutArray);
        }
      }
      if (montantMin) {
        query = query.gte('montant', parseFloat(montantMin as string));
      }
      if (montantMax) {
        query = query.lte('montant', parseFloat(montantMax as string));
      }
      if (searchTerm) {
        query = query.or(`description.ilike.%${searchTerm}%,mission.ilike.%${searchTerm}%,client.ilike.%${searchTerm}%`);
      }

      // Récupérer les transactions
      const { data: transactions, error } = await query;

      if (error) throw error;

      // Créer le document PDF
      const doc = new PDFDocument({
        size: 'A4',
        margin: 50,
        info: {
          Title: 'Rapport des Transactions',
          Author: 'JobPartiel.fr'
        }
      });

      // Configurer les en-têtes pour le téléchargement
      const today = new Date().toISOString().split('T')[0];
      const fileName = `jobpartiel-fr-transactions-${dateDebut || today}-${dateFin || today}.pdf`;
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename=${fileName}`);

      // Pipe le PDF vers la réponse
      doc.pipe(res);

      // Définir les couleurs et styles
      const colors = {
        primary: '#FF7A35',
        primaryLight: '#FFF1EA',
        secondary: '#2C3E50',
        text: '#2C3E50',
        textLight: '#64748B',
        background: '#FFFFFF',
        borderLight: '#E2E8F0',
        success: '#10B981',
        error: '#EF4444',
        accent: '#F8FAFC'
      };

      // Fonction pour ajouter le header sur chaque page
      const addHeader = () => {
        // Ajouter le header
        doc.rect(0, 0, doc.page.width, 100)
           .fillColor(colors.background)
           .fill();

        // Ajouter une bande décorative en haut
        doc.rect(0, 0, doc.page.width, 8)
           .fillColor(colors.primary)
           .fill();

        // Ajouter le logo et le titre
        doc.fontSize(26)
           .fillColor(colors.secondary)
           .text('JobPartiel.fr', 50, 30)
           .fontSize(11)
           .fillColor(colors.textLight)
           .text('Vos compétences, vos missions. Vos revenus, vos règles.', 50, 60);

        // Ajouter la date du jour
        const dateGeneration = new Date().toLocaleDateString('fr-FR', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });

        doc.fontSize(10)
           .fillColor(colors.textLight)
           .text(dateGeneration, doc.page.width - 200, 40, { align: 'right' });

        return 120; // Retourne la position Y après le header
      };

      // Initialiser la position Y avec le header
      let currentY = addHeader();

      // Ajouter le titre du rapport avec un style moderne
      doc.rect(50, currentY, 495, 100)
         .fillColor(colors.primaryLight)
         .fill()
         .roundedRect(50, currentY, 495, 100, 10)
         .fillColor(colors.primaryLight)
         .fill();

      doc.fontSize(24)
         .fillColor(colors.secondary)
         .text('Rapport des Transactions', 50, currentY + 20, { align: 'center' });

      // Ajouter la période avec un style amélioré
      const dateDebutFormatted = new Date(startDate).toLocaleDateString('fr-FR', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });
      const dateFinFormatted = new Date(endDate).toLocaleDateString('fr-FR', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });

      doc.fontSize(12)
         .fillColor(colors.text)
         .text('Période', {align: 'center'})
         .fontSize(11)
         .fillColor(colors.textLight)
         .text(`Du ${dateDebutFormatted} au ${dateFinFormatted}`, 50, currentY + 60, { align: 'center' });

      currentY += 120;

      // Ajouter les filtres appliqués dans une boîte élégante
      if (type || categorie || methode || statut || montantMin || montantMax || searchTerm) {
        // Fond pour les filtres avec coins arrondis
        doc.roundedRect(50, currentY, 495, 15 + (
          (type ? 1 : 0) + 
          (categorie ? 1 : 0) + 
          (methode ? 1 : 0) + 
          (statut ? 1 : 0) + 
          ((montantMin || montantMax) ? 1 : 0) + 
          (searchTerm ? 1 : 0)
        ) * 15, 5)
           .fillColor(colors.accent)
           .fill();

        currentY += 10;
        doc.fontSize(10).fillColor(colors.text);

        // Afficher les filtres avec des puces élégantes
        if (type) {
          doc.circle(65, currentY + 5, 2).fillColor(colors.primary).fill()
             .fillColor(colors.text)
             .text(`Types: ${(type as string).split(',').join(', ')}`, 75, currentY);
          currentY += 15;
        }
        if (categorie) {
          doc.circle(65, currentY + 5, 2).fillColor(colors.primary).fill()
             .fillColor(colors.text)
             .text(`Catégories: ${(categorie as string).split(',').join(', ')}`, 75, currentY);
          currentY += 15;
        }
        if (methode) {
          doc.circle(65, currentY + 5, 2).fillColor(colors.primary).fill()
             .fillColor(colors.text)
             .text(`Méthodes: ${(methode as string).split(',').join(', ')}`, 75, currentY);
          currentY += 15;
        }
        if (statut) {
          doc.circle(65, currentY + 5, 2).fillColor(colors.primary).fill()
             .fillColor(colors.text)
             .text(`Statuts: ${(statut as string).split(',').join(', ')}`, 75, currentY);
          currentY += 15;
        }
        if (montantMin || montantMax) {
          const montantFilter = `Montant: ${montantMin ? `min ${montantMin}€` : ''} ${montantMax ? `max ${montantMax}€` : ''}`;
          doc.text(montantFilter, 75, currentY);
          currentY += 15;
        }
        if (searchTerm) {
          doc.circle(65, currentY + 5, 2).fillColor(colors.primary).fill()
             .fillColor(colors.text)
             .text(`Recherche: "${searchTerm}"`, 75, currentY);
          currentY += 15;
        }

        currentY += 20;
      }

      // Configuration du tableau avec un style moderne
      const tableTop = currentY;
      const rowHeight = 35;
      currentY = tableTop;

      // Fonction pour ajouter le footer
      const addFooter = (pageNumber: number, totalPages: number, forceBottom: boolean = false) => {
        const footerHeight = 40;
        let footerTop = currentY + 20;
        
        // Si on force le footer en bas de page
        if (forceBottom) {
          footerTop = doc.page.height - 50;
        }
        
        // Ligne de séparation
        doc.moveTo(50, footerTop)
           .lineTo(545, footerTop)
           .lineWidth(0.5)
           .strokeColor(colors.borderLight)
           .stroke();

        // Footer content
        doc.fontSize(8)
           .fillColor(colors.textLight)
           .text('JobPartiel.fr - Vos compétences, vos missions. Vos revenus, vos règles.', 50, footerTop + 15)
           .text(`Page ${pageNumber} sur ${totalPages}`, 450, footerTop + 15, { align: 'right' });

        return footerTop + footerHeight;
      };

      // Calculer le nombre total de pages
      const contentHeight = doc.page.height - 150; // Hauteur disponible par page
      const rowsPerPage = Math.floor((contentHeight - tableTop) / rowHeight);
      const transactionPages = Math.ceil((transactions?.length || 0) / rowsPerPage);
      const hasStats = true; // On a toujours une page de stats
      const totalPages = transactionPages + (hasStats ? 1 : 0);
      let pageNumber = 1;

      // En-têtes du tableau avec un style amélioré
      doc.rect(50, currentY, 495, rowHeight)
         .fillColor(colors.accent)
         .fill();

      doc.fontSize(10)
         .fillColor(colors.text)
         .text('Date', 60, currentY + 12)
         .text('Type', 140, currentY + 12)
         .text('Méthode', 200, currentY + 12)
         .text('Description', 280, currentY + 12)
         .text('Montant', 450, currentY + 12, { align: 'right' });

      currentY += rowHeight;

      // Lignes du tableau avec alternance de couleurs
      transactions?.forEach((transaction, index) => {
        // Vérifier s'il faut ajouter une nouvelle page
        if (currentY > 650) { // Réduit la limite pour laisser de la place au footer
          addFooter(pageNumber, totalPages);
          pageNumber++;
          doc.addPage();
          currentY = addHeader(); // Ajouter le header sur la nouvelle page
          
          // Répéter les en-têtes sur la nouvelle page
          doc.rect(50, currentY, 495, rowHeight)
             .fillColor(colors.accent)
             .fill();

          doc.fontSize(10)
             .fillColor(colors.text)
             .text('Date', 60, currentY + 12)
             .text('Type', 140, currentY + 12)
             .text('Méthode', 200, currentY + 12)
             .text('Description', 280, currentY + 12)
             .text('Montant', 450, currentY + 12, { align: 'right' });
          
          currentY += rowHeight;
        }

        // Alternance de couleurs pour les lignes
        if (index % 2 === 0) {
          doc.rect(50, currentY, 495, rowHeight)
             .fillColor('#F8FAFC')
             .fill();
        }

        const date = new Date(transaction.date).toLocaleDateString('fr-FR');
        
        doc.fontSize(10)
           .fillColor(colors.text)
           .text(date, 60, currentY + 12)
           .text(transaction.type, 140, currentY + 12)
           .text(transaction.methode || '', 200, currentY + 12)
           .text(transaction.description || '', 280, currentY + 12, { width: 160 })
           .fillColor(transaction.montant >= 0 ? colors.success : colors.error)
           .text(`${transaction.montant.toFixed(2)} €`, 450, currentY + 12, { align: 'right' });

        currentY += rowHeight;
      });

      // Total avec un style moderne
      doc.rect(50, currentY + 10, 495, 100)
         .fillColor(colors.accent)
         .fill();

      const totalEntrees = transactions?.reduce((sum, t) => sum + (t.montant > 0 ? t.montant : 0), 0) || 0;
      const totalSorties = transactions?.reduce((sum, t) => sum + (t.montant < 0 ? t.montant : 0), 0) || 0;
      const total = totalEntrees + totalSorties;

      // Afficher les totaux avec un style élégant
      doc.fontSize(11)
         .fillColor(colors.text)
         .text('Total des entrées:', 380, currentY + 20)
         .fillColor(colors.success)
         .text(`${totalEntrees.toFixed(2)} €`, 450, currentY + 20, { align: 'right' })
         .fillColor(colors.text)
         .text('Total des sorties:', 380, currentY + 45)
         .fillColor(colors.error)
         .text(`${totalSorties.toFixed(2)} €`, 450, currentY + 45, { align: 'right' })
         .moveTo(380, currentY + 65)
         .lineTo(545, currentY + 65)
         .lineWidth(0.5)
         .strokeColor(colors.borderLight)
         .stroke()
         .fontSize(12)
         .fillColor(colors.text)
         .text('Total net:', 380, currentY + 75)
         .fillColor(total >= 0 ? colors.success : colors.error)
         .text(`${total.toFixed(2)} €`, 450, currentY + 75, { align: 'right' });

      currentY += 130;

      // Vérifier s'il faut ajouter une nouvelle page pour les statistiques
      if (currentY > 450) {
        addFooter(pageNumber, totalPages);
        pageNumber++;
        doc.addPage();
        currentY = addHeader();
      }

      // Calculer les statistiques
      const stats = {
        totalTransactions: transactions?.length || 0,
        moyenneParTransaction: total / (transactions?.length || 1),
        plusGrosseTransaction: Math.max(...(transactions?.map(t => Math.abs(t.montant)) || [0])),
        totalPositif: transactions?.reduce((sum, t) => sum + (t.montant > 0 ? t.montant : 0), 0) || 0,
        totalNegatif: transactions?.reduce((sum, t) => sum + (t.montant < 0 ? t.montant : 0), 0) || 0,
        parType: transactions?.reduce((acc: any, t) => {
          acc[t.type] = (acc[t.type] || 0) + 1;
          return acc;
        }, {}),
        parMethode: transactions?.reduce((acc: any, t) => {
          acc[t.methode] = (acc[t.methode] || 0) + 1;
          return acc;
        }, {})
      };

      // Afficher les statistiques
      doc.fontSize(16)
         .fillColor(colors.secondary)
         .text(`Statistiques du ${dateDebutFormatted} au ${dateFinFormatted}`, 50, currentY);

      currentY += 30;

      // Créer une grille de statistiques 2x2
      const statBoxWidth = 240;
      const statBoxHeight = 80;
      const gap = 15;

      // Fonction pour dessiner une boîte de statistique
      const drawStatBox = (x: number, y: number, title: string, value: string) => {
        doc.roundedRect(x, y, statBoxWidth, statBoxHeight, 5)
           .fillColor(colors.primaryLight)
           .fill();

        doc.fontSize(10)
           .fillColor(colors.textLight)
           .text(title, x + 15, y + 15)
           .fontSize(16)
           .fillColor(colors.secondary)
           .text(value, x + 15, y + 35);
      };

      // Première ligne de statistiques
      drawStatBox(50, currentY, 'Nombre de transactions', stats.totalTransactions.toString());
      drawStatBox(50 + statBoxWidth + gap, currentY, 'Moyenne par transaction', 
        `${stats.moyenneParTransaction.toFixed(2)} €`);

      // Deuxième ligne de statistiques
      currentY += statBoxHeight + gap;
      drawStatBox(50, currentY, 'Total des entrées', `${stats.totalPositif.toFixed(2)} €`);
      drawStatBox(50 + statBoxWidth + gap, currentY, 'Total des sorties', 
        `${stats.totalNegatif.toFixed(2)} €`);

      // Calculer la position du footer pour la dernière page
      currentY += statBoxHeight + 50;

      // Ajouter le footer sur la dernière page
      addFooter(pageNumber, totalPages);

      // Finaliser le document
      doc.end();

    } catch (error) {
      logger.error('Erreur lors de l\'export des transactions en PDF:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'export des transactions en PDF'
      });
    }
  }
} 