import React, { useState, useEffect } from 'react';
import { Button, Chip, CircularProgress, Tooltip } from '@mui/material';
import { MessageSquare, Settings, ChevronDown, ChevronUp } from 'lucide-react';
import { missionResponsesApi, MissionResponse } from '../services/missionResponsesApi';
import logger from '@/utils/logger';

interface PredefinedResponsesListProps {
  onSelectResponse: (content: string) => void;
  onManageResponses: () => void;
}

const PredefinedResponsesList: React.FC<PredefinedResponsesListProps> = ({
  onSelectResponse,
  onManageResponses
}) => {
  const [responses, setResponses] = useState<MissionResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    loadResponses();
  }, []);

  const loadResponses = async () => {
    setLoading(true);
    try {
      const data = await missionResponsesApi.getUserResponses();
      setResponses(data);
    } catch (error) {
      logger.error('Erreur lors du chargement des réponses:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-3">
        <CircularProgress size={24} sx={{ color: '#FF6B2C' }} />
      </div>
    );
  }

  if (responses.length === 0) {
    return (
      <div className="mt-2">
        <Button
          variant="outlined"
          startIcon={<Settings size={16} />}
          onClick={onManageResponses}
          size="small"
          sx={{
            borderColor: '#FF6B2C',
            color: '#FF6B2C',
            '&:hover': { borderColor: '#FF7A35', bgcolor: 'rgba(255, 107, 44, 0.04)' }
          }}
        >
          Gérer mes réponses prédéfinies
        </Button>
      </div>
    );
  }

  return (
    <div className="mt-2">
      <div className="flex items-center justify-between mb-2">
        <Button
          variant="text"
          endIcon={expanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
          onClick={() => setExpanded(!expanded)}
          size="small"
          sx={{
            color: '#FF6B2C',
            '&:hover': { bgcolor: 'rgba(255, 107, 44, 0.04)' }
          }}
        >
          {expanded ? 'Masquer mes réponses' : 'Afficher mes réponses prédéfinies'}
        </Button>
        <Button
          variant="outlined"
          startIcon={<Settings size={16} />}
          onClick={onManageResponses}
          size="small"
          sx={{
            borderColor: '#FF6B2C',
            color: '#FF6B2C',
            '&:hover': { borderColor: '#FF7A35', bgcolor: 'rgba(255, 107, 44, 0.04)' }
          }}
        >
          Gérer
        </Button>
      </div>

      {expanded && (
        <div className="flex flex-wrap gap-2 mt-2 pb-2">
          {responses.map((response) => (
            <Tooltip 
              key={response.id} 
              title={response.content.length > 10 ? response.content : ''}
              arrow
              placement="top"
            >
              <Chip
                label={response.title}
                onClick={() => onSelectResponse(response.content)}
                icon={<MessageSquare size={14} />}
                sx={{
                  bgcolor: 'white',
                  border: '1px solid #FFD0B5',
                  borderRadius: '4px',
                  color: '#FF6B2C',
                  '&:hover': {
                    bgcolor: '#FFF8F3',
                  },
                  '& .MuiChip-icon': {
                    color: '#FF6B2C',
                  }
                }}
              />
            </Tooltip>
          ))}
        </div>
      )}
    </div>
  );
};

export default PredefinedResponsesList; 