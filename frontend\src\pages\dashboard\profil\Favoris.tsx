import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { notify } from '../../../components/Notification';
import { Link, useNavigate } from 'react-router-dom';
import { Heart, MapPin, Loader2, Shield, CheckCircle, Phone, Mail, MessageCircle, Star } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { getCommonHeaders } from '../../../utils/headers';
import { logger } from '../../../utils/logger';
import { API_CONFIG } from '../../../config/api';
import { styled } from '@mui/material/styles';
import { Typography, Chip, Tooltip, Rating, CircularProgress } from '@mui/material';
import ModalPortal from '../../../components/ModalPortal';
import { getCityFromPostalCode } from '../../../utils/geocoding';
import OnlineStatusDot from '../../../components/OnlineStatusDot';
import ModalReview from '../../../components/ReviewModalSelecteurMission';
import DOMPurify from 'dompurify';

interface FavoriteProfile {
  id: string;
  favorite_user_id: string;
  created_at: string;
  users: {
    id: string;
    email: string;
    user_type: string;
    profil_verifier: boolean;
    identite_verifier: boolean;
    entreprise_verifier: boolean;
    assurance_verifier: boolean;
    is_online: boolean;
    last_login: string;
    profil: {
      nom: string;
      prenom: string;
      photo_url: string;
      bio: string;
      ville: string;
      slug: string;
      telephone: string;
      pays: string;
      code_postal: string;
      intervention_zone: {
        center: [number, number];
        radius: number;
        adresse?: string;
        france_entiere?: boolean;
      };
      adresse: string;
      numero: string;
      telephone_prive: boolean;
      storage_id: string;
      type_de_profil: 'particulier' | 'entreprise';
    }[];
    reviews: Array<{
      note: number;
      commentaire: string;
      created_at: string;
    }>;
    services: Array<{
      subcategory_id: string;
    }>;
  };
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

const PageTitle = styled(Typography)(() => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: '16px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: '#FF6B2C',
    borderRadius: '2px',
  },
}));

const ProfileCard = styled(motion.div)(() => ({
  background: '#FFFFFF',
  borderRadius: '16px',
  padding: '24px',
  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  transition: 'all 0.3s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  },
}));


const Favoris: React.FC = () => {
  const navigate = useNavigate();
  const [favorites, setFavorites] = useState<FavoriteProfile[]>([]);
  const [favoriteLimit, setFavoriteLimit] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [profilToDelete, setProfilToDelete] = useState<FavoriteProfile | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredFavorites, setFilteredFavorites] = useState<FavoriteProfile[]>([]);
  const [cities, setCities] = useState<{[key: string]: string}>({});
  const [addressDisplays, setAddressDisplays] = useState<{[key: string]: string}>({});
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [selectedProfilId, setSelectedProfilId] = useState<string>('');

  const fetchFavorites = async (page: number = 1, isLoadMore: boolean = false) => {
    if (isLoadMore) {
      setLoadingMore(true);
    } else {
      setLoading(true);
    }

    try {
      const response = await axios.get(`${API_CONFIG.baseURL}/api/favorites?page=${page}`, {
        headers: await getCommonHeaders(),
        withCredentials: true
      });

      logger.info('Réponse des favoris:', response.data);
      
      const newFavorites = response.data.data;
      setPagination(response.data.pagination);
      if (typeof response.data.favoriteLimit === 'number') {
        setFavoriteLimit(response.data.favoriteLimit);
      }

      if (isLoadMore) {
        setFavorites(prev => [...prev, ...newFavorites]);
        setFilteredFavorites(prev => [...prev, ...newFavorites]);
      } else {
        setFavorites(newFavorites);
        setFilteredFavorites(newFavorites);
      }

      logger.info('Favoris récupérés avec succès:', response.data);
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === 400) {
        notify(error.response.data.message, "warning");
      } else {
        notify("Erreur lors de la récupération des favoris", "error");
      }
      logger.error('Erreur lors de la récupération des favoris:', error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const loadMore = () => {
    if (pagination.page < pagination.totalPages) {
      fetchFavorites(pagination.page + 1, true);
    }
  };

  const removeFavorite = async (userId: string) => {
    try {
      await axios.delete(`${API_CONFIG.baseURL}/api/favorites/${userId}`, {
        headers: await getCommonHeaders(),
        withCredentials: true
      });
      notify("Favori supprimé avec succès", "success");
      const updatedFavorites = favorites.filter(fav => fav.users.id !== userId);
      setFavorites(updatedFavorites);
      setFilteredFavorites(updatedFavorites);
      setProfilToDelete(null);
    } catch (error) {
      notify("Erreur lors de la suppression du favori", "error");
      logger.error('Erreur lors de la suppression du favori:', error);
    }
  };

  const calculateAverageRating = (reviews: Array<{ note: number }>) => {
    if (!reviews || reviews.length === 0) return 0;
    const sum = reviews.reduce((acc, review) => acc + review.note, 0);
    return sum / reviews.length;
  };

  const formatLastLogin = (date: string) => {
    if (!date) return 'Jamais connecté';
    const lastLogin = new Date(date);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - lastLogin.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
      if (diffHours === 0) {
        const diffMinutes = Math.floor(diffTime / (1000 * 60));
        return `Il y a ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
      }
      return `Il y a ${diffHours} heure${diffHours > 1 ? 's' : ''}`;
    } else if (diffDays === 1) {
      return 'Hier';
    } else if (diffDays < 7) {
      return `Il y a ${diffDays} jour${diffDays > 1 ? 's' : ''}`;
    } else {
      return lastLogin.toLocaleDateString('fr-FR', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });
    }
  };

  const getServiceStatus = (services: Array<any> | undefined) => {
    if (!services || services.length === 0) {
      return "Ne propose pas de services";
    }
    return `Propose ${services.length} service${services.length > 1 ? 's' : ''}`;
  };

  const updateAddressDisplay = async (intervention_zone: any, ville: string, favoriteId: string) => {
    if (!intervention_zone) {
      setAddressDisplays(prev => ({ ...prev, [favoriteId]: ville }));
      return;
    }
    
    if (intervention_zone.france_entiere) {
      setAddressDisplays(prev => ({ ...prev, [favoriteId]: "France entière" }));
      return;
    }

    if (intervention_zone.adresse) {
      const codePostalMatch = intervention_zone.adresse.match(/\b\d{5}\b/);
      if (codePostalMatch) {
        const postalCode = codePostalMatch[0];
        
        if (!cities[postalCode]) {
          try {
            const city = await getCityFromPostalCode(postalCode);
            setCities(prev => ({ ...prev, [postalCode]: city }));
            setAddressDisplays(prev => ({ 
              ...prev, 
              [favoriteId]: `Rayon de ${intervention_zone.radius}km autour de ${city} (${postalCode})`
            }));
          } catch (error) {
            logger.error('Erreur lors de la récupération de la ville:', error);
            setAddressDisplays(prev => ({ 
              ...prev, 
              [favoriteId]: `Rayon de ${intervention_zone.radius}km autour de ${postalCode}`
            }));
          }
        } else {
          setAddressDisplays(prev => ({ 
            ...prev, 
            [favoriteId]: `Rayon de ${intervention_zone.radius}km autour de ${cities[postalCode]}`
          }));
        }
        return;
      }
    }

    setAddressDisplays(prev => ({ 
      ...prev, 
      [favoriteId]: `Rayon de ${intervention_zone.radius}km autour de ${ville}`
    }));
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    const searchValue = event.target.value.toLowerCase();
    setSearchTerm(searchValue);

    const filtered = favorites.filter(favorite => {
      const profil = (favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil && favorite.users.profil.length > 0) ? favorite.users.profil[0] : undefined;
      return (
        profil?.nom?.toLowerCase().includes(searchValue) ||
        profil?.prenom?.toLowerCase().includes(searchValue) ||
        profil?.ville?.toLowerCase().includes(searchValue) ||
        profil?.pays?.toLowerCase().includes(searchValue) ||
        favorite.users.email.toLowerCase().includes(searchValue) ||
        profil?.type_de_profil.toLowerCase().includes(searchValue)
      );
    });
    setFilteredFavorites(filtered);
  };

  const handleOpenMessage = (favorite: FavoriteProfile) => {
    // Stocker les informations dans le localStorage pour la modal de nouvelle conversation
    localStorage.setItem('newMessageInfo', JSON.stringify({
      recipientId: favorite.users.id,
      recipientName: `${(favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil.length > 0 ? favorite.users.profil[0].prenom : '')} ${(favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil.length > 0 && favorite.users.profil[0].nom ? favorite.users.profil[0].nom.charAt(0) + '.' : '')}`,
      initialMessage: `Bonjour ${(favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil.length > 0 ? favorite.users.profil[0].prenom : '')},\n\nJe souhaite vous contacter pour discuter d'une mission.\n\nNote : Le spam et les messages non sollicités sont interdits sur JobPartiel. Merci de respecter nos conditions d'utilisation sous peine de bannissement.`
    }));
    
    // Rediriger vers la page des messages
    navigate('/dashboard/messages');
  };

  const handleOpenReviewModal = (profilId: string) => {
    setSelectedProfilId(profilId);
    setIsReviewModalOpen(true);
  };

  const handleCloseReviewModal = () => {
    setIsReviewModalOpen(false);
    setSelectedProfilId('');
  };

  useEffect(() => {
    fetchFavorites();
  }, []);

  // Effet pour mettre à jour les adresses quand les favoris changent
  useEffect(() => {
    favorites.forEach(favorite => {
      updateAddressDisplay(
        (favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil && favorite.users.profil.length > 0 ? favorite.users.profil[0].intervention_zone : undefined),
        (favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil && favorite.users.profil.length > 0 ? favorite.users.profil[0].ville : ''),
        favorite.id
      );
    });
  }, [favorites]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-[#FF7A35]" />
      </div>
    );
  }

  return (
    <div className="space-y-6 px-2 md:px-0">
      <div className="flex items-center justify-between mb-4">
        <PageTitle variant="h1">
          Mes profils favoris
        </PageTitle>
      </div>

      {favoriteLimit !== null && (
        <div className="mb-2 text-sm text-gray-600">
          Favoris utilisés : <span className="font-bold text-[#FF7A35]">{favorites.length}</span> / <span className="font-bold text-[#FF7A35]">{favoriteLimit}</span>
          {favoriteLimit === 3 && (
            <span className="ml-2 text-xs text-[#FF6B2C] font-semibold">(Limité à 3 car vous n'êtes pas Premium)</span>
          )}
        </div>
      )}

      <div className="mb-6">
        <div className="relative">
          <input
            type="text"
            placeholder="Rechercher par nom, ville, pays..."
            value={searchTerm}
            onChange={handleSearch}
            className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent outline-none transition-all duration-200"
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      {filteredFavorites.length === 0 ? (
        <div className="bg-white rounded-xl p-8 text-center shadow-lg">
          {searchTerm ? (
            <>
              <svg className="h-16 w-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              <h2 className="text-xl font-semibold text-gray-600 mb-2">Aucun résultat trouvé</h2>
              <p className="text-gray-500">
                Aucun profil ne correspond à votre recherche. Essayez avec d'autres termes.
              </p>
            </>
          ) : (
            <>
              <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-600 mb-2">Aucun profil favori</h2>
              <p className="text-gray-500">
                Vous n'avez pas encore ajouté de profils à vos favoris.
                Explorez les profils et ajoutez ceux qui vous intéressent !
              </p>
            </>
          )}
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <AnimatePresence>
              {filteredFavorites.map((favorite) => (
                <ProfileCard
                  key={favorite.id}
                >
                  <div className="flex justify-between items-start mb-4">
                    <Link 
                      to={`/dashboard/profil/${(favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil.length > 0 ? favorite.users.profil[0].slug : '')}`}
                      target="_blank"
                      className="flex items-center gap-4 group flex-1"
                    >
                      <div className="relative">
                        <img
                          src={(favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil.length > 0 ? favorite.users.profil[0].photo_url : '/default-avatar.png')}
                          alt={`${(favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil.length > 0 ? favorite.users.profil[0].prenom : '')} ${(favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil.length > 0 && favorite.users.profil[0].nom ? favorite.users.profil[0].nom.charAt(0) + '.' : '')}`}
                          className="w-20 h-20 rounded-xl object-cover shadow-md group-hover:shadow-lg transition-all duration-300"
                        />
                        <div className="absolute bottom-0 right-0 transform translate-x-1/4 translate-y-1/4">
                          <OnlineStatusDot userId={favorite.users.id} />
                        </div>
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-800 group-hover:text-[#FF7A35] transition-colors">
                          {(favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil.length > 0 ? favorite.users.profil[0].prenom : '')} {(favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil.length > 0 && favorite.users.profil[0].nom ? favorite.users.profil[0].nom.charAt(0) + '.' : '')}
                        </h3>
                        <div className="flex items-center gap-2 text-sm text-gray-600 mt-1">
                          <MapPin className="h-4 w-4" />
                          {(favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil.length > 0 ? favorite.users.profil[0].ville : '')}, {(favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil.length > 0 ? favorite.users.profil[0].pays : '')}
                        </div>
                        <Rating
                          value={calculateAverageRating(favorite.users.reviews)}
                          readOnly
                          size="small"
                          precision={0.5}
                        />
                        <span className="ml-2 text-sm text-gray-500">
                          ({favorite.users.reviews?.length || 0} avis)
                        </span>
                      </div>
                    </Link>
                    <button
                      onClick={() => setProfilToDelete(favorite)}
                      className="p-2 hover:bg-red-50 rounded-lg transition-colors group"
                    >
                      <Heart className="h-5 w-5 text-red-500 fill-current group-hover:scale-110 transition-transform" />
                    </button>
                  </div>

                  <div className="space-y-3">
                    <p className="text-sm text-gray-600 line-clamp-1">
                      {(favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil && favorite.users.profil.length > 0 && favorite.users.profil[0].bio) ? (
                        <span dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil && favorite.users.profil.length > 0 ? favorite.users.profil[0].bio : '') }} />
                      ) : (
                        "Aucune description disponible"
                      )}
                    </p>

                    <div className="flex flex-wrap gap-2">
                      {favorite.users.identite_verifier && (
                        <Tooltip title="Identité vérifiée">
                          <Chip
                            icon={<Shield className="h-4 w-4" />}
                            label="Identité vérifiée"
                            size="small"
                            className="bg-green-50 text-green-700"
                          />
                        </Tooltip>
                      )}
                      {favorite.users.entreprise_verifier && (
                        <Tooltip title="Entreprise vérifiée">
                          <Chip
                            icon={<CheckCircle className="h-4 w-4" />}
                            label="Entreprise vérifiée"
                            size="small"
                            className="bg-blue-50 text-blue-700"
                          />
                        </Tooltip>
                      )}
                      {favorite.users.profil_verifier && (
                        <Tooltip title="Profil vérifié par JobPartiel">
                          <Chip
                            icon={<CheckCircle className="h-4 w-4" />}
                            label="Profil vérifié"
                            size="small"
                            className="bg-green-50 text-green-700"
                          />
                        </Tooltip>
                      )}
                      <Tooltip title="Type de profil">
                        <Chip
                          label={(favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil && favorite.users.profil.length > 0 && favorite.users.profil[0].type_de_profil === 'entreprise') ? 'Entreprise' : 'Particulier'}
                          size="small"
                          sx={{
                            backgroundColor: (favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil && favorite.users.profil.length > 0 && favorite.users.profil[0].type_de_profil === 'entreprise')
                              ? '#FFF8F3' // Background color du thème
                              : '#FFE4BA', // Accent color du thème
                            color: (favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil && favorite.users.profil.length > 0 && favorite.users.profil[0].type_de_profil === 'entreprise')
                              ? '#FF6B2C' // Primaire
                              : '#FF7A35', // Secondaire
                            '&:hover': {
                              backgroundColor: (favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil && favorite.users.profil.length > 0 && favorite.users.profil[0].type_de_profil === 'entreprise')
                                ? '#FFE4BA' // Accent color du thème
                                : '#FF965E' // Tertiaire
                            }
                          }}
                        />
                      </Tooltip>
                    </div>

                    <div className="text-sm text-gray-600">
                      <div className="flex items-center gap-2 mb-2">
                        <Mail className="h-4 w-4" />
                        <span className="text-gray-400">{favorite.users.email ? "*".repeat(favorite.users.email.length) : ""}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4" />
                        {(favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil && favorite.users.profil.length > 0 && favorite.users.profil[0].telephone_prive) ? (
                          <span className="text-orange-500">Le numéro est privé</span>
                        ) : (favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil && favorite.users.profil.length > 0 && favorite.users.profil[0].telephone) ? (
                          favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil && favorite.users.profil.length > 0 ? favorite.users.profil[0].telephone : ''
                        ) : (
                          <span className="text-gray-400">Aucun numéro renseigné</span>
                        )}
                      </div>
                    </div>

                    <div className="text-sm text-gray-600">
                      <strong>Zone d'intervention:</strong>
                      <br />
                      {addressDisplays[favorite.id] || "Chargement..."}
                    </div>

                    <div className="flex flex-col gap-2 mt-4">
                      <div className="text-sm text-gray-600">
                        <strong>Services:</strong>{' '}
                        {getServiceStatus(favorite.users.services)}
                      </div>
                      <div className="text-sm text-gray-600">
                        <strong>Dernière connexion:</strong>{' '}
                        {formatLastLogin(favorite.users.last_login)}
                      </div>
                    </div>

                    <div className="flex flex-col gap-2 mt-4">
                      <div className="flex flex-col gap-2">
                        <button
                          onClick={() => handleOpenMessage(favorite)}
                          className="w-full inline-flex items-center justify-center px-4 py-2 border-2 border-[#FF7A35] text-[#FF7A35] rounded-lg hover:bg-[#FF7A35] hover:text-white transition-colors"
                        >
                          <MessageCircle className="h-4 w-4 mr-2" />
                          Envoyer un message
                        </button>
                        <button
                          onClick={() => handleOpenReviewModal(favorite.users.id)}
                          className="w-full inline-flex items-center justify-center px-4 py-2 border-2 border-[#FF7A35] text-[#FF7A35] rounded-lg hover:bg-[#FF7A35] hover:text-white transition-colors"
                        >
                          <Star className="h-4 w-4 mr-2" />
                          Déposer un avis
                        </button>
                        <Link 
                          to={`/dashboard/profil/${(favorite.users && Array.isArray(favorite.users.profil) && favorite.users.profil.length > 0 ? favorite.users.profil[0].slug : '')}`}
                          target="_blank"
                          className="w-full inline-flex items-center justify-center px-4 py-2 bg-[#FF7A35] text-white rounded-lg hover:bg-[#FF6B2C] transition-colors"
                        >
                          Voir le profil complet
                        </Link>
                      </div>
                    </div>
                  </div>
                </ProfileCard>
              ))}
            </AnimatePresence>
          </div>

          {pagination.page < pagination.totalPages && (
            <div className="flex justify-center mt-8">
              <button
                onClick={loadMore}
                disabled={loadingMore}
                className="inline-flex items-center justify-center px-6 py-3 bg-[#FF7A35] text-white rounded-lg hover:bg-[#FF6B2C] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loadingMore ? (
                  <>
                    <CircularProgress size={20} className="text-white mr-2" />
                    Chargement...
                  </>
                ) : (
                  'Charger plus'
                )}
              </button>
            </div>
          )}
        </>
      )}

      <AnimatePresence>
        {profilToDelete && (
          <ModalPortal>
            <motion.div
              className="fixed inset-0 flex items-center justify-center p-4 z-[60]"
              onClick={() => setProfilToDelete(null)}
            >
              <motion.div
                transition={{ type: "spring", damping: 25, stiffness: 300 }}
                className="bg-white p-6 rounded-xl shadow-xl max-w-md w-full"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-gray-800">
                    Confirmer la suppression
                  </h3>
                  <p className="text-gray-600 mt-2">
                    Êtes-vous sûr de vouloir retirer {(profilToDelete && profilToDelete.users && Array.isArray(profilToDelete.users.profil) && profilToDelete.users.profil.length > 0 ? profilToDelete.users.profil[0].prenom : '')} {(profilToDelete && profilToDelete.users && Array.isArray(profilToDelete.users.profil) && profilToDelete.users.profil.length > 0 && profilToDelete.users.profil[0].nom ? profilToDelete.users.profil[0].nom.charAt(0) + '.' : '')} de vos favoris ?
                  </p>
                </div>

                <div className="flex justify-end gap-4">
                  <button
                    onClick={() => setProfilToDelete(null)}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    Annuler
                  </button>
                  <button
                    onClick={() => profilToDelete && removeFavorite(profilToDelete.users.id)}
                    className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                  >
                    Confirmer
                  </button>
                </div>
              </motion.div>
            </motion.div>
          </ModalPortal>
        )}
      </AnimatePresence>

      {/* Modal d'avis */}
      <ModalReview
        isOpen={isReviewModalOpen}
        onClose={handleCloseReviewModal}
        profilId={selectedProfilId}
        onReviewSubmitted={() => {
          handleCloseReviewModal();
        }}
      />
    </div>
  );
};

export default Favoris; 