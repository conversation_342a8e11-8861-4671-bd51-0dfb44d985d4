import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, Paper, Grid, CircularProgress, Chip, Alert, Card, CardContent, useMediaQuery, useTheme, Container } from '@mui/material';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';
import axios from 'axios';
import { API_CONFIG } from '@/config/api';
import { getCommonHeaders } from '@/utils/headers';
import { notify } from '@/components/Notification';
import logger from '@/utils/logger';
import ModerationFilterBar from '@/components/moderation/ModerationFilterBar';
import { 
  AlertTriangle, 
  CheckCircle, 
  Shield, 
  ShieldAlert, 
  ShieldCheck, 
  MessageSquareWarning,
  Eye,
  Swords,
  HeartCrack,
  Skull,
  MailWarning,
  Phone,
  MapPin,
  Ban
} from 'lucide-react';
import { styled } from '@mui/material/styles';

// Types pour les statistiques de modération
interface ModerationStats {
  total: number;
  unsafe: number;
  safeRate: number;
  contentTypes: Array<{
    content_type: string;
    count: number;
  }>;
  categories: {
    harassment: number;
    hateSpeech: number;
    sexualContent: number;
    violence: number;
    selfHarm: number;
    illegalActivity: number;
    spam: number;
    phoneSpam: number;
    addressSpam: number;
    unknownRisk: number;
  };
  timeline: Record<string, { total: number; unsafe: number }>;
  methods: {
    textIA: { total: number; unsafe: number };
    textNonIA: { total: number; unsafe: number };
    imageIA: { total: number; unsafe: number };
    other: { total: number; unsafe: number };
  };
  availableFilters?: {
    contentTypes: string[];
    categories: string[];
    methods: string[];
  };
  appliedFilters?: {
    startDate: string | null;
    endDate: string | null;
    contentType: string | null;
    category: string | null;
    method: string | null;
    isSafe: boolean | null;
  };
}

// Interface pour les filtres
interface ModerationFilters {
  startDate: string | null;
  endDate: string | null;
  contentType: string | null;
  category: string | null;
  method: string | null;
  isSafe: boolean | null;
}

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

// Styles personnalisés pour les composants
const StyledPaper = styled(Paper)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  padding: theme.spacing(3),
  height: '100%',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const PageTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.75rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: theme.spacing(1),
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: COLORS.primary,
    borderRadius: '2px',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.5rem',
  },
}));

const MetricCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-start',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.25rem',
  fontWeight: 600,
  marginBottom: theme.spacing(2),
  color: '#2D3748',
}));

interface CategoryCardProps {
  color?: string;
  active?: boolean;
}

const CategoryCard = styled(Card, {
  shouldForwardProp: (prop) => prop !== 'active' && prop !== 'color',
})<CategoryCardProps>(({ color = COLORS.neutral, active = false }) => ({
  borderRadius: '16px',
  border: `1px solid ${active ? color : 'rgba(0,0,0,0.08)'}`,
  backgroundColor: active ? `${color}10` : COLORS.white,
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 16px rgba(0,0,0,0.08)',
  },
  position: 'relative',
  overflow: 'visible',
}));

interface IconBoxProps {
  color?: string;
}

const IconBox = styled(Box)<IconBoxProps>(({ theme, color = COLORS.primary }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: COLORS.white,
  borderRadius: '50%',
  padding: theme.spacing(1),
  border: `2px solid ${color}`,
  boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
  position: 'absolute',
  top: '-15px',
  right: '20px',
  color: color,
}));

const ModerationStatsPage: React.FC = () => {
  const [stats, setStats] = useState<ModerationStats | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ModerationFilters>({
    startDate: null,
    endDate: null,
    contentType: null,
    category: null,
    method: null,
    isSafe: null
  });
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Couleurs pour les graphiques
  const CATEGORY_COLORS: Record<string, string> = {
    'Harcèlement': '#FF5252',
    'Discours haineux': '#FF7043',
    'Contenu sexuel': '#AB47BC',
    'Violence': '#5C6BC0',
    'Auto-mutilation': '#26A69A',
    'Activité illégale': '#FFA726',
    'Spam': '#29B6F6',
    'Numéro de téléphone': '#EC407A',
    'Adresse postale': '#66BB6A'
  };

  // Palette de couleurs JobPartiel pour les colonnes
  const CONTENT_TYPE_COLORS = [
    COLORS.primary,
    COLORS.secondary,
    COLORS.tertiary,
    COLORS.accent,
    '#4CAF50',
    '#2196F3',
    '#FFC107',
    '#9C27B0',
    '#FF5722',
    '#607D8B',
    '#E91E63',
    '#795548',
    '#FFA000',
    '#0288D1',
    '#7B1FA2',
    '#E65100',
    '#FF8F00',
    '#388E3C',
    '#EF6C00',
    '#FF6B2C'
  ];

  // Charger les statistiques avec filtres
  const fetchStats = useCallback(async (appliedFilters: ModerationFilters = filters) => {
    try {
      setLoading(true);
      setError(null);

      // Construire l'URL avec les paramètres de filtre
      let url = `${API_CONFIG.baseURL}/api/content-moderation/stats`;
      const queryParams: string[] = [];
      
      if (appliedFilters.startDate) {
        queryParams.push(`startDate=${encodeURIComponent(appliedFilters.startDate)}`);
      }
      
      if (appliedFilters.endDate) {
        queryParams.push(`endDate=${encodeURIComponent(appliedFilters.endDate)}`);
      }
      
      if (appliedFilters.contentType) {
        queryParams.push(`contentType=${encodeURIComponent(appliedFilters.contentType)}`);
      }
      
      if (appliedFilters.category) {
        queryParams.push(`category=${encodeURIComponent(appliedFilters.category)}`);
      }
      
      if (appliedFilters.method) {
        queryParams.push(`method=${encodeURIComponent(appliedFilters.method)}`);
      }
      
      if (appliedFilters.isSafe !== null) {
        queryParams.push(`isSafe=${appliedFilters.isSafe}`);
      }
      
      if (queryParams.length > 0) {
        url = `${url}?${queryParams.join('&')}`;
      }

      const headers = await getCommonHeaders();
      const response = await axios.get(url, { headers, withCredentials: true });

      if (response.data.success) {
        setStats(response.data.data);
      } else {
        throw new Error(response.data.message || 'Erreur lors de la récupération des statistiques');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Erreur lors de la récupération des statistiques';
      setError(errorMessage);
      notify(errorMessage, 'error');
      logger.error('Erreur lors de la récupération des statistiques de modération:', err);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Charger les statistiques initiales
  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  // Gérer le changement de filtres
  const handleFilterChange = (newFilters: ModerationFilters) => {
    setFilters(newFilters);
    fetchStats(newFilters);
  };

  // Préparer les données pour les graphiques
  const prepareTimelineData = () => {
    if (!stats?.timeline) return [];

    return Object.entries(stats.timeline)
      .map(([date, data]) => ({
        date,
        total: data.total,
        unsafe: data.unsafe,
        safe: data.total - data.unsafe
      }))
      .sort((a, b) => a.date.localeCompare(b.date))
      .slice(-30); // Limiter aux 30 derniers jours
  };

  const prepareCategoryData = () => {
    if (!stats?.categories) return [];

    // Filtrer et traiter les données pour afficher seulement les catégories significatives
    const filteredData = Object.entries(stats.categories)
      .map(([category, count]) => ({
        name: formatCategoryName(category),
        value: count,
        category // Conserver la clé originale pour l'association des couleurs
      }))
      .filter(item => item.value > 0)
      .sort((a, b) => b.value - a.value);
    
    // Si trop de petites catégories, regrouper les plus petites dans "Autres"
    if (filteredData.length > 6) {
      const mainCategories = filteredData.slice(0, 5);
      const otherCategories = filteredData.slice(5);
      
      const otherSum = otherCategories.reduce((sum, item) => sum + item.value, 0);
      
      if (otherSum > 0) {
        mainCategories.push({
          name: 'Autres',
          value: otherSum,
          category: 'other'
        });
      }
      
      return mainCategories;
    }
    
    return filteredData;
  };

  // Générer les données pour le graphique avec une lettre courte
  const prepareContentTypeDataWithLabels = () => {
    if (!stats?.contentTypes) return [];
    return stats.contentTypes.map((type, idx) => ({
      key: String.fromCharCode(65 + idx), // A, B, C, ...
      name: formatContentType(type.content_type),
      value: type.count,
      color: CONTENT_TYPE_COLORS[idx % CONTENT_TYPE_COLORS.length]
    }));
  };

  const prepareMethodData = () => {
    if (!stats?.methods) return [];

    return [
      { name: 'Texte (IA)', total: stats.methods.textIA.total, unsafe: stats.methods.textIA.unsafe },
      { name: 'Texte (SANS IA)', total: stats.methods.textNonIA.total, unsafe: stats.methods.textNonIA.unsafe },
      { name: 'Image (IA)', total: stats.methods.imageIA.total, unsafe: stats.methods.imageIA.unsafe },
      { name: 'Autre', total: stats.methods.other.total, unsafe: stats.methods.other.unsafe }
    ];
  };

  // Fonctions utilitaires
  const formatCategoryName = (category: string): string => {
    const categoryMap: Record<string, string> = {
      harassment: 'Harcèlement',
      hateSpeech: 'Discours haineux',
      sexualContent: 'Contenu sexuel',
      violence: 'Violence',
      selfHarm: 'Auto-mutilation',
      illegalActivity: 'Activité illégale',
      spam: 'Spam',
      phoneSpam: 'Numéro de téléphone',
      addressSpam: 'Adresse postale',
      unknownRisk: 'Risque inconnu'
    };

    return categoryMap[category] || category;
  };

  const formatContentType = (type: string): string => {
    const typeMap: Record<string, string> = {
      mission: 'Mission',
      comment: 'Commentaire',
      profile: 'Biographie',
      titre_service: 'Titre de service',
      description_service: 'Description de service',
      gallery_name: 'Nom de galerie',
      gallery_description: 'Description de galerie',
      mission_title: 'Titre de mission',
      mission_description: 'Description de mission',
      review: 'Avis',
      gallery: 'Image de galerie',
      gallery_cover: 'Image de couverture',
      featured: 'Image featured profil',
      mission_assistant: 'Image assistant mission',
      avatar: 'Photo de profil',
      profile_picture: 'Photo de profil',
      banner_picture: 'Bannière de profil'
    };
    return typeMap[type] || type;
  };

  // Rendu des statistiques
  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '80vh',
          gap: 2
        }}
      >
        <CircularProgress 
          size={60} 
          thickness={4} 
          sx={{ color: COLORS.primary }}
        />
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ mt: 2, fontWeight: 500 }}
        >
          Chargement des statistiques...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert 
          severity="error" 
          sx={{ 
            mb: 2, 
            borderRadius: '12px',
            '& .MuiAlert-icon': { color: COLORS.error } 
          }}
        >
          {error}
        </Alert>
        <Typography variant="body1" sx={{ mt: 2 }}>
          Impossible de charger les statistiques de modération. Veuillez réessayer plus tard.
        </Typography>
      </Container>
    );
  }

  if (!stats) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert 
          severity="warning"
          sx={{ 
            borderRadius: '12px',
            '& .MuiAlert-icon': { color: COLORS.warning } 
          }}
        >
          Aucune donnée de modération disponible.
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: { xs: 3, md: 5 } }}>
      <PageTitle sx={{ mb: 3 }}>
        Statistiques de modération
      </PageTitle>
      <Typography 
        variant="subtitle1" 
        color="text.secondary" 
        sx={{ mb: 3, maxWidth: '800px' }}
      >
        Vue d'ensemble des contenus modérés et des tendances détectées
      </Typography>

      {/* Barre de filtres */}
      <ModerationFilterBar 
        availableFilters={stats?.availableFilters}
        appliedFilters={stats?.appliedFilters}
        onFilterChange={handleFilterChange}
      />

      {/* Résumé des statistiques */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <MetricCard
            elevation={0}
            sx={{
              bgcolor: COLORS.background,
              border: `1px solid ${COLORS.borderColor}`
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Shield size={24} color={COLORS.primary} />
              <Typography variant="subtitle1" sx={{ ml: 1, fontWeight: 600, color: COLORS.primary }}>
                Total modéré
              </Typography>
            </Box>
            <Typography variant="h3" sx={{ fontWeight: 700, color: '#333', mb: 1 }}>
              {stats.total.toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Contenus analysés
            </Typography>
          </MetricCard>
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <MetricCard
            elevation={0}
            sx={{
              bgcolor: '#FEF2F2',
              border: '1px solid rgba(239, 68, 68, 0.2)'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <ShieldAlert size={24} color={COLORS.error} />
              <Typography variant="subtitle1" sx={{ ml: 1, fontWeight: 600, color: COLORS.error }}>
                Contenus bloqués
              </Typography>
            </Box>
            <Typography variant="h3" sx={{ fontWeight: 700, color: '#333', mb: 1 }}>
              {stats.unsafe.toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {((stats.unsafe / stats.total) * 100).toFixed(1)}% du total
            </Typography>
          </MetricCard>
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <MetricCard
            elevation={0}
            sx={{
              bgcolor: '#F0FDF4',
              border: '1px solid rgba(34, 197, 94, 0.2)'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <ShieldCheck size={24} color={COLORS.success} />
              <Typography variant="subtitle1" sx={{ ml: 1, fontWeight: 600, color: COLORS.success }}>
                Taux de sécurité
              </Typography>
            </Box>
            <Typography variant="h3" sx={{ fontWeight: 700, color: '#333', mb: 1 }}>
              {stats.safeRate}%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Contenus sûrs
            </Typography>
          </MetricCard>
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <MetricCard
            elevation={0}
            sx={{
              bgcolor: '#EFF6FF',
              border: '1px solid rgba(59, 130, 246, 0.2)'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <AlertTriangle size={24} color={COLORS.info} />
              <Typography variant="subtitle1" sx={{ ml: 1, fontWeight: 600, color: COLORS.info }}>
                Catégorie principale
              </Typography>
            </Box>
            <Typography variant="h5" sx={{ fontWeight: 700, color: '#333', mb: 1 }}>
              {prepareCategoryData()[0]?.name || 'Aucune'}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {prepareCategoryData()[0]?.value || 0} détections
            </Typography>
          </MetricCard>
        </Grid>
      </Grid>

      {/* Graphiques */}
      <Grid container spacing={3}>
        {/* Évolution dans le temps */}
        <Grid size={{ xs: 12, lg: 8 }}>
          <StyledPaper>
            <SectionTitle>
              Évolution de la modération (30 derniers jours)
            </SectionTitle>
            <Box sx={{ height: 350, pt: 2 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={prepareTimelineData()}
                  margin={{ top: 5, right: 30, left: isMobile ? 5 : 20, bottom: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.06)" />
                  <XAxis 
                    dataKey="date" 
                    tick={{ fontSize: isMobile ? 10 : 12 }}
                    tickMargin={10}
                  />
                  <YAxis 
                    tick={{ fontSize: isMobile ? 10 : 12 }}
                    tickMargin={10}
                  />
                  <Tooltip 
                    contentStyle={{
                      backgroundColor: '#FFF',
                      border: `1px solid ${COLORS.borderColor}`,
                      borderRadius: '8px',
                      boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                    }}
                  />
                  <Legend 
                    iconType="circle" 
                    iconSize={8}
                    wrapperStyle={{ paddingTop: 15 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="total" 
                    name="Total" 
                    stroke={COLORS.info} 
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 6, stroke: 'white', strokeWidth: 2 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="unsafe" 
                    name="Bloqués" 
                    stroke={COLORS.error} 
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 6, stroke: 'white', strokeWidth: 2 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="safe" 
                    name="Sûrs" 
                    stroke={COLORS.success} 
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 6, stroke: 'white', strokeWidth: 2 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </Box>
          </StyledPaper>
        </Grid>

        {/* Répartition par catégorie */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <StyledPaper>
            <SectionTitle>
              Répartition par catégorie
            </SectionTitle>
            <Box sx={{ height: 350, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
                  <Pie
                    data={prepareCategoryData()}
                    cx="50%"
                    cy="42%"
                    labelLine={true}
                    outerRadius={isMobile ? 80 : 100}
                    fill="#8884d8"
                    dataKey="value"
                    nameKey="name"
                    paddingAngle={2}
                    label={({ percent, x, y, cx }) => {
                      const percentage = (percent * 100).toFixed(0);
                      
                      if (percent < 0.05) return null; // Ne pas afficher les petits pourcentages
                      
                      return (
                        <text
                          x={x}
                          y={y}
                          fill="#333"
                          textAnchor={x > Number(cx) ? 'start' : 'end'}
                          dominantBaseline="central"
                          fontWeight="bold"
                          fontSize={isMobile ? "10px" : "12px"}
                        >
                          {`${percentage}%`}
                        </text>
                      );
                    }}
                  >
                    {prepareCategoryData().map((entry, index) => (
                      <Cell 
                        key={`cell-${index}`} 
                        fill={CATEGORY_COLORS[entry.name] || Object.values(COLORS)[index % Object.values(COLORS).length]} 
                        stroke="#fff"
                        strokeWidth={2}
                      />
                    ))}
                  </Pie>
                  <Tooltip 
                    contentStyle={{
                      backgroundColor: '#FFF',
                      border: `1px solid ${COLORS.borderColor}`,
                      borderRadius: '8px',
                      fontSize: '0.8rem',
                      padding: '10px',
                      boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                    }}
                    formatter={(value: number, name: string) => [
                      `${value} détections (${((value / (stats?.unsafe || 1)) * 100).toFixed(1)}%)`,
                      name
                    ]}
                  />
                  <Legend 
                    verticalAlign="bottom" 
                    height={60}
                    layout="horizontal"
                    wrapperStyle={{
                      fontSize: isMobile ? '0.7rem' : '0.8rem',
                      paddingTop: '20px',
                      width: '100%'
                    }}
                    iconSize={8}
                    iconType="circle"
                    formatter={(value) => {
                      if (value.length > (isMobile ? 10 : 15)) {
                        return `${value.substring(0, isMobile ? 8 : 12)}...`;
                      }
                      return value;
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </Box>
          </StyledPaper>
        </Grid>

        {/* Répartition par type de contenu */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <StyledPaper>
            <SectionTitle>
              Répartition par type de contenu
            </SectionTitle>
            <Box sx={{ height: 320, pt: 2 }}>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={prepareContentTypeDataWithLabels()}
                  margin={{ top: 5, right: 20, left: isMobile ? 5 : 20, bottom: 20 }}
                  barSize={isMobile ? 30 : 40}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.06)" vertical={false} />
                  <XAxis 
                    dataKey="key" 
                    tick={{ fontSize: 14, dy: 10, textAnchor: 'middle' }}
                    tickMargin={10}
                    axisLine={false}
                  />
                  <YAxis 
                    tick={{ fontSize: isMobile ? 10 : 12 }}
                    tickMargin={10}
                    axisLine={false}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#FFF',
                      border: `1px solid ${COLORS.borderColor}`,
                      borderRadius: '8px',
                      boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                    }}
                    formatter={(value: number, name: string, props: any) => [
                      value,
                      prepareContentTypeDataWithLabels().find(d => d.key === props.payload.key)?.name || ''
                    ]}
                  />
                  <Bar 
                    dataKey="value" 
                    name="Nombre" 
                    radius={[4, 4, 0, 0]}
                  >
                    {prepareContentTypeDataWithLabels().map((entry, idx) => (
                      <Cell key={`cell-${idx}`} fill={entry.color} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </Box>
            {/* Légende horizontale sous le graphique */}
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, justifyContent: 'center', mt: 2 }}>
              {prepareContentTypeDataWithLabels().map((entry, idx) => (
                <Box key={entry.key} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Box sx={{ width: 16, height: 16, bgcolor: entry.color, borderRadius: '4px', mr: 0.5 }} />
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>{entry.key}</Typography>
                  <Typography variant="body2" color="text.secondary">= {entry.name}</Typography>
                </Box>
              ))}
            </Box>
          </StyledPaper>
        </Grid>

        {/* Efficacité des méthodes de détection */}
        <Grid size={{ xs: 12, md: 12, lg: 8 }}>
          <StyledPaper>
            <SectionTitle>
              Efficacité des méthodes de détection
            </SectionTitle>
            <Box sx={{ height: 320, pt: 2 }}>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={prepareMethodData()}
                  margin={{ top: 5, right: 30, left: isMobile ? 5 : 20, bottom: 20 }}
                  barSize={isMobile ? 25 : 40}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.06)" />
                  <XAxis 
                    dataKey="name" 
                    tick={{ fontSize: isMobile ? 10 : 12 }}
                    tickMargin={10}
                  />
                  <YAxis 
                    tick={{ fontSize: isMobile ? 10 : 12 }}
                    tickMargin={10}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#FFF',
                      border: `1px solid ${COLORS.borderColor}`,
                      borderRadius: '8px',
                      boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                    }}
                  />
                  <Legend 
                    iconType="circle" 
                    iconSize={8}
                    wrapperStyle={{ paddingTop: 15 }}
                  />
                  <Bar 
                    dataKey="total" 
                    name="Total analysé" 
                    fill={COLORS.info} 
                    radius={[4, 4, 0, 0]}
                  />
                  <Bar 
                    dataKey="unsafe" 
                    name="Contenus bloqués" 
                    fill={COLORS.error}
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </Box>
          </StyledPaper>
        </Grid>

        {/* Détail des catégories */}
        <Grid size={{ xs: 12 }}>
          <StyledPaper>
            <SectionTitle>
              Détail des catégories de modération
            </SectionTitle>
            <Grid container spacing={3}>
              {Object.entries(stats.categories).map(([category, count]) => {
                // Obtenir la couleur correspondante
                const categoryName = formatCategoryName(category);
                const categoryColor = CATEGORY_COLORS[categoryName] || COLORS.neutral;
                
                // Sélectionner une icône en fonction de la catégorie
                let CategoryIcon;
                switch(category) {
                  case 'harassment':
                    CategoryIcon = <MessageSquareWarning size={24} />;
                    break;
                  case 'hateSpeech':
                    CategoryIcon = <Ban size={24} />;
                    break;
                  case 'sexualContent':
                    CategoryIcon = <Eye size={24} />;
                    break;
                  case 'violence':
                    CategoryIcon = <Swords size={24} />;
                    break;
                  case 'selfHarm':
                    CategoryIcon = <HeartCrack size={24} />;
                    break;
                  case 'illegalActivity':
                    CategoryIcon = <Skull size={24} />;
                    break;
                  case 'spam':
                    CategoryIcon = <MailWarning size={24} />;
                    break;
                  case 'phoneSpam':
                    CategoryIcon = <Phone size={24} />;
                    break;
                  case 'addressSpam':
                    CategoryIcon = <MapPin size={24} />;
                    break;
                  case 'unknownRisk':
                    CategoryIcon = <AlertTriangle size={24} />;
                    break;
                  default:
                    CategoryIcon = <AlertTriangle size={24} />;
                }
                
                return (
                  <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={category}>
                    <CategoryCard 
                      elevation={0}
                      color={categoryColor}
                      active={count > 0}
                      sx={{
                        p: 2
                      }}
                    >
                      <IconBox color={count > 0 ? categoryColor : '#E0E0E0'}>
                        {CategoryIcon}
                      </IconBox>
                      
                      <CardContent sx={{ p: { xs: 2.5, md: 3 } }}>
                        <Typography variant="subtitle1" sx={{ 
                          fontWeight: 600, 
                          mb: 0.5,
                          color: count > 0 ? categoryColor : '#666'
                        }}>
                          {categoryName}
                        </Typography>
                        
                        <Typography variant="h4" component="div" sx={{ 
                          fontWeight: 700, 
                          mt: 1,
                          color: count > 0 ? categoryColor : '#999',
                          fontSize: { xs: '1.75rem', md: '2rem' }
                        }}>
                          {count}
                        </Typography>
                        
                        {count > 0 ? (
                          <Box>
                            <Box sx={{ mt: 1, display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 1 }}>
                              <Typography variant="body2" sx={{ 
                                color: '#666', 
                                fontWeight: 500
                              }}>
                                {((count / stats.unsafe) * 100).toFixed(1)}% des bloqués
                              </Typography>
                              
                              {count > stats.unsafe * 0.1 && (
                                <Chip
                                  label={count > stats.unsafe * 0.3 ? "Priorité haute" : "Significatif"}
                                  size="small"
                                  sx={{ 
                                    height: 20, 
                                    backgroundColor: count > stats.unsafe * 0.3 ? `${categoryColor}20` : '#f5f5f5',
                                    color: count > stats.unsafe * 0.3 ? categoryColor : '#666',
                                    fontWeight: 500,
                                    fontSize: '0.7rem',
                                    borderRadius: '10px'
                                  }}
                                />
                              )}
                            </Box>
                            
                            <Box sx={{ 
                              position: 'relative', 
                              height: 8, 
                              width: '100%', 
                              bgcolor: '#f5f5f5', 
                              borderRadius: 4, 
                              overflow: 'hidden',
                              mt: 0.5
                            }}>
                              <Box 
                                sx={{ 
                                  position: 'absolute',
                                  top: 0,
                                  left: 0,
                                  height: '100%', 
                                  width: `${Math.min(100, (count / stats.unsafe) * 100)}%`, 
                                  background: `linear-gradient(90deg, ${categoryColor}70 0%, ${categoryColor} 100%)`,
                                  borderRadius: 4,
                                  transition: 'width 1s ease-in-out'
                                }} 
                              />
                            </Box>
                          </Box>
                        ) : (
                          <Box sx={{ 
                            mt: 1,
                            display: 'flex', 
                            alignItems: 'center',
                            p: 1
                          }}>
                            <CheckCircle size={16} color="#22C55E" />
                            <Typography variant="body2" sx={{ 
                              ml: 0.5,
                              color: '#22C55E', 
                              fontStyle: 'italic', 
                              fontWeight: 500 
                            }}>
                              Aucune détection
                            </Typography>
                          </Box>
                        )}
                      </CardContent>
                    </CategoryCard>
                  </Grid>
                );
              })}
            </Grid>
          </StyledPaper>
        </Grid>
      </Grid>

      {/* Informations supplémentaires */}
      <Box sx={{ mt: 5 }}>
        <Alert 
          severity="info" 
          sx={{ 
            mb: 3,
            p: 2,
            borderRadius: '12px',
            '& .MuiAlert-icon': { color: COLORS.info },
            boxShadow: '0 4px 12px rgba(0,0,0,0.03)',
          }}
        >
          <Box>
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 0.5 }}>
              À propos des statistiques de modération
            </Typography>
            <Typography variant="body2">
              Ces statistiques sont basées sur les données de modération collectées par le système. 
              Elles sont mises à jour toutes les 5 minutes. Les contenus bloqués sont ceux qui ont été 
              détectés comme inappropriés par le système de modération automatique.
              {stats.appliedFilters && Object.values(stats.appliedFilters).some(value => value !== null) && (
                <Box component="span" sx={{ display: 'block', mt: 1, fontStyle: 'italic' }}>
                  Attention : Des filtres sont actuellement appliqués à ces statistiques. Les données affichées ne représentent qu'une partie des activités de modération.
                </Box>
              )}
            </Typography>
          </Box>
        </Alert>
      </Box>
    </Container>
  );
};

export default ModerationStatsPage;
