import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import { redis } from '../config/redis';
import logger from '../utils/logger';
import { FileRequest } from '../middleware/fileValidation';
import { uploadMissionPhoto, cleanupOldMissionPhotos, DEFAULT_AVATAR_URL, extractFilePathFromUrl } from '../services/storage';
import { DistanceService } from '../services/distanceService';
import { ParsedQs } from 'qs';
import { rewardReferral } from './jobi';
import { logUserActivity, getIpFromRequest } from '../utils/activityLogger';
import { canSendEmail, sendEmailWithPreferenceCheck } from '../utils/emailPreferences';
import { getUserSubscriptionLimits } from '../routes/configSubscriptions';
import contentModerationService, { ContentToModerate } from '../services/contentModerationService';
import { decryptProfilDataAsync, decryptUserDataAsync, hashEmail, decryptDataAsync } from '../utils/encryption';
import {
  sendNewMissionEmail,
  sendMissionAutoClosureEmail,
  sendNewProposalEmail,
  sendCounterOfferEmail,
  sendProposalAcceptedEmail,
  sendProposalRejectedEmail,
  sendJobbeurCounterOfferEmail,
  sendContactInfoEmail,
  sendMissionCancelledEmail
} from '../services/emailService';

// Fonction pour convertir le HTML en texte brut
const stripHtml = (html: string) => {
  return html.replace(/<[^>]*>/g, '');
};

interface TimeSlot {
  start: string;
  end: string;
  date?: string;
}

interface UserProfile {
  user_id: string;
  nom: string;
  prenom: string;
  type_de_profil: string;
  photo_url: string | null;
  statut_entreprise: string;
  profil_verifier: boolean;
}

interface ParentComment {
  mission_id: string;
  user_id: string;
  user: Array<{
    id: string;
    user_profil: Array<{
      nom: string | null;
      prenom: string | null;
    }>;
  }>;
}

// Constantes pour la durée du cache
const CACHE_DURATION = 300; // 5 minutes en secondes

// Fonction utilitaire pour générer la clé de cache
export const generateCacheKey = (prefix: string, params: any): string => {
  return `${prefix}:${JSON.stringify(params)}`;
};

// Fonction utilitaire pour convertir les statuts
const convertStatusToArray = (status: string | string[] | any): string[] => {
  return typeof status === 'string' ? status.split(',') : Array.isArray(status) ? status : [status];
};

// Fonction utilitaire pour invalider le cache lié aux missions
export const invalidateMissionCache = async (missionId?: string, userId?: string) => {
  try {
    const keysToDelete: string[] = [];

    // Patterns de clés à invalider
    const patterns = [
      'missions:*',
      'all_missions:*',
      'matching_missions:*',
      'available_missions:*'
    ];

    // Si userId est fourni, ajouter les patterns spécifiques à l'utilisateur
    if (userId) {
      patterns.push(
        `sent_proposals:*"userId":"${userId}"*`,
        `sent_proposal_stats:*"userId":"${userId}"*`,
        `user_propositions:*"userId":"${userId}"*`,
        `received_proposals:*"userId":"${userId}"*`,
        `received_proposal_stats:*"userId":"${userId}"*`
      );
    }

    // Si missionId est fourni, ajouter les patterns spécifiques à la mission
    if (missionId) {
      patterns.push(
        `mission_details:*"missionId":"${missionId}"*`,
        `mission_comments:*"missionId":"${missionId}"*`
      );
    }

    // Si missionId et userId sont fournis, ajouter le pattern spécifique
    if (missionId && userId) {
      patterns.push(`user_proposal_for_mission:*"userId":"${userId}"*"missionId":"${missionId}"*`);
    }

    // Récupérer les clés pour chaque pattern
    for (const pattern of patterns) {
      const keys = await redis.keys(pattern);
      keysToDelete.push(...keys);
    }

    // Supprimer les doublons
    const uniqueKeysToDelete = [...new Set(keysToDelete)];

    // Supprimer les clés
    if (uniqueKeysToDelete.length > 0) {
      await redis.del(...uniqueKeysToDelete);
      logger.info(`Cache invalidé: ${uniqueKeysToDelete.length} clés supprimées pour missionId=${missionId}, userId=${userId}`);
    }

    return true;
  } catch (error) {
    logger.error('Erreur lors de l\'invalidation du cache:', error);
    return false;
  }
};

// Fonction pour invalider le cache d'une proposition
export const invalidatePropositionCache = async (missionId: string, proposalId: string) => {
  const cacheKey = `proposition:${missionId}:${proposalId}`;
  await redis.del(cacheKey);
  logger.info(`Cache invalidé pour la proposition: ${cacheKey}`);
};

export const missionController = {
  // Créer une nouvelle mission
  createMission: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const {
        category_id,
        subcategory_id,
        titre,
        description,
        budget,
        budget_defini,
        duree_estimee,
        date_mission,
        has_time_preference,
        time_slots,
        adresse,
        code_postal,
        ville,
        pays,
        payment_method,
        intervention_zone,
        is_urgent
      } = req.body;

      let formattedDate = null;

      // Vérifier et formater la date uniquement si elle est fournie ou si has_time_preference est true
      if (has_time_preference) {
        if (!date_mission) {
          res.status(400).json({
            error: 'La date de mission est requise avec des préférences horaires',
            details: 'date_mission field is required when has_time_preference is true'
          });
        }

        formattedDate = new Date(date_mission);
        if (isNaN(formattedDate.getTime())) {
          res.status(400).json({
            error: 'Format de date invalide',
            details: 'date_mission must be a valid date'
          });
        }
      }

      // Vérifier la cohérence des créneaux horaires
      if (has_time_preference && (!time_slots || time_slots.length === 0)) {
        res.status(400).json({
          error: 'Créneaux horaires manquants',
          details: 'Au moins un créneau horaire est requis lorsque des préférences horaires sont définies'
        });
      }

      // Créer la mission
      const { data: mission, error } = await supabase
        .from('user_missions')
        .insert([{
          user_id: userId,
          category_id,
          subcategory_id,
          titre,
          description,
          budget,
          budget_defini,
          duree_estimee,
          date_mission: formattedDate ? formattedDate.toISOString() : null,
          time_slots: has_time_preference ? time_slots : [],
          has_time_preference,
          adresse,
          code_postal,
          ville,
          pays,
          intervention_zone,
          statut: 'en_cours',
          payment_method,
          is_urgent,
          is_closed: false,
          date_creation: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        logger.error('Erreur lors de la création de la mission:', error);
        res.status(400).json({
          error: 'Erreur lors de la création de la mission',
          details: error.message
        });
      }

      // Invalider le cache après la création de la mission
      await invalidateMissionCache(mission.id, userId);
      logger.info('Cache invalidé après création de mission:', { missionId: mission.id, userId });

      // Journalisation de l'action utilisateur
      if (userId) {
        await logUserActivity(
          userId,
          'mission_create',
          mission.id,
          'mission',
          {
            titre: mission.titre,
            category_id: mission.category_id,
            subcategory_id: mission.subcategory_id,
            ville: mission.ville,
            is_urgent: mission.is_urgent
          },
          getIpFromRequest(req)
        );
      }

      // Après la création réussie de la mission, notifier les jobbeurs correspondants
      try {
        // Récupérer les jobbeurs correspondants
        const matchingJobbers = await DistanceService.findMatchingJobbers(
          mission.id,
          category_id,
          subcategory_id,
          {
            lat: intervention_zone?.center[0] || 0,
            lng: intervention_zone?.center[1] || 0
          }
        );

        // Récupérer les emails des jobbeurs
        const { data: jobbers, error: jobbersError } = await supabase
          .from('users')
          .select('email, id')
          .in('id', matchingJobbers)
          .eq('profil_actif', true)

        if (jobbersError) {
          logger.error('Erreur lors de la récupération des emails des jobbeurs:', jobbersError);
        } else if (jobbers && jobbers.length > 0) {
          // Déchiffrer les données des jobbeurs
          const decryptedJobbers = await Promise.all(jobbers.map(jobber => decryptUserDataAsync(jobber)));
          // Construire l'URL de la mission
          const missionUrl = `${process.env.FRONTEND_URL}/dashboard/missions/${mission.id}`;

          // Ajouter les emails à la file d'attente et créer des notifications internes
          for (let i = 0; i < jobbers.length; i++) {
            const jobber = jobbers[i];
            const decryptedJobber = decryptedJobbers[i];

            // Vérifier les préférences d'email avant d'envoyer la notification
            const shouldSendEmail = await canSendEmail(jobber.id, 'missions');

            if (shouldSendEmail) {
              // Envoyer l'email
              await sendNewMissionEmail(decryptedJobber.email, {
                missionTitle: titre,
                missionDescription: description,
                missionBudget: budget || 0,
                missionLocation: `${ville}, ${code_postal}`,
                missionDate: formattedDate?.toISOString() || null,
                timeSlots: has_time_preference ? time_slots : [],
                missionUrl,
                budget_defini,
                payment_method,
                category_id
              });
              logger.info(`Email de nouvelle mission envoyé à ${decryptedJobber.email}`, { userId: jobber.id });
            } else {
              logger.info(`Email de nouvelle mission non envoyé à ${decryptedJobber.email} en raison des préférences utilisateur`, { userId: jobber.id });
            }

            // Créer une notification interne
            const { error: notifError } = await supabase
              .from('user_notifications')
              .insert([{
                user_id: jobber.id,
                type: 'mission',
                title: 'Nouvelle mission disponible',
                content: `Une nouvelle mission "${titre}" est disponible dans votre zone d'intervention.`,
                link: `/dashboard/missions/${mission.id}`,
                is_read: false,
                is_archived: false
              }]);

            if (notifError) {
              logger.error('Erreur lors de la création de la notification interne pour le jobbeur:', notifError);
            }
          }

          logger.info('Emails et notifications internes envoyés aux jobbeurs correspondants', {
            missionId: mission.id,
            jobberCount: jobbers.length
          });
        }

        // Vérifier et fermer les missions automatiquement de plus de 60 jours
        const sixtyDaysAgo = new Date();
        sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

        // Récupérer les missions à fermer
        const { data: missionsToClose, error: missionsError } = await supabase
          .from('user_missions')
          .select('id, user_id, titre, description, budget, budget_defini, adresse, ville, code_postal, date_mission, category_id, payment_method')
          .eq('is_closed', false)
          .in('statut', ['en_attente', 'en_cours'])
          .lt('date_creation', sixtyDaysAgo.toISOString());

        logger.info('Recherche de missions à fermer:', missionsToClose);

        if (!missionsError && missionsToClose && missionsToClose.length > 0) {
          // Fermer les missions
          const { error: updateError } = await supabase
            .from('user_missions')
            .update({
              is_closed: true,
              statut: 'terminee',
              updated_at: new Date().toISOString()
            })
            .in('id', missionsToClose.map(m => m.id));

          if (!updateError) {
            // Invalider le cache pour toutes les missions fermées
            for (const closedMission of missionsToClose) {
              await invalidateMissionCache(closedMission.id, closedMission.user_id);
            }

            // Récupérer les emails des utilisateurs pour les missions fermées
            const userIds = [...new Set(missionsToClose.map(m => m.user_id))];
            const { data: users, error: usersError } = await supabase
              .from('users')
              .select('id, email')
              .in('id', userIds);

            if (!usersError && users) {
              // Déchiffrer les données des utilisateurs
              const decryptedUsers = await Promise.all(users.map(user => decryptUserDataAsync(user)));
              // Envoyer les emails via la file d'attente et retirer les Jobis
              for (const mission of missionsToClose) {
                const user = users.find(u => u.id === mission.user_id);
                const decryptedUser = decryptedUsers.find(u => u.id === mission.user_id);
                if (user?.email && decryptedUser?.email) {
                  // Retirer 1 jobi à l'utilisateur pour la fermeture automatique
                  try {
                    // Vérifier le solde actuel
                    const { data: jobiData, error: jobiError } = await supabase
                      .from('user_jobi')
                      .select('montant')
                      .eq('user_id', user.id)
                      .single();

                    if (!jobiError && jobiData) {
                      const currentAmount = jobiData.montant || 0;
                      const deductionAmount = 1;

                      // Calculer le nouveau solde
                      const newAmount = currentAmount - deductionAmount;

                      // Mettre à jour le solde
                      const { error: updateJobiError } = await supabase
                        .from('user_jobi')
                        .update({ montant: newAmount })
                        .eq('user_id', user.id)
                        .single();

                      if (!updateJobiError) {
                        // Ajouter l'entrée dans l'historique
                        await supabase
                          .from('user_jobi_historique')
                          .insert({
                          user_id: user.id,
                            montant: -deductionAmount,
                            titre: 'Fermeture automatique de mission',
                            description: `Déduction suite à la fermeture automatique de la mission "${mission.titre}" après 60 jours d'inactivité.`
                          });

                        // Supprimer le cache Redis Jobi
                        const cacheKey = `jobi_balance_${user.id}`;
                        await redis.del(cacheKey);

                        // Créer une notification pour informer l'utilisateur de la fermeture automatique et du retrait des Jobis
                        const { error: notifError } = await supabase
                          .from('user_notifications')
                          .insert([{
                            user_id: user.id,
                            type: 'jobi',
                            title: 'Mission fermée automatiquement',
                            content: `Votre mission "${mission.titre}" a été fermée automatiquement après 60 jours d'inactivité. ${deductionAmount} Jobi a été déduit de votre solde.`,
                            link: `/dashboard/missions/mes-missions`,
                            is_read: false,
                            is_archived: false
                          }]);

                        if (notifError) {
                          logger.error('Erreur lors de la création de la notification de fermeture automatique:', notifError);
                        } else {
                          logger.info('Notification de fermeture automatique créée avec succès pour l\'utilisateur:', user.id);
                        }

                        // Ajouter l'information de déduction dans les données de l'email
                        await sendMissionAutoClosureEmail(decryptedUser.email, {
                          titre: mission.titre,
                          description: mission.description,
                          budget: mission.budget,
                          budget_defini: mission.budget_defini,
                          adresse: mission.adresse,
                          ville: mission.ville,
                          code_postal: mission.code_postal,
                          date_mission: mission.date_mission,
                          category_id: mission.category_id,
                          payment_method: mission.payment_method,
                          jobi_deduction: deductionAmount
                        });
                      } else {
                        logger.error('Erreur lors de la mise à jour du solde Jobi:', updateJobiError);

                        // Envoyer l'email sans information de déduction
                        await sendMissionAutoClosureEmail(decryptedUser.email, {
                          titre: mission.titre,
                          description: mission.description,
                          budget: mission.budget,
                          budget_defini: mission.budget_defini,
                          adresse: mission.adresse,
                          ville: mission.ville,
                          code_postal: mission.code_postal,
                          date_mission: mission.date_mission,
                          category_id: mission.category_id,
                          payment_method: mission.payment_method
                      });

                        // Créer une notification pour informer l'utilisateur de la fermeture automatique sans mentionner le retrait des Jobis
                        const { error: notifError } = await supabase
                          .from('user_notifications')
                          .insert([{
                            user_id: user.id,
                            type: 'mission',
                            title: 'Mission fermée automatiquement',
                            content: `Votre mission "${mission.titre}" a été fermée automatiquement après 60 jours d'inactivité.`,
                            link: `/dashboard/missions/mes-missions`,
                            is_read: false,
                            is_archived: false
                          }]);

                        if (notifError) {
                          logger.error('Erreur lors de la création de la notification de fermeture automatique:', notifError);
                        }
                      }
                    } else {
                      // Envoyer l'email sans information de déduction
                      await sendMissionAutoClosureEmail(decryptedUser.email, {
                        titre: mission.titre,
                        description: mission.description,
                        budget: mission.budget,
                        budget_defini: mission.budget_defini,
                        adresse: mission.adresse,
                        ville: mission.ville,
                        code_postal: mission.code_postal,
                        date_mission: mission.date_mission,
                        category_id: mission.category_id,
                        payment_method: mission.payment_method
                      });

                      // Créer une notification pour informer l'utilisateur de la fermeture automatique sans mentionner le retrait des Jobis
                      const { error: notifError } = await supabase
                        .from('user_notifications')
                        .insert([{
                          user_id: user.id,
                          type: 'mission',
                          title: 'Mission fermée automatiquement',
                          content: `Votre mission "${mission.titre}" a été fermée automatiquement après 60 jours d'inactivité.`,
                          link: `/dashboard/missions/mes-missions`,
                          is_read: false,
                          is_archived: false
                        }]);

                      if (notifError) {
                        logger.error('Erreur lors de la création de la notification de fermeture automatique:', notifError);
                      }
                    }
                  } catch (jobiError) {
                    logger.error('Erreur lors de la déduction de Jobi pour fermeture automatique:', jobiError);
                    // Envoyer l'email sans information de déduction en cas d'erreur
                    await sendMissionAutoClosureEmail(decryptedUser.email, {
                      titre: mission.titre,
                      description: mission.description,
                      budget: mission.budget,
                      budget_defini: mission.budget_defini,
                      adresse: mission.adresse,
                      ville: mission.ville,
                      code_postal: mission.code_postal,
                      date_mission: mission.date_mission,
                      category_id: mission.category_id,
                      payment_method: mission.payment_method
                    });

                    // Créer une notification pour informer l'utilisateur de la fermeture automatique sans mentionner le retrait des Jobis
                    const { error: notifError } = await supabase
                      .from('user_notifications')
                      .insert([{
                        user_id: user.id,
                        type: 'mission',
                        title: 'Mission fermée automatiquement',
                        content: `Votre mission "${mission.titre}" a été fermée automatiquement après 60 jours d'inactivité.`,
                        link: `/dashboard/missions/mes-missions`,
                        is_read: false,
                        is_archived: false
                      }]);

                    if (notifError) {
                      logger.error('Erreur lors de la création de la notification de fermeture automatique:', notifError);
                    }
                  }
                }
              }
            }
          }
        }
      } catch (notificationError) {
        logger.error('Erreur lors de la notification des jobbeurs ou de la fermeture des missions:', notificationError);
        // Ne pas bloquer la création de la mission si la notification échoue
      }

      res.status(201).json(mission);
    } catch (error) {
      logger.error('Erreur lors de la création de la mission:', error);
      res.status(500).json({ error: 'Erreur lors de la création de la mission' });
    }
  },

  // Récupérer les missions d'un utilisateur
  getMissions: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const offset = (page - 1) * limit;

      // Récupérer les paramètres de filtrage
      const {
        status,
        search,
        categories,
        subcategories,
        budget_types,
        payment_methods,
        is_urgent,
        liked,
        owner // Nouveau paramètre pour indiquer si on veut récupérer les missions dont l'utilisateur est le propriétaire
      } = req.query;

      // Générer une clé de cache unique basée sur tous les paramètres
      const cacheKey = generateCacheKey('missions', {
        userId,
        page,
        limit,
        status,
        search,
        categories,
        subcategories,
        budget_types,
        payment_methods,
        is_urgent,
        liked,
        owner
      });

      // Vérifier si les données sont en cache
      const cachedData = await redis.get(cacheKey);
      if (cachedData) {
        logger.info('Données récupérées depuis le cache Redis pour getMissions');
        res.json(JSON.parse(cachedData));
        return;
      }

      logger.info('Paramètres de filtrage reçus:', {
        status,
        search,
        categories,
        subcategories,
        budget_types,
        payment_methods,
        is_urgent,
        liked
      });

      // Gestion du filtre offres envoyées
      const offresEnvoyeesFilter = req.query.offresEnvoyeesFilter as string[] | string | undefined;
      let hasOffresEnvoyees = false;
      if (offresEnvoyeesFilter && Array.isArray(offresEnvoyeesFilter)) {
        hasOffresEnvoyees = offresEnvoyeesFilter.includes('offres_envoyees');
      } else if (offresEnvoyeesFilter && typeof offresEnvoyeesFilter === 'string') {
        hasOffresEnvoyees = offresEnvoyeesFilter === 'offres_envoyees';
      }

      // Récupérer toutes les missions où l'utilisateur a postulé
      let appliedMissionIds: string[] = [];
      if (hasOffresEnvoyees || offresEnvoyeesFilter !== undefined) {
        const { data: appliedMissions, error: appliedError } = await supabase
          .from('user_mission_candidature')
          .select('mission_id')
          .eq('jobbeur_id', userId);
        if (!appliedError && appliedMissions) {
          appliedMissionIds = appliedMissions.map((m: any) => m.mission_id);
        }
      }

      // Récupérer toutes les missions aimées par l'utilisateur
      let likedMissionIds: string[] = [];
      if (liked === 'true') {
        const { data: likedMissions } = await supabase
          .from('user_mission_likes')
          .select('mission_id')
          .eq('user_id', userId);
        if (likedMissions) {
          likedMissionIds = likedMissions.map((m: any) => m.mission_id);
        }
      }

      // Déterminer l'ensemble final de missions à inclure selon les filtres actifs
      let finalMissionIds: string[] | null = null;
      if (liked === 'true' && offresEnvoyeesFilter !== undefined) {
        // Les deux filtres sont actifs : intersection
        if (hasOffresEnvoyees) {
          // Missions à la fois aimées et où l'utilisateur a postulé
          finalMissionIds = likedMissionIds.filter(id => appliedMissionIds.includes(id));
        } else {
          // Missions aimées mais où l'utilisateur N'A PAS postulé
          finalMissionIds = likedMissionIds.filter(id => !appliedMissionIds.includes(id));
        }
        // Si l'intersection est vide, retour immédiat
        if (!finalMissionIds.length) {
          return res.json({ data: [], hasMore: false, total: 0 });
        }
      } else if (liked === 'true') {
        // Seulement liked actif
        if (!likedMissionIds.length) {
          return res.json({ data: [], hasMore: false, total: 0 });
        }
        finalMissionIds = likedMissionIds;
      } else if (offresEnvoyeesFilter !== undefined) {
        // Seulement offres envoyées actif
        if (hasOffresEnvoyees) {
          if (!appliedMissionIds.length) {
            return res.json({ data: [], hasMore: false, total: 0 });
          }
          finalMissionIds = appliedMissionIds;
        } else {
          // Missions où l'utilisateur N'A PAS postulé
          // On ne peut pas utiliser finalMissionIds ici, on applique le filtre plus bas
          if (appliedMissionIds.length === 0) {
            finalMissionIds = null; // Pas de missions à exclure
          } else {
            finalMissionIds = null; // On gère l'exclusion plus bas
          }
        }
      }
      // Construire la requête de base pour le comptage
      let countQuery = supabase
        .from('user_missions')
        .select('*', { count: 'exact', head: true })
        .eq('statut', 'en_cours')
        .eq('is_closed', false);

      // Si le paramètre owner est true, on récupère les missions dont l'utilisateur est le propriétaire
      // Sinon, on récupère les missions dont l'utilisateur n'est pas le propriétaire (comportement par défaut)
      if (owner === 'true') {
        countQuery = countQuery.eq('user_id', userId);
      } else {
        countQuery = countQuery.neq('user_id', userId);
      }

      if (finalMissionIds) {
        countQuery = countQuery.in('id', finalMissionIds);
      } else if (offresEnvoyeesFilter !== undefined && !hasOffresEnvoyees && appliedMissionIds.length > 0) {
        // Exclure les missions où l'utilisateur a postulé
        countQuery = countQuery.not('id', 'in', `(${appliedMissionIds.join(',')})`);
      }

      // Appliquer les mêmes filtres à la requête de comptage
      if (status) {
        const statusArray = convertStatusToArray(status);
        logger.info('Application du filtre de statut:', statusArray);
        countQuery = countQuery.in('statut', statusArray);
      }

      if (is_urgent === 'true') {
        countQuery = countQuery.eq('is_urgent', true);
      }

      if (categories) {
        const categoriesArray = typeof categories === 'string' ? categories.split(',') : Array.isArray(categories) ? categories : [categories];
        countQuery = countQuery.or(categoriesArray.map((cat: string | ParsedQs) => `category_id.eq.${String(cat)}`).join(','));
      }

      if (subcategories) {
        const subcategoriesArray = typeof subcategories === 'string' ? subcategories.split(',') : Array.isArray(subcategories) ? subcategories : [subcategories];
        countQuery = countQuery.or(subcategoriesArray.map((subcat: string | ParsedQs) => `subcategory_id.eq.${String(subcat)}`).join(','));
      }

      if (budget_types) {
        const budgetTypesArray = Array.isArray(budget_types) ? budget_types : [budget_types];
        if (budgetTypesArray.includes('budget_defini')) {
          countQuery = countQuery.eq('budget_defini', true);
        } else if (budgetTypesArray.includes('budget_non_defini')) {
          countQuery = countQuery.eq('budget_defini', false);
        }
      }

      if (payment_methods) {
        const paymentMethodsArray = Array.isArray(payment_methods) ? payment_methods : [payment_methods];
        countQuery = countQuery.in('payment_method', paymentMethodsArray);
      }

      if (search && typeof search === 'string') {
        const searchTerm = search.toLowerCase();
        countQuery = countQuery.or(
          `titre.ilike.%${searchTerm}%,` +
          `description.ilike.%${searchTerm}%,` +
          `ville.ilike.%${searchTerm}%,` +
          `code_postal.ilike.%${searchTerm}%`
        );
      }

      // Exécuter la requête de comptage
      const { count: total, error: countError } = await countQuery;

      if (countError) throw countError;

      // Construire la requête principale pour les données
      let query = supabase
        .from('user_missions')
        .select(`
          *,
          photos:user_mission_photos (
            id,
            photo_url,
            order_index
          ),
          applications:user_mission_candidature (
            id,
            jobbeur_id,
            statut,
            created_at
          ),
          likes:user_mission_likes (
            id,
            user_id
          ),
          comments:user_mission_comments (
            id,
            user_id
          )
        `)
        .eq('statut', 'en_cours')
        .eq('is_closed', false);

      // Si le paramètre owner est true, on récupère les missions dont l'utilisateur est le propriétaire
      // Sinon, on récupère les missions dont l'utilisateur n'est pas le propriétaire (comportement par défaut)
      if (owner === 'true') {
        query = query.eq('user_id', userId);
      } else {
        query = query.neq('user_id', userId);
      }

      if (finalMissionIds) {
        query = query.in('id', finalMissionIds);
      } else if (offresEnvoyeesFilter !== undefined && !hasOffresEnvoyees && appliedMissionIds.length > 0) {
        // Exclure les missions où l'utilisateur a postulé
        query = query.not('id', 'in', `(${appliedMissionIds.join(',')})`);
      }

      // Appliquer les mêmes filtres à la requête principale
      if (status) {
        const statusArray = convertStatusToArray(status);
        logger.info('Application du filtre de statut à la requête principale:', statusArray);
        query = query.in('statut', statusArray);
      }

      if (is_urgent === 'true') {
        query = query.eq('is_urgent', true);
      }

      if (categories) {
        const categoriesArray = typeof categories === 'string' ? categories.split(',') : Array.isArray(categories) ? categories : [categories];
        query = query.or(categoriesArray.map((cat: string | ParsedQs) => `category_id.eq.${String(cat)}`).join(','));
      }

      if (subcategories) {
        const subcategoriesArray = typeof subcategories === 'string' ? subcategories.split(',') : Array.isArray(subcategories) ? subcategories : [subcategories];
        query = query.or(subcategoriesArray.map((subcat: string | ParsedQs) => `subcategory_id.eq.${String(subcat)}`).join(','));
      }

      if (budget_types) {
        const budgetTypesArray = Array.isArray(budget_types) ? budget_types : [budget_types];
        if (budgetTypesArray.includes('budget_defini')) {
          query = query.eq('budget_defini', true);
        } else if (budgetTypesArray.includes('budget_non_defini')) {
          query = query.eq('budget_defini', false);
        }
      }

      if (payment_methods) {
        const paymentMethodsArray = Array.isArray(payment_methods) ? payment_methods : [payment_methods];
        query = query.in('payment_method', paymentMethodsArray);
      }

      if (search && typeof search === 'string') {
        const searchTerm = search.toLowerCase();
        logger.info('Application du filtre de recherche:', { searchTerm });

        try {
          query = query.or(
            `titre.ilike.%${searchTerm}%,` +
            `description.ilike.%${searchTerm}%,` +
            `ville.ilike.%${searchTerm}%,` +
            `code_postal.ilike.%${searchTerm}%`
          );

          logger.info('Filtre de recherche appliqué avec succès');
        } catch (searchError) {
          logger.error('Erreur lors de l\'application du filtre de recherche:', searchError);
          throw searchError;
        }
      }

      // Ajouter les filtres de pagination
      const { data: missions, error } = await query
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        console.error('Erreur lors de la récupération des missions:', error);
        res.status(500).json({ error: 'Erreur lors de la récupération des missions' });
        return;
      }

      // Récupérer le profil de l'utilisateur
      const { data: userProfile, error: profileError } = await supabase
        .from('user_profil')
        .select('nom, prenom, type_de_profil, photo_url, statut_entreprise')
        .eq('user_id', userId)
        .single();

      if (profileError) {
        logger.error('Erreur lors de la récupération du profil utilisateur:', profileError);
      }

      // Déchiffrer les données de profil
      const decryptedUserProfile = userProfile ? await decryptProfilDataAsync(userProfile) : null;

      // Ajouter le profil utilisateur et les compteurs à chaque mission
      const missionsWithProfile = missions.map(mission => ({
        ...mission,
        user_profile: decryptedUserProfile || {
          nom: '',
          prenom: '',
          type_de_profil: 'particulier',
          photo_url: null,
          statut_entreprise: ''
        },
        likes_count: mission.likes?.length || 0,
        applications_count: mission.applications?.length || 0,
        comments_count: mission.comments?.length || 0,
        user_has_liked: mission.likes?.some((like: { user_id: string }) => like.user_id === userId) || false,
        user_has_commented: mission.comments?.some((comment: { user_id: string }) => comment.user_id === userId) || false
      }));

      // Calculer s'il y a plus de résultats
      const hasMore = total ? offset + missions.length < total : false;

      const responseData = {
        data: missionsWithProfile,
        hasMore,
        total: total || 0
      };

      // Mettre en cache les résultats
      await redis.set(cacheKey, JSON.stringify(responseData), 'EX', CACHE_DURATION);
      logger.info(`Données mises en cache avec la clé: ${cacheKey}`);

      res.json(responseData);
    } catch (error) {
      logger.error('Erreur lors de la récupération des missions:', error);
      res.status(500).json({ error: 'Erreur lors de la récupération des missions' });
    }
  },

  // Récupérer les missions disponibles pour un jobbeurs
  getAvailableMissions: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;

      // Générer une clé de cache unique pour cette requête
      const cacheKey = generateCacheKey('available_missions', { userId });

      // Vérifier si les données sont en cache
      const cachedData = await redis.get(cacheKey);
      if (cachedData) {
        logger.info('Récupération des missions disponibles depuis le cache');
        res.json(JSON.parse(cachedData));
        return;
      }

      // Récupérer les services du jobbeur
      const { data: services, error: servicesError } = await supabase
        .from('user_services')
        .select('category_id, subcategory_id')
        .eq('user_id', userId)
        .eq('statut', 'actif');

      if (servicesError) throw servicesError;

      if (!services || services.length === 0) {
        res.json([]);
        return;
      }

      // Récupérer les missions déjà postulées par l'utilisateur
      const { data: appliedMissions, error: appliedError } = await supabase
        .from('user_mission_candidature')
        .select('mission_id')
        .eq('jobbeur_id', userId);
      if (appliedError) throw appliedError;
      const appliedMissionIds = appliedMissions?.map(a => a.mission_id) || [];

      // Récupérer les missions rejetées par l'utilisateur
      const { data: rejectedMissions, error: rejectedError } = await supabase
        .from('user_mission_rejections')
        .select('mission_id')
        .eq('user_id', userId);
      if (rejectedError) throw rejectedError;
      const rejectedMissionIds = rejectedMissions?.map(rm => rm.mission_id) || [];

      // Créer les conditions pour la requête
      const conditions = services.map(service => ({
        category_id: service.category_id,
        subcategory_id: service.subcategory_id
      }));

      // Récupérer les missions correspondantes
      let query = supabase
        .from('user_missions')
        .select('*')
        .eq('statut', 'en_cours')
        .eq('is_closed', false)
        .or(conditions.map(c => `and(category_id.eq.${c.category_id},subcategory_id.eq.${c.subcategory_id})`).join(','))
        .order('created_at', { ascending: false });

      // Exclure les missions où l'utilisateur a déjà postulé ou qu'il a refusées
      if (appliedMissionIds.length > 0) {
        query = query.not('id', 'in', `(${appliedMissionIds.join(',')})`);
      }
      if (rejectedMissionIds.length > 0) {
        query = query.not('id', 'in', `(${rejectedMissionIds.join(',')})`);
      }

      const { data: missions, error } = await query;
      if (error) throw error;

      // Mettre en cache les résultats
      await redis.set(cacheKey, JSON.stringify(missions), 'EX', CACHE_DURATION);
      logger.info('Missions disponibles mises en cache');

      res.json(missions);
    } catch (error) {
      logger.error('Erreur lors de la récupération des missions disponibles:', error);
      res.status(500).json({ error: 'Erreur lors de la récupération des missions disponibles' });
    }
  },

  // Postuler à une mission
  applyToMission: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const { missionId, message } = req.body;

      if (!userId) {
        res.status(401).json({ error: 'Utilisateur non authentifié' });
        return;
      }

      // Vérifier la limite de réponses/candidatures pour les non-premium
      const { isPremium, missionResponsesLimit } = await getUserSubscriptionLimits(userId);
      if (!isPremium) {
        // Compter le nombre de candidatures envoyées ce mois-ci
        const startOfMonth = new Date();
        startOfMonth.setDate(1);
        startOfMonth.setHours(0, 0, 0, 0);
        const { count: responsesCount, error: countError } = await supabase
          .from('user_mission_candidature')
          .select('*', { count: 'exact', head: true })
          .eq('jobbeur_id', userId)
          .gte('created_at', startOfMonth.toISOString());
        if (countError) {
          logger.error('Erreur lors du comptage des réponses aux missions:', countError);
          return res.status(500).json({ error: "Erreur lors de la vérification de vos candidatures ce mois-ci." });
        }
        const proposalsCountSafe = responsesCount ?? 0;
        if (proposalsCountSafe >= missionResponsesLimit) {
          res.status(403).json({ error: `Limite atteinte : vous avez déjà envoyé ${missionResponsesLimit} candidatures/offres par mois avec l'offre gratuite. Passez Premium pour lever cette limite illimitée !` });
          return;
        }
      }

      // Vérifier si l'utilisateur a déjà postulé
      const { data: existingApplication, error: checkError } = await supabase
        .from('user_mission_candidature')
        .select('id')
        .eq('mission_id', missionId)
        .eq('jobbeur_id', userId)
        .single();

      if (existingApplication) {
        res.status(400).json({ error: 'Vous avez déjà postulé à cette mission' });
        return;
      }

      // Créer la candidature
      const { data: application, error } = await supabase
        .from('user_mission_candidature')
        .insert([{
          mission_id: missionId,
          jobbeur_id: userId,
          message,
          statut: 'en_cours'
        }])
        .select()
        .single();

      if (error) throw error;

      // Journalisation de l'action utilisateur
      if (userId) {
        await logUserActivity(
          userId,
          'mission_apply',
          missionId,
          'mission',
          {
            message: message ? (message.length > 100 ? message.substring(0, 100) + '...' : message) : undefined
          },
          getIpFromRequest(req)
        );
      }

      res.status(201).json(application);
    } catch (error) {
      logger.error('Erreur lors de la candidature à la mission:', error);
      res.status(500).json({ error: 'Erreur lors de la candidature à la mission' });
    }
  },

  // Ajouter une photo à une mission
  addPhotoToMission: async (req: FileRequest, res: Response): Promise<void> => {
    let userId: string | undefined;
    try {
      userId = req.user?.userId;
      const missionId = req.params.id;
      logger.info('Début de addPhotoToMission', { userId, missionId });

      if (!userId) {
        logger.warn('Tentative d\'ajout de photo sans authentification');
        res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
        return;
      }

      // Gérer les fichiers multiples
      const photos = req.files?.photo;
      if (!photos) {
        logger.warn('Aucune photo fournie');
        res.status(400).json({
          message: 'Aucune photo fournie',
          success: false,
          toastType: 'error'
        });
        return;
      }

      // Convertir en tableau si c'est un seul fichier
      const photoArray = Array.isArray(photos) ? photos : [photos];
      logger.info('Nombre de photos à traiter:', { count: photoArray.length });

      // Vérifier le nombre total de photos existantes
      const { count } = await supabase
        .from('user_mission_photos')
        .select('*', { count: 'exact' })
        .eq('mission_id', missionId);

      const currentCount = count || 0;
      if (currentCount + photoArray.length > 8) {
        res.status(400).json({
          message: 'Vous ne pouvez pas ajouter plus de 8 photos par mission.',
          success: false,
          toastType: 'error'
        });
        return;
      }

      const uploadedPhotos = [];
      let orderIndex = currentCount;

      for (const file of photoArray) {
        // Vérification du type MIME
        const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp'];
        if (!allowedMimeTypes.includes(file.mimetype)) {
          logger.error('Type de fichier non autorisé :', { mimetype: file.mimetype });
          continue;
        }

        // Vérification de la taille
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (file.size > maxSize) {
          logger.error('Fichier trop volumineux', { size: file.size });
          continue;
        }

        try {
          // Lire le fichier temporaire
          const fileBuffer = await new Promise<Buffer>((resolve, reject) => {
            require('fs').readFile(file.tempFilePath, (err: any, data: Buffer) => {
              if (err) reject(err);
              else resolve(data);
            });
          });

          // Upload de la photo
          const photoUrl = await uploadMissionPhoto(userId, fileBuffer, file.mimetype, missionId);

          const newPhoto = {
            mission_id: missionId,
            user_id: userId,
            photo_url: photoUrl,
            order_index: orderIndex++,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          const { data, error } = await supabase
            .from('user_mission_photos')
            .insert([newPhoto])
            .select()
            .single();

          if (error) {
            logger.error('Erreur lors de l\'ajout de la photo à la mission', { error, userId, missionId });
            continue;
          }

          // Créer un signalement différé seulement si l'image a été modérée avec un ID temporaire réel
          if (req.body?.tempImageId && typeof req.body.tempImageId === 'string' && req.body.tempImageId.trim() !== '') {
              try {
              await contentModerationService.createDeferredImageReport(
                req.body.tempImageId,
                data.id,
                'mission',
                userId || ''
              );

              logger.info('Signalement différé créé avec succès pour la photo de mission', {
                tempImageId: req.body.tempImageId,
                permanentImageId: data.id,
                contentType: 'mission',
                userId
              });
            } catch (reportError) {
              // Ne pas bloquer le processus si la création du signalement échoue
              logger.error('Erreur lors de la création du signalement différé pour la photo de mission', {
                error: reportError instanceof Error ? reportError.message : 'Unknown error',
                userId,
                photoId: data.id
              });
            }
          }

          uploadedPhotos.push(data);
          logger.info('Photo ajoutée à la mission avec succès', { userId, missionId, photoId: data.id });
        } catch (error) {
          logger.error('Erreur lors du traitement d\'une photo', { error, userId, missionId });
          continue;
        }
      }

      if (uploadedPhotos.length === 0) {
        res.status(400).json({
          message: 'Aucune photo n\'a pu être uploadée',
          success: false,
          toastType: 'error'
        });
        return;
      }

      // Nettoyer les anciennes photos après l'upload
      cleanupOldMissionPhotos().catch(error => {
        logger.error('Erreur lors du nettoyage des photos anciennes', { error });
      });

      res.status(201).json({
        message: `${uploadedPhotos.length} photo(s) ajoutée(s) avec succès`,
        success: true,
        photos: uploadedPhotos
      });
    } catch (error) {
      logger.error('Erreur lors de l\'ajout des photos à la mission', { error, userId });
      res.status(500).json({
        message: 'Erreur lors de l\'ajout des photos à la mission',
        success: false,
        toastType: 'error'
      });
    }
  },

  // Supprimer une photo d'une mission
  deletePhotoFromMission: async (req: Request, res: Response): Promise<void> => {
    let userId: string | undefined;
    try {
      userId = req.user?.userId;
      const { missionId, photoId } = req.params;

      if (!userId) {
        res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
        return;
      }

      // Récupérer les informations de la photo
      const { data: photo, error: photoError } = await supabase
        .from('user_mission_photos')
        .select('*')
        .eq('id', photoId)
        .eq('mission_id', missionId)
        .eq('user_id', userId)
        .single();

      if (photoError || !photo) {
        res.status(404).json({
          message: 'Photo non trouvée',
          success: false,
          toastType: 'error'
        });
        return;
      }

      // Ne pas supprimer si c'est déjà l'image par défaut
      if (photo.photo_url === DEFAULT_AVATAR_URL) {
        res.json({
          message: 'Cette photo a déjà été supprimée',
          success: true,
          toastType: 'info'
        });
        return;
      }

      // Supprimer la photo du storage avec la fonction utilitaire
      const storagePath = extractFilePathFromUrl(photo.photo_url, 'mission_photos');
      if (storagePath) {
        const { error: storageError } = await supabase.storage
          .from('mission_photos')
          .remove([storagePath]);

        if (storageError) {
          logger.error('Erreur lors de la suppression de la photo du storage', {
            error: storageError,
            photoUrl: photo.photo_url,
            storagePath
          });
        } else {
          logger.info('Photo supprimée avec succès du bucket mission', { photoId, missionId, storagePath });
        }
      } else {
        logger.warn('Impossible d\'extraire le chemin de la photo de mission', {
          photoUrl: photo.photo_url,
          photoId,
          missionId
        });
      }

      // Mettre à jour l'URL avec l'image par défaut
      const { error: updateError } = await supabase
        .from('user_mission_photos')
        .update({ photo_url: DEFAULT_AVATAR_URL })
        .eq('id', photoId)
        .eq('mission_id', missionId)
        .eq('user_id', userId);

      if (updateError) {
        res.status(500).json({
          message: 'Erreur lors de la mise à jour de la photo',
          success: false,
          toastType: 'error'
        });
        return;
      }

      res.json({
        message: 'Photo supprimée avec succès',
        success: true,
        toastType: 'success'
      });
    } catch (error) {
      logger.error('Erreur lors de la suppression de la photo', { error, userId });
      res.status(500).json({
        message: 'Erreur lors de la suppression de la photo',
        success: false,
        toastType: 'error'
      });
    }
  },

  // Réorganiser les photos d'une mission
  reorderMissionPhotos: async (req: Request, res: Response): Promise<void> => {
    let userId: string | undefined;
    try {
      userId = req.user?.userId;
      const { missionId } = req.params;
      const { photoIds } = req.body;

      if (!userId) {
        res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
        return;
      }

      // Mettre à jour l'ordre des photos
      const updates = photoIds.map((photoId: string, index: number) => ({
        id: photoId,
        order_index: index
      }));

      const { error } = await supabase
        .from('user_mission_photos')
        .upsert(updates);

      if (error) {
        res.status(500).json({
          message: 'Erreur lors de la réorganisation des photos',
          success: false,
          toastType: 'error'
        });
        return;
      }

      // Invalider le cache après avoir réorganisé les photos
      await invalidateMissionCache(missionId, userId);
      logger.info('Cache invalidé après réorganisation des photos:', { missionId, userId });

      res.json({
        message: 'Photos réorganisées avec succès',
        success: true,
        toastType: 'success'
      });
    } catch (error) {
      logger.error('Erreur lors de la réorganisation des photos', { error, userId });
      res.status(500).json({
        message: 'Erreur lors de la réorganisation des photos',
        success: false,
        toastType: 'error'
      });
    }
  },

  // Récupérer toutes les missions
  getAllMissions: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const offset = (page - 1) * limit;

      // Récupérer les paramètres de filtrage
      const {
        status,
        search,
        categories,
        subcategories,
        budget_types,
        payment_methods,
        is_urgent,
        liked,
        sort_by
      } = req.query;

      // Générer une clé de cache unique pour cette requête
      const cacheKey = generateCacheKey('all_missions', {
        userId,
        page,
        limit,
        status,
        search,
        categories,
        subcategories,
        budget_types,
        payment_methods,
        is_urgent,
        liked,
        sort_by
      });

      // Vérifier si les données sont en cache
      const cachedData = await redis.get(cacheKey);
      if (cachedData) {
        logger.info('Récupération de toutes les missions depuis le cache');
        return res.json(JSON.parse(cachedData));
      }

      // Gestion du filtre offres envoyées
      const offresEnvoyeesFilter = req.query.offresEnvoyeesFilter as string[] | string | undefined;
      let hasOffresEnvoyees = false;
      if (offresEnvoyeesFilter && Array.isArray(offresEnvoyeesFilter)) {
        hasOffresEnvoyees = offresEnvoyeesFilter.includes('offres_envoyees');
      } else if (offresEnvoyeesFilter && typeof offresEnvoyeesFilter === 'string') {
        hasOffresEnvoyees = offresEnvoyeesFilter === 'offres_envoyees';
      }

      // Récupérer toutes les missions où l'utilisateur a postulé
      let appliedMissionIds: string[] = [];
      if (hasOffresEnvoyees || offresEnvoyeesFilter !== undefined) {
        const { data: appliedMissions, error: appliedError } = await supabase
          .from('user_mission_candidature')
          .select('mission_id')
          .eq('jobbeur_id', userId);
        if (!appliedError && appliedMissions) {
          appliedMissionIds = appliedMissions.map((m: any) => m.mission_id);
        }
      }

      // Récupérer toutes les missions aimées par l'utilisateur
      let likedMissionIds: string[] = [];
      if (liked === 'true') {
        const { data: likedMissions } = await supabase
          .from('user_mission_likes')
          .select('mission_id')
          .eq('user_id', userId);
        if (likedMissions) {
          likedMissionIds = likedMissions.map((m: any) => m.mission_id);
        }
      }

      // Déterminer l'ensemble final de missions à inclure selon les filtres actifs
      let finalMissionIds: string[] | null = null;
      if (liked === 'true' && offresEnvoyeesFilter !== undefined) {
        // Les deux filtres sont actifs : intersection
        if (hasOffresEnvoyees) {
          // Missions à la fois aimées et où l'utilisateur a postulé
          finalMissionIds = likedMissionIds.filter(id => appliedMissionIds.includes(id));
        } else {
          // Missions aimées mais où l'utilisateur N'A PAS postulé
          finalMissionIds = likedMissionIds.filter(id => !appliedMissionIds.includes(id));
        }
        // Si l'intersection est vide, retour immédiat
        if (!finalMissionIds.length) {
          return res.json({ data: [], hasMore: false, total: 0 });
        }
      } else if (liked === 'true') {
        // Seulement liked actif
        if (!likedMissionIds.length) {
          return res.json({ data: [], hasMore: false, total: 0 });
        }
        finalMissionIds = likedMissionIds;
      } else if (offresEnvoyeesFilter !== undefined) {
        // Seulement offres envoyées actif
        if (hasOffresEnvoyees) {
          if (!appliedMissionIds.length) {
            return res.json({ data: [], hasMore: false, total: 0 });
          }
          finalMissionIds = appliedMissionIds;
        } else {
          // Missions où l'utilisateur N'A PAS postulé
          // On ne peut pas utiliser finalMissionIds ici, on applique le filtre plus bas
          if (appliedMissionIds.length === 0) {
            finalMissionIds = null; // Pas de missions à exclure
          } else {
            finalMissionIds = null; // On gère l'exclusion plus bas
          }
        }
      }
      // Construire la requête de base pour le comptage
      let countQuery = supabase
        .from('user_missions')
        .select('*', { count: 'exact', head: true })
        .eq('statut', 'en_cours')
        .eq('is_closed', false)
        .neq('user_id', userId);

      if (finalMissionIds) {
        countQuery = countQuery.in('id', finalMissionIds);
      } else if (offresEnvoyeesFilter !== undefined && !hasOffresEnvoyees && appliedMissionIds.length > 0) {
        // Exclure les missions où l'utilisateur a postulé
        countQuery = countQuery.not('id', 'in', `(${appliedMissionIds.join(',')})`);
      }

      // Appliquer les mêmes filtres à la requête de comptage
      if (status) {
        const statusArray = convertStatusToArray(status);
        countQuery = countQuery.in('statut', statusArray);
      }

      if (is_urgent === 'true') {
        countQuery = countQuery.eq('is_urgent', true);
      }

      if (categories) {
        const categoriesArray = typeof categories === 'string' ? categories.split(',') : Array.isArray(categories) ? categories : [categories];
        countQuery = countQuery.or(categoriesArray.map((cat: string | ParsedQs) => `category_id.eq.${String(cat)}`).join(','));
      }

      if (subcategories) {
        const subcategoriesArray = typeof subcategories === 'string' ? subcategories.split(',') : Array.isArray(subcategories) ? subcategories : [subcategories];
        countQuery = countQuery.or(subcategoriesArray.map((subcat: string | ParsedQs) => `subcategory_id.eq.${String(subcat)}`).join(','));
      }

      if (budget_types) {
        const budgetTypesArray = Array.isArray(budget_types) ? budget_types : [budget_types];
        if (budgetTypesArray.includes('budget_defini')) {
          countQuery = countQuery.eq('budget_defini', true);
        } else if (budgetTypesArray.includes('budget_non_defini')) {
          countQuery = countQuery.eq('budget_defini', false);
        }
      }

      if (payment_methods) {
        const paymentMethodsArray = Array.isArray(payment_methods) ? payment_methods : [payment_methods];
        countQuery = countQuery.in('payment_method', paymentMethodsArray);
      }

      if (search && typeof search === 'string') {
        const searchTerm = search.toLowerCase();
        countQuery = countQuery.or(
          `titre.ilike.%${searchTerm}%,` +
          `description.ilike.%${searchTerm}%,` +
          `ville.ilike.%${searchTerm}%,` +
          `code_postal.ilike.%${searchTerm}%`
        );
      }

      // Exécuter la requête de comptage
      const { count: total, error: countError } = await countQuery;

      if (countError) throw countError;

      // Construire la requête principale pour les données
      let query = supabase
        .from('user_missions')
        .select(`
          *,
          photos:user_mission_photos (
            id,
            photo_url,
            order_index
          ),
          applications:user_mission_candidature (
            id,
            jobbeur_id,
            statut,
            created_at
          ),
          likes:user_mission_likes (
            id,
            user_id
          ),
          comments:user_mission_comments (
            id,
            user_id
          )
        `)
        .eq('statut', 'en_cours')
        .eq('is_closed', false)
        .neq('user_id', userId);

      if (finalMissionIds) {
        query = query.in('id', finalMissionIds);
      } else if (offresEnvoyeesFilter !== undefined && !hasOffresEnvoyees && appliedMissionIds.length > 0) {
        // Exclure les missions où l'utilisateur a postulé
        query = query.not('id', 'in', `(${appliedMissionIds.join(',')})`);
      }

      // Appliquer les mêmes filtres à la requête principale
      if (status) {
        const statusArray = convertStatusToArray(status);
        logger.info('Application du filtre de statut à la requête principale:', statusArray);
        query = query.in('statut', statusArray);
      }

      if (is_urgent === 'true') {
        query = query.eq('is_urgent', true);
      }

      if (categories) {
        const categoriesArray = typeof categories === 'string' ? categories.split(',') : Array.isArray(categories) ? categories : [categories];
        query = query.or(categoriesArray.map((cat: string | ParsedQs) => `category_id.eq.${String(cat)}`).join(','));
      }

      if (subcategories) {
        const subcategoriesArray = typeof subcategories === 'string' ? subcategories.split(',') : Array.isArray(subcategories) ? subcategories : [subcategories];
        query = query.or(subcategoriesArray.map((subcat: string | ParsedQs) => `subcategory_id.eq.${String(subcat)}`).join(','));
      }

      if (budget_types) {
        const budgetTypesArray = Array.isArray(budget_types) ? budget_types : [budget_types];
        if (budgetTypesArray.includes('budget_defini')) {
          query = query.eq('budget_defini', true);
        } else if (budgetTypesArray.includes('budget_non_defini')) {
          query = query.eq('budget_defini', false);
        }
      }

      if (payment_methods) {
        const paymentMethodsArray = Array.isArray(payment_methods) ? payment_methods : [payment_methods];
        query = query.in('payment_method', paymentMethodsArray);
      }

      if (search && typeof search === 'string') {
        const searchTerm = search.toLowerCase();
        query = query.or(
          `titre.ilike.%${searchTerm}%,` +
          `description.ilike.%${searchTerm}%,` +
          `ville.ilike.%${searchTerm}%,` +
          `code_postal.ilike.%${searchTerm}%`
        );
      }

      // Appliquer le tri si spécifié
      if (sort_by === 'date_creation') {
        query = query.order('created_at', { ascending: false });
      }

      // Paginer les résultats
      query = query.range(offset, offset + limit - 1);

      // Exécuter la requête principale
      const { data: missions, error } = await query;

      if (error) {
        logger.error('Erreur lors de la requête Supabase:', error);
        throw error;
      }

      // Récupérer les profils des utilisateurs pour toutes les missions
      const userIds = [...new Set(missions.map(m => m.user_id))];
      const { data: userProfiles, error: profileError } = await supabase
        .from('users')
        .select(`
          id,
          profil_verifier,
          user_profil!user_id (
            nom,
            prenom,
            type_de_profil,
            photo_url,
            statut_entreprise,
            slug
          )
        `)
        .in('id', userIds);

      if (profileError) {
        logger.error('Erreur lors de la récupération des profils utilisateurs:', profileError);
      }

      // Créer un map des profils pour un accès rapide avec déchiffrement
      const profilePromises = userProfiles?.map(async (user) => {
        // Déchiffrer les données de profil
        const decryptedProfil = user.user_profil?.[0] ? await await decryptProfilDataAsync(user.user_profil[0]) : null;

        return [user.id, {
          user_id: user.id,
          nom: decryptedProfil?.nom || '',
          prenom: decryptedProfil?.prenom || '',
          type_de_profil: decryptedProfil?.type_de_profil || 'particulier',
          photo_url: decryptedProfil?.photo_url || null,
          statut_entreprise: decryptedProfil?.statut_entreprise || '',
          profil_verifier: user.profil_verifier || false,
          slug: decryptedProfil?.slug || ''
        }] as [string, any];
      }) || [];

      const profileEntries = await Promise.all(profilePromises);
      const profileMap = new Map(profileEntries);

      // Ajouter les profils utilisateurs et les compteurs à chaque mission
      const missionsWithProfiles = missions.map(mission => ({
        ...mission,
        user_profile: profileMap.get(mission.user_id) || {
          nom: '',
          prenom: '',
          type_de_profil: 'particulier',
          photo_url: null,
          statut_entreprise: '',
          profil_verifier: false,
          slug: ''
        },
        likes_count: mission.likes?.length || 0,
        applications_count: mission.applications?.length || 0,
        comments_count: mission.comments?.length || 0,
        user_has_liked: mission.likes?.some((like: { user_id: string }) => like.user_id === userId) || false,
        user_has_commented: mission.comments?.some((comment: { user_id: string }) => comment.user_id === userId) || false
      }));

      // Calculer s'il y a plus de missions à charger
      const hasMore = total ? offset + missions.length < total : false;

      const response = {
        data: missionsWithProfiles,
        hasMore,
        total: total || 0
      };

      // Mettre en cache les résultats
      await redis.set(cacheKey, JSON.stringify(response), 'EX', CACHE_DURATION);
      logger.info('Toutes les missions mises en cache');

      res.json(response);
    } catch (error) {
      logger.error('Erreur lors de la récupération des missions:', error);
      res.status(500).json({ error: 'Erreur lors de la récupération des missions' });
    }
  },

  // Récupérer les missions correspondant aux services de l'utilisateur
  getMatchingMissions: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const showRejected = req.query.showRejected === 'true';
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const offset = (page - 1) * limit;

      // Récupérer les paramètres de filtrage
      const {
        status,
        search,
        categories,
        subcategories,
        budget_types,
        payment_methods,
        is_urgent,
        offres_envoyees, // Ajout du paramètre
        liked,
        sort_by
      } = req.query;

      // Générer une clé de cache unique pour cette requête
      const cacheKey = generateCacheKey('matching_missions', {
        userId,
        showRejected,
        page,
        limit,
        status,
        search,
        categories,
        subcategories,
        budget_types,
        payment_methods,
        is_urgent,
        offres_envoyees,
        liked,
        sort_by
      });

      // Vérifier si les données sont en cache
      const cachedData = await redis.get(cacheKey);
      if (cachedData) {
        logger.info('Récupération des missions correspondantes depuis le cache');
        return res.json(JSON.parse(cachedData));
      }

      logger.info('Paramètres de filtrage reçus:', {
        status,
        search,
        categories,
        subcategories,
        budget_types,
        payment_methods,
        is_urgent,
        showRejected
      });

      // Récupérer les services de l'utilisateur
      const { data: services, error: servicesError } = await supabase
        .from('user_services')
        .select('category_id, subcategory_id')
        .eq('user_id', userId)
        .eq('statut', 'actif');

      if (servicesError) throw servicesError;

      if (!services || services.length === 0) {
        const emptyResponse = {
          data: [],
          hasMore: false,
          total: 0
        };

        return res.json(emptyResponse);
      }

      // Récupérer les missions rejetées par l'utilisateur
      const { data: rejectedMissions, error: rejectedError } = await supabase
        .from('user_mission_rejections')
        .select('mission_id')
        .eq('user_id', userId);

      if (rejectedError) throw rejectedError;

      // Créer un tableau des IDs de missions rejetées
      const rejectedMissionIds = rejectedMissions?.map(rm => rm.mission_id) || [];

      // Créer les conditions pour la requête
      const conditions = services.map(service => ({
        category_id: service.category_id,
        subcategory_id: service.subcategory_id
      }));

      // Construire la requête de base pour le comptage
      let countQuery = supabase
        .from('user_missions')
        .select('*', { count: 'exact', head: true })
        .eq('statut', 'en_cours')
        .eq('is_closed', false)
        .neq('user_id', userId);

      // Construire la requête de base pour les données
      let query = supabase
        .from('user_missions')
        .select(`
          *,
          photos:user_mission_photos (
            id,
            photo_url,
            order_index
          ),
          applications:user_mission_candidature (
            id,
            jobbeur_id,
            statut,
            created_at
          ),
          likes:user_mission_likes (
            id,
            user_id
          ),
          comments:user_mission_comments (
            id,
            user_id
          )
        `)
        .eq('statut', 'en_cours')
        .eq('is_closed', false)
        .neq('user_id', userId);

      // Ajouter la condition pour les missions rejetées aux deux requêtes
      if (rejectedMissionIds.length > 0) {
        if (showRejected) {
          countQuery = countQuery.filter('id', 'in', `(${rejectedMissionIds.join(',')})`);
          query = query.filter('id', 'in', `(${rejectedMissionIds.join(',')})`);
        } else {
          countQuery = countQuery.filter('id', 'not.in', `(${rejectedMissionIds.join(',')})`);
          query = query.filter('id', 'not.in', `(${rejectedMissionIds.join(',')})`);
        }
      } else if (showRejected) {
        const emptyResponse = {
          data: [],
          hasMore: false,
          total: 0
        };

        return res.json(emptyResponse);
      }

      // Ajouter les conditions de catégorie aux deux requêtes
      const categoryConditions = conditions.map(c => `and(category_id.eq.${c.category_id},subcategory_id.eq.${c.subcategory_id})`).join(',');
      countQuery = countQuery.or(categoryConditions);
      query = query.or(categoryConditions);

      // Appliquer les filtres supplémentaires
      if (status) {
        const statusArray = convertStatusToArray(status);
        countQuery = countQuery.in('statut', statusArray);
        query = query.in('statut', statusArray);
      }

      if (is_urgent === 'true') {
        countQuery = countQuery.eq('is_urgent', true);
        query = query.eq('is_urgent', true);
      }

      if (categories) {
        const categoriesArray = typeof categories === 'string' ? categories.split(',') : Array.isArray(categories) ? categories : [categories];
        countQuery = countQuery.in('category_id', categoriesArray);
        query = query.or(categoriesArray.map((cat: string | ParsedQs) => `category_id.eq.${String(cat)}`).join(','));
      }

      if (subcategories) {
        const subcategoriesArray = typeof subcategories === 'string' ? subcategories.split(',') : Array.isArray(subcategories) ? subcategories : [subcategories];
        countQuery = countQuery.or(subcategoriesArray.map((subcat: string | ParsedQs) => `subcategory_id.eq.${String(subcat)}`).join(','));
        query = query.or(subcategoriesArray.map((subcat: string | ParsedQs) => `subcategory_id.eq.${String(subcat)}`).join(','));
      }

      if (budget_types) {
        const budgetTypesArray = Array.isArray(budget_types) ? budget_types : [budget_types];
        if (budgetTypesArray.includes('budget_defini')) {
          countQuery = countQuery.eq('budget_defini', true);
          query = query.eq('budget_defini', true);
        } else if (budgetTypesArray.includes('budget_non_defini')) {
          countQuery = countQuery.eq('budget_defini', false);
          query = query.eq('budget_defini', false);
        }
      }

      if (payment_methods) {
        const paymentMethodsArray = Array.isArray(payment_methods) ? payment_methods : [payment_methods];
        countQuery = countQuery.in('payment_method', paymentMethodsArray);
        query = query.in('payment_method', paymentMethodsArray);
      }

      if (search && typeof search === 'string') {
        const searchTerm = search.toLowerCase();
        countQuery = countQuery.or(
          `titre.ilike.%${searchTerm}%,` +
          `description.ilike.%${searchTerm}%,` +
          `ville.ilike.%${searchTerm}%,` +
          `code_postal.ilike.%${searchTerm}%`
        );
        query = query.or(
          `titre.ilike.%${searchTerm}%,` +
          `description.ilike.%${searchTerm}%,` +
          `ville.ilike.%${searchTerm}%,` +
          `code_postal.ilike.%${searchTerm}%`
        );
      }

      // Filtre pour les missions aimées
      if (liked === 'true') {
        const { data: likedMissions } = await supabase
          .from('user_mission_likes')
          .select('mission_id')
          .eq('user_id', userId);

        if (likedMissions && likedMissions.length > 0) {
          const likedMissionIds = likedMissions.map(lm => lm.mission_id);
          countQuery = countQuery.filter('id', 'in', `(${likedMissionIds.join(',')})`);
          query = query.filter('id', 'in', `(${likedMissionIds.join(',')})`);
        } else {
          // Si l'utilisateur n'a aimé aucune mission, retourner un tableau vide
          return res.json({
            data: [],
            hasMore: false,
            total: 0
          });
        }
      }

      // Exécuter la requête de comptage
      const { count, error: countError } = await countQuery;

      if (countError) {
        logger.error('Erreur lors du comptage des missions:', countError);
        throw countError;
      }

      // Appliquer le tri si spécifié
      if (sort_by === 'date_creation' || !sort_by) {
        query = query.order('created_at', { ascending: false });
      }

      // Ajouter la pagination à la requête principale
      query = query.range(offset, offset + limit - 1);

      // Exécuter la requête principale
      const { data: missions, error } = await query;

      if (error) {
        logger.error('Erreur lors de la requête Supabase:', error);
        throw error;
      }

      // Récupérer les profils des utilisateurs pour toutes les missions
      const userIds = [...new Set(missions.map(m => m.user_id))];
      const { data: userProfiles, error: profileError } = await supabase
        .from('users')
        .select(`
          id,
          profil_verifier,
          user_profil!user_id (
            nom,
            prenom,
            type_de_profil,
            photo_url,
            statut_entreprise,
            slug
          )
        `)
        .in('id', userIds);

      if (profileError) {
        logger.error('Erreur lors de la récupération des profils utilisateurs:', profileError);
      }

      // Créer un map des profils pour un accès rapide avec déchiffrement
      const profilePromises = userProfiles?.map(async (user) => {
        // Déchiffrer les données de profil
        const decryptedProfil = user.user_profil?.[0] ? await await decryptProfilDataAsync(user.user_profil[0]) : null;

        return [user.id, {
          user_id: user.id,
          nom: decryptedProfil?.nom || '',
          prenom: decryptedProfil?.prenom || '',
          type_de_profil: decryptedProfil?.type_de_profil || 'particulier',
          photo_url: decryptedProfil?.photo_url || null,
          statut_entreprise: decryptedProfil?.statut_entreprise || '',
          profil_verifier: user.profil_verifier || false,
          slug: decryptedProfil?.slug || ''
        }] as [string, any];
      }) || [];

      const profileEntries = await Promise.all(profilePromises);
      const profileMap = new Map(profileEntries);

      // Ajouter les profils utilisateurs et les compteurs à chaque mission
      const missionsWithProfiles = missions.map(mission => ({
        ...mission,
        user_profile: profileMap.get(mission.user_id) || {
          nom: '',
          prenom: '',
          type_de_profil: 'particulier',
          photo_url: null,
          statut_entreprise: '',
          profil_verifier: false,
          slug: ''
        },
        likes_count: mission.likes?.length || 0,
        applications_count: mission.applications?.length || 0,
        comments_count: mission.comments?.length || 0,
        user_has_liked: mission.likes?.some((like: { user_id: string }) => like.user_id === userId) || false,
        user_has_commented: mission.comments?.some((comment: { user_id: string }) => comment.user_id === userId) || false,
        is_rejected: rejectedMissionIds.includes(mission.id)
      }));

      const response = {
        data: missionsWithProfiles,
        hasMore: count ? offset + missions.length < count : false,
        total: count || 0
      };

      // Mettre en cache les résultats
      await redis.set(cacheKey, JSON.stringify(response), 'EX', CACHE_DURATION);
      logger.info('Missions correspondantes mises en cache');

      res.json(response);
    } catch (error) {
      logger.error('Erreur lors de la récupération des missions correspondantes:', error);
      res.status(500).json({ error: 'Erreur lors de la récupération des missions correspondantes' });
    }
  },

  // Rejeter une mission
  rejectMission: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const missionId = req.params.id;

      // Vérifier si le rejet existe déjà
      const { data: existingRejection, error: checkError } = await supabase
        .from('user_mission_rejections')
        .select('id')
        .eq('user_id', userId)
        .eq('mission_id', missionId)
        .single();

      if (existingRejection) {
        res.status(400).json({ error: 'Vous avez déjà rejeté cette mission' });
      }

      // Créer le rejet
      const { error } = await supabase
        .from('user_mission_rejections')
        .insert([{
          user_id: userId,
          mission_id: missionId
        }]);

      if (error) throw error;

      // Invalider le cache Redis pour cette mission et cet utilisateur
      await invalidateMissionCache(missionId, userId);
      logger.info(`Cache invalidé après rejet de la mission ${missionId} par l'utilisateur ${userId}`);

      res.status(200).json({ success: true, message: 'Mission rejetée avec succès' });
    } catch (error) {
      logger.error('Erreur lors du rejet de la mission:', error);
      res.status(500).json({ error: 'Erreur lors du rejet de la mission' });
    }
  },

  // Faire une proposition pour une mission
  makeProposal: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const missionId = req.params.id;
      const { amount, message } = req.body;

      // Vérifier si la mission est fermée
      const { data: mission, error: missionError } = await supabase
        .from('user_missions')
        .select('is_closed, user_id, titre, category_id, subcategory_id')
        .eq('id', missionId)
        .single();

      if (missionError) throw missionError;

      if (mission.is_closed) {
        res.status(400).json({ error: 'Cette mission est fermée et n\'accepte plus de propositions' });
      }

      // Vérifier si l'utilisateur a les services correspondants
      const { data: userServices, error: servicesError } = await supabase
        .from('user_services')
        .select('category_id, subcategory_id')
        .eq('user_id', userId)
        .eq('statut', 'actif');

      if (servicesError) throw servicesError;

      const hasMatchingService = userServices?.some(
        service => service.category_id === mission.category_id && service.subcategory_id === mission.subcategory_id
      );

      if (!hasMatchingService) {
        // Vérifier si une notification a déjà été envoyée dans la dernière heure
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
        const { data: recentNotification, error: notifCheckError } = await supabase
          .from('user_notifications')
          .select('id')
          .eq('user_id', userId)
          .eq('type', 'mission')
          .eq('title', 'Service requis pour faire une proposition')
          .gte('created_at', oneHourAgo.toISOString())
          .maybeSingle();

        if (notifCheckError) {
          logger.error('Erreur lors de la vérification des notifications récentes:', notifCheckError);
        }

        // Si aucune notification récente n'a été envoyée, créer une nouvelle notification
        if (!recentNotification) {
          // Créer une notification pour guider l'utilisateur
          const { error: notifError } = await supabase
            .from('user_notifications')
            .insert([{
              user_id: userId,
              type: 'mission',
              title: 'Service requis pour faire une proposition',
              content: `Pour proposer vos services sur la mission "${mission.titre}", vous devez d'abord activer un service correspondant à la catégorie de cette mission dans votre profil dans "Mes Services".`,
              link: '/dashboard/profil',
              is_read: false,
              is_archived: false
            }]);

          if (notifError) {
            logger.error('Erreur lors de la création de la notification:', notifError);
          } else {
            logger.info('Notification de service requis créée avec succès pour l\'utilisateur:', userId);
          }
        }

        res.status(403).json({
          error: 'Pour faire une proposition, vous devez avoir activé un service correspondant à la catégorie de cette mission dans votre profil. Rendez-vous dans la section "Mes services" de votre profil personnel pour en activer un.'
        });
      }

      // Vérifier la relation de parrainage - si l'utilisateur qui propose est parrain ou filleul du propriétaire de la mission
      const missionOwnerId = mission.user_id;

      // Récupérer l'email de l'utilisateur qui fait la proposition et du propriétaire de la mission
      const { data: userEmails, error: emailsError } = await supabase
        .from('users')
        .select('id, email')
        .in('id', [userId, missionOwnerId]);

      if (emailsError) {
        logger.error('Erreur lors de la récupération des emails:', emailsError);
        throw emailsError;
      }

      const jobbeurEmailCrypted = userEmails.find(u => u.id === userId)?.email;
      const missionOwnerEmailCrypted = userEmails.find(u => u.id === missionOwnerId)?.email;

      if (!jobbeurEmailCrypted || !missionOwnerEmailCrypted) {
        logger.error('Email non trouvé pour l\'un des utilisateurs');
        throw new Error('Email non trouvé pour l\'un des utilisateurs');
      }

      // Déchiffrer les emails avant de les utiliser
      const jobbeurEmail = await decryptDataAsync(jobbeurEmailCrypted);
      const missionOwnerEmail = await decryptDataAsync(missionOwnerEmailCrypted);

      // Vérifier si l'un est parrain de l'autre (dans les deux sens)
      // Cas 1: jobbeur est parrain du propriétaire de mission
      const { data: referrals1, error: referralsError1 } = await supabase
        .from('user_referrals')
        .select('id')
        .eq('referrer_id', userId)
        .eq('referred_id', missionOwnerId);

      // Cas 2: propriétaire de mission est parrain du jobbeur
      const { data: referrals2, error: referralsError2 } = await supabase
        .from('user_referrals')
        .select('id')
        .eq('referrer_id', missionOwnerId)
        .eq('referred_id', userId);

      if (referralsError1 || referralsError2) {
        logger.error('Erreur lors de la vérification de la relation de parrainage:', referralsError1 || referralsError2);
        throw referralsError1 || referralsError2;
      }

      // Vérifier aussi par email au cas où des comptes auraient été recréés en utilisant le hash d'email
      const jobbeurEmailHash = hashEmail(jobbeurEmail);
      const missionOwnerEmailHash = hashEmail(missionOwnerEmail);

      const { data: usersByJobbeurEmail, error: emailCheckError1 } = await supabase
        .from('users')
        .select('id')
        .eq('email_hash', jobbeurEmailHash);

      const { data: usersByMissionOwnerEmail, error: emailCheckError2 } = await supabase
        .from('users')
        .select('id')
        .eq('email_hash', missionOwnerEmailHash);

      if (emailCheckError1 || emailCheckError2) {
        logger.error('Erreur lors de la vérification des emails:', emailCheckError1 || emailCheckError2);
        throw emailCheckError1 || emailCheckError2;
      }

      // Filtrer tous les IDs autres que ceux des utilisateurs actuels
      const jobbeurRelatedIds = usersByJobbeurEmail ? usersByJobbeurEmail.map(u => u.id).filter(id => id !== userId) : [];
      const missionOwnerRelatedIds = usersByMissionOwnerEmail ? usersByMissionOwnerEmail.map(u => u.id).filter(id => id !== missionOwnerId) : [];

      // Vérifier s'il existe des relations de parrainage avec des comptes ayant le même email
      let additionalReferrals: any[] = [];

      // Pour chaque compte lié au jobbeur, vérifier les relations avec le propriétaire de mission
      for (const relatedId of jobbeurRelatedIds) {
        const { data: rel1, error: relError1 } = await supabase
          .from('user_referrals')
          .select('id')
          .eq('referrer_id', relatedId)
          .eq('referred_id', missionOwnerId);

        const { data: rel2, error: relError2 } = await supabase
          .from('user_referrals')
          .select('id')
          .eq('referrer_id', missionOwnerId)
          .eq('referred_id', relatedId);

        if (rel1 && rel1.length > 0) additionalReferrals = additionalReferrals.concat(rel1);
        if (rel2 && rel2.length > 0) additionalReferrals = additionalReferrals.concat(rel2);
      }

      // Pour chaque compte lié au propriétaire, vérifier les relations avec le jobbeur
      for (const relatedId of missionOwnerRelatedIds) {
        const { data: rel1, error: relError1 } = await supabase
          .from('user_referrals')
          .select('id')
          .eq('referrer_id', relatedId)
          .eq('referred_id', userId);

        const { data: rel2, error: relError2 } = await supabase
          .from('user_referrals')
          .select('id')
          .eq('referrer_id', userId)
          .eq('referred_id', relatedId);

        if (rel1 && rel1.length > 0) additionalReferrals = additionalReferrals.concat(rel1);
        if (rel2 && rel2.length > 0) additionalReferrals = additionalReferrals.concat(rel2);
      }

      // Si une relation de parrainage est détectée, bloquer la proposition
      if ((referrals1 && referrals1.length > 0) ||
          (referrals2 && referrals2.length > 0) ||
          additionalReferrals.length > 0) {

        // Créer une notification pour informer l'utilisateur
        const { error: notifError } = await supabase
          .from('user_notifications')
          .insert([{
            user_id: userId,
            type: 'mission',
            title: 'Proposition bloquée',
            content: 'Vous ne pouvez pas proposer vos services à un utilisateur avec lequel vous avez une relation de parrainage (parrain ou filleul).',
            is_read: false,
            is_archived: false
          }]);

        if (notifError) {
          logger.error('Erreur lors de la création de la notification de blocage:', notifError);
        }

        // Journaliser la tentative de fraude potentielle
        const { error: logError } = await supabase
          .from('user_referrals_abuse_logs')
          .insert([{
            user_id: userId,
            related_user_id: missionOwnerId,
            type: 'mission_proposal_blocked',
            severity: 'medium',
            status: 'detected',
            description: JSON.stringify({
              mission_id: missionId,
              jobbeur_email: jobbeurEmail,
              mission_owner_email: missionOwnerEmail,
              jobbeur_related_ids: jobbeurRelatedIds,
              mission_owner_related_ids: missionOwnerRelatedIds
            })
          }]);

        if (logError) {
          logger.error('Erreur lors de la journalisation de l\'abus potentiel:', logError);
        }

        res.status(403).json({
          error: 'Vous ne pouvez pas proposer vos services à un utilisateur avec lequel vous avez une relation de parrainage (parrain ou filleul).'
        });
        return;
      }

      // Vérifier si une proposition existe déjà
      const { data: existingProposal, error: checkError } = await supabase
        .from('user_mission_candidature')
        .select('id')
        .eq('mission_id', missionId)
        .eq('jobbeur_id', userId)
        .single();

      if (existingProposal) {
        res.status(400).json({ error: 'Vous avez déjà fait une proposition pour cette mission' });
        return;
      }

      // === Limite d'abonnement : nombre de propositions par mois ===
      const { getUserSubscriptionLimits } = require('../routes/configSubscriptions');
      const subscription = await getUserSubscriptionLimits(userId);
      const isPremium = subscription.isPremium;
      const missionResponsesLimit = subscription.missionResponsesLimit || 5;

      if (!isPremium && missionResponsesLimit > 0) {
        // Compter le nombre de propositions envoyées ce mois-ci
        const startOfMonth = new Date();
        startOfMonth.setDate(1);
        startOfMonth.setHours(0, 0, 0, 0);
        const { count: proposalsCount, error: countError } = await supabase
          .from('user_mission_candidature')
          .select('id', { count: 'exact', head: true })
          .eq('jobbeur_id', userId)
          .gte('created_at', startOfMonth.toISOString());
        if (countError) {
          logger.error('Erreur lors du comptage des propositions:', countError);
        }
        const proposalsCountSafe = proposalsCount ?? 0;
        if (proposalsCountSafe >= missionResponsesLimit) {
          res.status(403).json({
            error: `Limite atteinte : vous avez déjà envoyé ${missionResponsesLimit} propositions ce mois-ci avec le plan gratuit. Passez Premium pour lever cette limite illimitée !`
          });
          return;
        }
      }

      // Créer la proposition
      const { data: proposal, error } = await supabase
        .from('user_mission_candidature')
        .insert([{
          mission_id: missionId,
          jobbeur_id: userId,
          message,
          montant_propose: amount,
          statut: 'en_attente'
        }])
        .select()
        .single();

      if (error) throw error;

      // Récupérer les informations du jobbeur
      const { data: jobbeur, error: jobbeurError } = await supabase
        .from('user_profil')
        .select('nom, prenom')
        .eq('user_id', userId)
        .single();

      if (jobbeurError) throw jobbeurError;

      // Déchiffrer les données du jobbeur
      const decryptedJobbeur = jobbeur ? await await decryptProfilDataAsync(jobbeur) : null;

      // Créer une notification pour le propriétaire de la mission
      const { error: notifError } = await supabase
        .from('user_notifications')
        .insert([{
          user_id: mission.user_id,
          type: 'mission',
          title: 'Nouvelle proposition pour votre mission',
          content: `${decryptedJobbeur?.prenom} ${decryptedJobbeur?.nom?.charAt(0).toUpperCase()}. a fait une proposition de ${amount}€ pour votre mission "${mission.titre}"`,
          link: `/dashboard/missions/offres?tab=1&mission=${missionId}`,
          is_read: false,
          is_archived: false
        }]);

        // Créer une notification pour le jobbeur
        const { error: notifErrorJobbeur } = await supabase
          .from('user_notifications')
          .insert([{
            user_id: userId,
            type: 'mission',
            title: 'Vous avez envoyé une proposition pour une mission',
            content: `Vous avez envoyé une proposition de ${amount}€ pour la mission "${mission.titre}"`,
            link: `/dashboard/missions/offres?tab=0&mission=${missionId}`,
            is_read: false,
            is_archived: false
          }]);

      if (notifError) {
        logger.error('Erreur lors de la création de la notification:', notifError);
      }

      if (notifErrorJobbeur) {
        logger.error('Erreur lors de la création de la notification:', notifErrorJobbeur);
      }

      // Envoyer un email au propriétaire de la mission
      const { data: missionOwner, error: ownerError } = await supabase
        .from('users')
        .select('email')
        .eq('id', mission.user_id)
        .single();

      if (!ownerError && missionOwner) {
        // Déchiffrer l'email du propriétaire de la mission
        const decryptedMissionOwner = await decryptUserDataAsync(missionOwner);

        await sendNewProposalEmail(decryptedMissionOwner.email, {
          missionTitle: mission.titre,
          jobbeurName: `${decryptedJobbeur?.prenom} ${decryptedJobbeur?.nom?.charAt(0).toUpperCase()}.`,
          amount: amount,
          message: message,
          missionId: missionId
        });
        logger.info(`[EMAIL_SENT] Email de nouvelle proposition envoyé`, { email: decryptedMissionOwner.email });
      }

      res.status(201).json({
        message: 'Proposition envoyée avec succès',
        proposal: {
          id: proposal.id,
          montant: amount,
          message: message,
          statut: 'en_attente'
        }
      });

      // Journaliser l'activité de l'utilisateur
      if (userId) {
        await logUserActivity(
          userId,
          'mission_proposal_create',
          missionId,
          'mission',
          {
            proposalId: proposal.id,
            amount: amount,
            missionTitle: mission.titre,
            hasMessage: !!message,
            missionOwnerId: mission.user_id
          },
          getIpFromRequest(req)
        );
      }

      // Invalider le cache pour l'expéditeur de l'offre
      await invalidateMissionCache(missionId, userId);

      // Invalider également le cache pour le destinataire de l'offre (propriétaire de la mission)
      await invalidateMissionCache(missionId, mission.user_id);

    } catch (error) {
      logger.error('Erreur lors de l\'envoi de la proposition:', error);
      res.status(500).json({ error: 'Erreur lors de l\'envoi de la proposition' });
    }
  },

  // Faire une contre-offre pour une proposition
  makeCounterOffer: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const missionId = req.params.id;
      const { amount, message, proposalId } = req.body;

      // Vérifier si la mission existe et appartient à l'utilisateur
      const { data: mission, error: missionError } = await supabase
        .from('user_missions')
        .select('id, user_id, titre, is_closed')
        .eq('id', missionId)
        .single();

      if (missionError) {
        logger.error('Erreur lors de la récupération de la mission:', missionError);
        return res.status(500).json({ error: 'Erreur lors de la récupération de la mission' });
      }

      if (!mission) {
        return res.status(404).json({ error: 'Mission non trouvée' });
      }

      if (mission.is_closed) {
        res.status(400).json({ error: 'Cette mission est fermée et n\'accepte plus de propositions' });
      }

      // Vérifier que l'utilisateur est le propriétaire de la mission
      if (mission.user_id !== userId) {
        res.status(403).json({ error: 'Vous n\'êtes pas autorisé à faire une contre-offre pour cette mission' });
      }

      // Récupérer la proposition spécifique si proposalId est fourni, sinon la dernière proposition en attente
      let query = supabase
        .from('user_mission_candidature')
        .select('id, jobbeur_id, montant_propose, montant_contre_offre_jobbeur, statut');

      if (proposalId) {
        // Si un proposalId est fourni, on récupère cette proposition spécifique
        query = query.eq('id', proposalId).eq('mission_id', missionId);
      } else {
        // Sinon, on récupère la dernière proposition en attente
        query = query.eq('mission_id', missionId)
          .eq('statut', 'en_attente')
          .order('created_at', { ascending: false })
          .limit(1);
      }

      const { data: application, error: applicationError } = await query.single();

      if (applicationError) {
        logger.error('Erreur lors de la récupération de la proposition:', applicationError);
        return res.status(500).json({ error: 'Erreur lors de la récupération de la proposition' });
      }

      if (!application) {
        return res.status(404).json({ error: 'Proposition non trouvée' });
      }

      // Vérifier que la proposition est dans un état qui permet une contre-offre
      if (application.statut !== 'en_attente' && application.statut !== 'contre_offre_jobbeur') {
        res.status(400).json({ error: 'Cette proposition ne peut pas recevoir de contre-offre dans son état actuel' });
      }

      // Vérifier que le montant de la contre-offre est différent du montant proposé par le jobbeur
      // const currentAmount = application.statut === 'contre_offre_jobbeur'
      //   ? application.montant_contre_offre_jobbeur
      //   : application.montant_propose;

      // if (amount === currentAmount) {
      //   res.status(400).json({
      //     error: 'Le montant de la contre-offre doit être différent du montant actuel proposé',
      //     message: 'Le montant de la contre-offre doit être différent du montant actuel proposé',
      //     success: false,
      //     toastType: "warning"
      //   });
      // }

      // Mettre à jour la proposition avec la contre-offre
      const { error: updateError } = await supabase
        .from('user_mission_candidature')
        .update({
          statut: 'contre_offre',
          montant_contre_offre: amount,
          message_contre_offre: message,
          date_contre_offre: new Date().toISOString()
        })
        .eq('id', application.id);

      if (updateError) {
        logger.error('Erreur lors de la mise à jour de la proposition:', updateError);
        return res.status(500).json({ error: 'Erreur lors de la mise à jour de la proposition' });
      }

      // Invalider le cache pour la mission et les propositions
      await invalidateMissionCache(missionId, userId);
      await invalidateMissionCache(missionId, application.jobbeur_id);
      logger.info('Cache invalidé après contre-offre:', { missionId, userId, jobbeurId: application.jobbeur_id });

      // Récupérer les informations du propriétaire de la mission
      const { data: missionOwner, error: ownerError } = await supabase
        .from('user_profil')
        .select('nom, prenom')
        .eq('user_id', userId)
        .single();

      if (ownerError) {
        logger.error('Erreur lors de la récupération du profil:', ownerError);
      }

      // Déchiffrer les données du propriétaire
      const decryptedMissionOwner = missionOwner ? await await decryptProfilDataAsync(missionOwner) : null;

      // Créer une notification pour le jobbeur
      const { error: notifError } = await supabase
        .from('user_notifications')
        .insert([{
          user_id: application.jobbeur_id,
          type: 'mission',
          title: 'Contre-offre reçue',
          content: `${decryptedMissionOwner?.prenom} ${decryptedMissionOwner?.nom?.charAt(0).toUpperCase()}. a fait une contre-offre de ${amount}€ pour la mission "${mission.titre}"`,
          link: `/dashboard/missions/offres?tab=0&mission=${missionId}`,
          is_read: false,
          is_archived: false
        }]);

      if (notifError) {
        logger.error('Erreur lors de la création de la notification:', notifError);
      }

      // Envoyer un email au jobbeur
      const { data: jobbeur, error: jobbeurError } = await supabase
        .from('users')
        .select('email')
        .eq('id', application.jobbeur_id)
        .single();

      if (!jobbeurError && jobbeur) {
        // Déchiffrer l'email du jobbeur
        const decryptedJobbeurEmail = await decryptUserDataAsync(jobbeur);

        await sendCounterOfferEmail(decryptedJobbeurEmail.email, {
          missionTitle: mission.titre,
          ownerName: `${decryptedMissionOwner?.prenom} ${decryptedMissionOwner?.nom?.charAt(0).toUpperCase()}.`,
          originalAmount: application.montant_contre_offre_jobbeur || application.montant_propose,
          counterOfferAmount: amount,
          message: message,
          missionId: missionId
        });
        logger.info(`[EMAIL_SENT] Email de contre-offre envoyé`, { email: decryptedJobbeurEmail.email });
      }

      res.status(200).json({
        message: 'Contre-offre envoyée avec succès',
        counterOffer: {
          amount: amount,
          message: message,
          date: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('Erreur lors de l\'envoi de la contre-offre:', error);
      res.status(500).json({ error: 'Erreur lors de l\'envoi de la contre-offre' });
    }
  },

  // Fermer une mission
  closeMission: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const missionId = req.params.id;
      const { statut } = req.body;

      // Vérifier que l'utilisateur est le propriétaire de la mission
      const { data: mission, error: missionError } = await supabase
        .from('user_missions')
        .select('user_id, is_closed, statut, titre')
        .eq('id', missionId)
        .single();

      if (missionError) throw missionError;

      if (mission.user_id !== userId) {
        res.status(403).json({ error: 'Vous n\'êtes pas autorisé à fermer cette mission' });
      }

      if (mission.is_closed) {
        res.status(400).json({ error: 'Cette mission est déjà fermée' });
      }

      // Récupérer les candidatures en cours pour cette mission
      const { data: pendingProposals, error: proposalsError } = await supabase
        .from('user_mission_candidature')
        .select(`
          id,
          jobbeur_id,
          statut,
          montant_propose,
          montant_contre_offre,
          montant_contre_offre_jobbeur
        `)
        .eq('mission_id', missionId)
        .in('statut', ['en_attente', 'contre_offre', 'contre_offre_jobbeur']);

      if (proposalsError) throw proposalsError;

      // Si la mission est annulée, envoyer un email à tous les jobbeurs ayant fait une offre
      if (statut === 'annulee' && pendingProposals && pendingProposals.length > 0) {
        // Récupérer les emails des jobbeurs
        const jobbeurIds = pendingProposals.map(proposal => proposal.jobbeur_id);
        const { data: jobbeurs, error: jobbeursError } = await supabase
          .from('users')
          .select('id, email')
          .in('id', jobbeurIds);

        if (jobbeursError) throw jobbeursError;

        // Refuser toutes les candidatures en attente
        for (const proposal of pendingProposals) {
          const { error: updateError } = await supabase
            .from('user_mission_candidature')
            .update({
              statut: 'refusée',
              date_refus: new Date().toISOString()
            })
            .eq('id', proposal.id);

          if (updateError) {
            logger.error('Erreur lors du refus automatique de la candidature:', updateError);
            continue;
          }

          // Envoyer une notification au jobbeur
          const { error: notifError } = await supabase
            .from('user_notifications')
            .insert([{
              user_id: proposal.jobbeur_id,
              type: 'mission',
              title: 'Mission annulée',
              content: `La mission "${mission.titre}" pour laquelle vous avez fait une offre a été annulée par le client.`,
              link: `/dashboard/missions/propositions`,
              is_read: false,
              is_archived: false
            }]);

          if (notifError) {
            logger.error('Erreur lors de la création de la notification:', notifError);
          }
        }

        // Envoyer les emails aux jobbeurs
        if (jobbeurs && jobbeurs.length > 0) {
          for (const jobbeur of jobbeurs) {
            try {
              // Déchiffrer l'email du jobbeur
              const decryptedJobbeurEmail = await decryptUserDataAsync(jobbeur);

              await sendMissionCancelledEmail(decryptedJobbeurEmail.email, {
                missionTitle: mission.titre,
                missionId: missionId
              });
              logger.info(`[EMAIL_SENT] Email de mission annulée envoyé`, { email: decryptedJobbeurEmail.email });
            } catch (emailError) {
              logger.error('Erreur lors de l\'envoi de l\'email de mission annulée:', emailError);
            }
          }
        }
      } else if (statut === 'terminee' && pendingProposals && pendingProposals.length > 0) {
        // Si la mission est terminée, vérifier s'il y a une candidature acceptée
        const { data: acceptedProposal, error: acceptedError } = await supabase
          .from('user_mission_candidature')
          .select('id')
          .eq('mission_id', missionId)
          .eq('statut', 'acceptée')
          .single();

        // S'il n'y a pas de candidature acceptée, demander à l'utilisateur d'en choisir une
        if (acceptedError || !acceptedProposal) {
          res.status(400).json({
            error: 'Vous devez d\'abord accepter une candidature avant de terminer la mission',
            pendingProposals: pendingProposals.length
          });
        }
      }

      // Fermer la mission
      const { error } = await supabase
        .from('user_missions')
        .update({
          is_closed: true,
          statut: statut,
          updated_at: new Date().toISOString()
        })
        .eq('id', missionId);

      if (error) throw error;

      // Invalider le cache pour la mission
      await invalidateMissionCache(missionId, userId);

      // Invalider également le cache pour tous les jobbeurs qui ont fait une proposition
      if (pendingProposals && pendingProposals.length > 0) {
        for (const proposal of pendingProposals) {
          await invalidateMissionCache(missionId, proposal.jobbeur_id);
        }
      }

      logger.info('Cache invalidé après fermeture de mission:', { missionId, userId });

      // Si la mission est terminée et qu'il y a une candidature acceptée, récompenser le parrainage
      if (statut === 'terminee') {
        logger.info('Mission marquée comme terminée, vérification du parrainage...');

        // Récupérer les propositions acceptées pour cette mission
        const { data: acceptedProposals, error: proposalsError } = await supabase
          .from('user_mission_candidature')
          .select('jobbeur_id')
          .eq('mission_id', missionId)
          .eq('statut', 'acceptée');

        if (proposalsError) {
          logger.error('Erreur lors de la récupération des propositions acceptées:', proposalsError);
        } else if (acceptedProposals && acceptedProposals.length > 0) {
          // Récupérer l'ID du propriétaire de la mission (le filleul)
          const { data: missionData, error: missionError } = await supabase
            .from('user_missions')
            .select('user_id')
            .eq('id', missionId)
            .single();

          if (missionError) {
            logger.error('Erreur lors de la récupération des données de la mission:', missionError);
          } else if (missionData) {
            logger.info('Vérification du parrainage pour le propriétaire de la mission (filleul):', missionData.user_id);
            await rewardReferral(missionData.user_id);
          }
        }
      } else {
        logger.info('Mission non marquée comme terminée, pas de récompense de parrainage');
      }

      res.status(200).json({
        message: 'Mission fermée avec succès',
        statut: statut,
        pendingProposalsCount: pendingProposals ? pendingProposals.length : 0
      });

      // Journaliser l'activité de l'utilisateur
      if (userId) {
        await logUserActivity(
          userId,
          'mission_close',
          missionId,
          'mission',
          {
            status: statut,
            missionTitle: mission.titre,
            pendingProposalsCount: pendingProposals ? pendingProposals.length : 0
          },
          getIpFromRequest(req)
        );
      }

    } catch (error) {
      logger.error('Erreur lors de la fermeture de la mission:', error);
      res.status(500).json({ error: 'Erreur lors de la fermeture de la mission' });
    }
  },

  // Liker une mission
  likeMission: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const missionId = req.params.id;

      // Vérifier si le like existe déjà
      const { data: existingLike, error: checkError } = await supabase
        .from('user_mission_likes')
        .select('id')
        .eq('user_id', userId)
        .eq('mission_id', missionId)
        .single();

      if (existingLike) {
        // Si le like existe, on le supprime
        const { error } = await supabase
          .from('user_mission_likes')
          .delete()
          .eq('user_id', userId)
          .eq('mission_id', missionId);

        if (error) throw error;

        // Invalider le cache après avoir retiré le like
        await invalidateMissionCache(missionId, userId);
        logger.info('Cache invalidé après retrait de like:', { missionId, userId });

        res.json({ message: 'Like retiré avec succès' });

        // Journaliser l'activité de l'utilisateur - unlike
        if (userId) {
          await logUserActivity(
            userId,
            'mission_unlike',
            missionId,
            'mission',
            {
              actionType: 'unlike'
            },
            getIpFromRequest(req)
          );
        }
      } else {
        // Sinon, on l'ajoute
        const { error } = await supabase
          .from('user_mission_likes')
          .insert([{
            user_id: userId,
            mission_id: missionId
          }]);

        if (error) throw error;

        // Invalider le cache après avoir ajouté le like
        await invalidateMissionCache(missionId, userId);
        logger.info('Cache invalidé après ajout de like:', { missionId, userId });

        res.json({ message: 'Mission likée avec succès' });

        // Journaliser l'activité de l'utilisateur - like
        if (userId) {
          await logUserActivity(
            userId,
            'mission_like',
            missionId,
            'mission',
            {
              actionType: 'like'
            },
            getIpFromRequest(req)
          );
        }
      }
    } catch (error) {
      logger.error('Erreur lors du like/unlike de la mission:', error);
      res.status(500).json({ error: 'Erreur lors du like/unlike de la mission' });
    }
  },

  // Commenter une mission
  commentMission: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const missionId = req.params.id;
      const { comment, isPrivate, parentId } = req.body;

      logger.info('Réception d\'une demande de commentaire:', {
        userId,
        missionId,
        isPrivate,
        parentId,
        commentLength: comment?.length
      });

      if (!comment || comment.trim().length === 0) {
        return res.status(400).json({ error: 'Le commentaire ne peut pas être vide' });
      }

      // Modération automatique du commentaire
      try {
        // Vérifier que userId est défini
        if (!userId) {
          return res.status(401).json({ error: 'Utilisateur non authentifié' });
        }

        const contentToModerate: ContentToModerate = {
          text: stripHtml(comment),
          type: 'comment',
          contentId: `temp-${Date.now()}`,
          userId: userId
        };

        const moderationResult = await contentModerationService.moderateContent(contentToModerate);

        if (!moderationResult.isSafe) {
          logger.warn('Commentaire détecté comme inapproprié par la modération automatique', {
            userId,
            missionId,
            score: moderationResult.score,
            categories: moderationResult.categories
          });

          return res.status(400).json({
            error: 'Votre commentaire contient du contenu inapproprié et ne peut pas être publié.',
            message: 'Votre commentaire contient du contenu inapproprié et ne peut pas être publié.',
            success: false,
            toastType: 'error'
          });
        }
      } catch (moderationError) {
        // En cas d'erreur de modération, on log mais on continue
        logger.error('Erreur lors de la modération du commentaire:', moderationError);
      }

      let missionOwnerId: string | null = null;
      let parentUserId: string | null = null;

      // Si c'est une réponse, vérifier que le commentaire parent existe et récupérer les infos
      if (parentId) {
        logger.info('Réponse à un commentaire détectée, parentId:', parentId);

        // Vérifier que le commentaire parent existe et appartient à la bonne mission
        const { data: parentComment, error: parentError } = await supabase
          .from('user_mission_comments')
          .select(`
            mission_id,
            user_id,
            user:users!user_id (
              id,
              user_profil:user_profil!user_id (
                nom,
                prenom
              )
            )
          `)
          .eq('id', parentId)
          .single();

        if (parentError || !parentComment) {
          logger.error('Erreur lors de la récupération du commentaire parent:', parentError);
          return res.status(404).json({
            success: false,
            message: 'Commentaire parent non trouvé'
          });
        }

        const typedParentComment = parentComment as ParentComment;
        parentUserId = typedParentComment.user_id;

        // Récupérer les informations de la mission et son créateur
        const { data: missionInfo, error: missionError } = await supabase
          .from('user_missions')
          .select('titre, user_id')
          .eq('id', missionId)
          .single();

        if (missionError) {
          logger.error('Erreur lors de la récupération du titre de la mission:', missionError);
        } else if (missionInfo) {
          missionOwnerId = missionInfo.user_id;
        }

        // Récupérer les informations de l'utilisateur qui répond
        const { data: userProfile, error: userProfileError } = await supabase
          .from('user_profil')
          .select('nom, prenom')
          .eq('user_id', userId)
          .single();

        if (userProfileError) {
          logger.error('Erreur lors de la récupération du profil de l\'utilisateur:', userProfileError);
        }

        // Déchiffrer les données du profil utilisateur
        const decryptedUserProfile = userProfile ? await decryptProfilDataAsync(userProfile) : null;

        // Ne pas envoyer de notification si l'utilisateur répond à son propre commentaire
        if (typedParentComment.user_id !== userId) {
          // Vérifier si une notification a déjà été envoyée dans les 1 dernières minutes
          const oneMinuteAgo = new Date(Date.now() - 1 * 60 * 1000);
          const { data: recentNotification, error: notifError } = await supabase
            .from('user_notifications')
            .select()
            .eq('user_id', typedParentComment.user_id)
            .eq('type', 'mission')
            .eq('link', `/dashboard/missions/${missionId}?openComments=true&commentId=${parentId}`)
            .gte('created_at', oneMinuteAgo.toISOString())
            .maybeSingle();

          if (notifError) {
            logger.error('Erreur lors de la vérification des notifications récentes:', notifError);
          }

          if (!recentNotification) {
            const { data: notification, error: notificationError } = await supabase
              .from('user_notifications')
              .insert({
                user_id: typedParentComment.user_id,
                type: 'mission',
                title: `Nouvelle réponse sur "${missionInfo?.titre || 'Mission'}"`,
                content: `${decryptedUserProfile?.prenom || 'Utilisateur'} ${decryptedUserProfile?.nom ? decryptedUserProfile.nom.charAt(0) + '.' : ''} a répondu : "${stripHtml(comment).slice(0, 20)}${stripHtml(comment).length > 20 ? '...' : ''}"`,
                link: `/dashboard/missions/${missionId}?openComments=true&commentId=${parentId}`,
                is_read: false,
                is_archived: false
              })
              .select()
              .single();

            if (notificationError) {
              logger.error('Erreur lors de la création de la notification:', notificationError);
            } else {
              logger.info('Notification créée avec succès:', notification);
            }
          }
        }

        // Envoyer également une notification au créateur de la mission si ce n'est pas l'utilisateur qui commente
        // et si ce n'est pas déjà l'auteur du commentaire parent
        if (missionInfo && missionInfo.user_id !== userId && missionInfo.user_id !== typedParentComment.user_id) {
          // Vérifier si une notification a déjà été envoyée au créateur dans la dernière minute
          const oneMinuteAgo = new Date(Date.now() - 1 * 60 * 1000);
          const { data: recentMissionOwnerNotif, error: ownerNotifError } = await supabase
            .from('user_notifications')
            .select()
            .eq('user_id', missionInfo.user_id)
            .eq('type', 'mission')
            .eq('link', `/dashboard/missions/${missionId}?openComments=true&commentId=${parentId}`)
            .gte('created_at', oneMinuteAgo.toISOString())
            .maybeSingle();

          if (ownerNotifError) {
            logger.error('Erreur lors de la vérification des notifications récentes pour le créateur:', ownerNotifError);
          }

          if (!recentMissionOwnerNotif) {
            const { data: ownerNotification, error: ownerNotificationError } = await supabase
              .from('user_notifications')
              .insert({
                user_id: missionInfo.user_id,
                type: 'mission',
                title: `Nouvelle réponse sur votre mission "${missionInfo.titre}"`,
                content: `${decryptedUserProfile?.prenom || 'Utilisateur'} ${decryptedUserProfile?.nom ? decryptedUserProfile.nom.charAt(0) + '.' : ''} a répondu à un commentaire : "${stripHtml(comment).slice(0, 20)}${stripHtml(comment).length > 20 ? '...' : ''}"`,
                link: `/dashboard/missions/${missionId}?openComments=true&commentId=${parentId}`,
                is_read: false,
                is_archived: false
              })
              .select()
              .single();

            if (ownerNotificationError) {
              logger.error('Erreur lors de la création de la notification pour le créateur:', ownerNotificationError);
            } else {
              logger.info('Notification créée avec succès pour le créateur de la mission:', ownerNotification);
            }
          }
        }
      } else {
        // Cas d'un nouveau commentaire (pas une réponse)
        // Récupérer l'auteur de la mission
        const { data: mission, error: missionError } = await supabase
          .from('user_missions')
          .select('user_id, titre')
          .eq('id', missionId)
          .single();

        if (!missionError && mission) {
          missionOwnerId = mission.user_id;

          if (mission.user_id !== userId) {
            // Vérifier si une notification non lue existe déjà pour les nouveaux commentaires
            const oneMinuteAgo = new Date(Date.now() - 1 * 60 * 1000);
            const { data: existingNotif, error: notifError } = await supabase
              .from('user_notifications')
              .select()
              .eq('user_id', mission.user_id)
              .eq('type', 'mission_comment')
              .eq('link', `/dashboard/missions/${missionId}?openComments=true`)
              .eq('is_read', false)
              .gte('created_at', oneMinuteAgo.toISOString())
              .maybeSingle();

            // Récupérer les informations de l'utilisateur qui commente
            const { data: userProfile } = await supabase
              .from('user_profil')
              .select('nom, prenom')
              .eq('user_id', userId)
              .single();

            // Déchiffrer les données du profil utilisateur
            const decryptedUserProfileComment = userProfile ? await decryptProfilDataAsync(userProfile) : null;

            if (existingNotif) {
              // Mettre à jour la notification existante
              const { error: updateError } = await supabase
                .from('user_notifications')
                .update({
                  content: `${decryptedUserProfileComment?.prenom || 'Utilisateur'} ${decryptedUserProfileComment?.nom ? decryptedUserProfileComment.nom.charAt(0) + '.' : ''} et d'autres personnes ont commenté votre mission`,
                  updated_at: new Date().toISOString()
                })
                .eq('id', existingNotif.id);

              if (updateError) {
                logger.error('Erreur lors de la mise à jour de la notification:', updateError);
              }
            } else {
              // Créer une nouvelle notification
              const { error: createError } = await supabase
                .from('user_notifications')
                .insert({
                  user_id: mission.user_id,
                  type: 'mission_comment',
                  title: `Nouveau commentaire sur "${mission.titre}"`,
                  content: `${decryptedUserProfileComment?.prenom || 'Utilisateur'} ${decryptedUserProfileComment?.nom ? decryptedUserProfileComment.nom.charAt(0) + '.' : ''} a commenté votre mission`,
                  link: `/dashboard/missions/${missionId}?openComments=true`,
                  is_read: false,
                  is_archived: false
                });

              if (createError) {
                logger.error('Erreur lors de la création de la notification:', createError);
              }
            }
          }
        } else if (missionError) {
          logger.error('Erreur lors de la récupération de la mission:', missionError);
        }
      }

      const { data: newComment, error } = await supabase
        .from('user_mission_comments')
        .insert([{
          user_id: userId,
          mission_id: missionId,
          comment: comment.trim(),
          created_at: new Date().toISOString(),
          is_private: isPrivate || false,
          parent_id: parentId || null
        }])
        .select(`
          *,
          user:users!user_id (
            id,
            profil_verifier,
            user_profil:user_profil!user_id (
              nom,
              prenom,
              photo_url,
              type_de_profil,
              statut_entreprise,
              slug
            ),
            services:user_services!user_id (
              id,
              category_id,
              subcategory_id,
              statut
            )
          ),
          replies:user_mission_comments!parent_id (
            *,
            user:users!user_id (
              id,
              profil_verifier,
              user_profil:user_profil!user_id (
                nom,
                prenom,
                photo_url,
                type_de_profil,
                statut_entreprise,
                slug
              ),
              services:user_services!user_id (
                id,
                category_id,
                subcategory_id,
                statut
              )
            )
          )
        `)
        .single();

      if (error) {
        logger.error('Erreur lors de l\'ajout du commentaire:', error);
        return res.status(500).json({ error: 'Erreur lors de l\'ajout du commentaire' });
      }

      // Invalider le cache pour les commentaires de la mission et les détails de la mission
      await invalidateMissionCache(missionId, userId);

      // Si le commentaire est une réponse à un autre utilisateur, invalider aussi son cache
      if (parentUserId && parentUserId !== userId) {
        await invalidateMissionCache(missionId, parentUserId);
      }

      // Si le commentaire est sur la mission d'un autre utilisateur, invalider aussi son cache
      if (missionOwnerId && missionOwnerId !== userId) {
        await invalidateMissionCache(missionId, missionOwnerId);
      }

      logger.info('Cache invalidé après ajout de commentaire:', { missionId, userId });

      logger.info('Commentaire créé avec succès:', {
        commentId: newComment.id,
        parentId: newComment.parent_id
      });

      // Déchiffrer le profil de l'utilisateur du nouveau commentaire
      const decryptedUserProfile = newComment.user?.user_profil?.[0] ?
        await decryptProfilDataAsync(newComment.user.user_profil[0]) : null;

      // Ajouter les flags canEdit et isOwnComment
      const processedComment = {
        ...newComment,
        user: {
          ...newComment.user,
          user_profil: decryptedUserProfile ? [decryptedUserProfile] : []
        },
        canEdit: true,
        isOwnComment: true,
        replies: []
      };

      res.json(processedComment);

      // Journaliser l'activité de l'utilisateur
      if (userId) {
        await logUserActivity(
          userId,
          'mission_comment',
          missionId,
          'mission',
          {
            commentId: newComment.id,
            isReply: !!parentId,
            isPrivate: isPrivate || false,
            commentLength: comment.trim().length
          },
          getIpFromRequest(req)
        );
      }
    } catch (error) {
      logger.error('Erreur lors de l\'ajout du commentaire:', error);
      res.status(500).json({ error: 'Erreur lors de l\'ajout du commentaire' });
    }
  },

  // Modifier un commentaire
  updateComment: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const { commentId } = req.params;
      const { comment } = req.body;

      if (!comment || comment.trim().length === 0) {
        return res.status(400).json({ error: 'Le commentaire ne peut pas être vide' });
      }

      // Modération automatique du commentaire modifié
      try {
        // Vérifier que userId est défini
        if (!userId) {
          return res.status(401).json({ error: 'Utilisateur non authentifié' });
        }

        const contentToModerate: ContentToModerate = {
          text: stripHtml(comment),
          type: 'comment',
          contentId: commentId,
          userId: userId
        };

        const moderationResult = await contentModerationService.moderateContent(contentToModerate);

        if (!moderationResult.isSafe) {
          logger.warn('Commentaire modifié détecté comme inapproprié par la modération automatique', {
            userId,
            commentId,
            score: moderationResult.score,
            categories: moderationResult.categories
          });

          return res.status(400).json({
            error: 'Votre commentaire contient du contenu inapproprié et ne peut pas être publié.',
            message: 'Votre commentaire contient du contenu inapproprié et ne peut pas être publié.',
            success: false,
            toastType: 'error'
          });
        }
      } catch (moderationError) {
        // En cas d'erreur de modération, on log mais on continue
        logger.error('Erreur lors de la modération du commentaire modifié:', moderationError);
      }

      // Vérifier si le commentaire existe et appartient à l'utilisateur
      const { data: existingComment, error: fetchError } = await supabase
        .from('user_mission_comments')
        .select('created_at, mission_id')
        .eq('id', commentId)
        .eq('user_id', userId)
        .single();

      if (fetchError || !existingComment) {
        return res.status(404).json({ error: 'Commentaire non trouvé' });
      }

      // Vérifier si moins de 2 minutes se sont écoulées
      const createdAt = new Date(existingComment.created_at);
      const now = new Date();
      const diffInMinutes = (now.getTime() - createdAt.getTime()) / 1000 / 60;

      if (diffInMinutes > 2) {
        res.status(403).json({ error: 'Le délai de modification (2 minutes) est dépassé' });
      }

      // Récupérer le propriétaire de la mission pour invalider son cache
      let missionOwnerId = null;
      if (existingComment.mission_id) {
        const { data: mission, error: missionError } = await supabase
          .from('user_missions')
          .select('user_id')
          .eq('id', existingComment.mission_id)
          .single();

        if (!missionError && mission) {
          missionOwnerId = mission.user_id;
        }
      }

      // Mettre à jour le commentaire
      const { data: updatedComment, error } = await supabase
        .from('user_mission_comments')
        .update({ comment: comment.trim() })
        .eq('id', commentId)
        .eq('user_id', userId)
        .select(`
          *,
          user:users!user_id (
            id,
            profil_verifier,
            user_profil:user_profil!user_id (
              nom,
              prenom,
              photo_url,
              type_de_profil,
              statut_entreprise,
              slug
            )
          )
        `)
        .single();

      if (error) throw error;

      // Invalider le cache pour les commentaires de la mission
      if (existingComment.mission_id) {
        await invalidateMissionCache(existingComment.mission_id, userId);
        logger.info('Cache invalidé après modification de commentaire:', { missionId: existingComment.mission_id, userId });

        // Invalider également le cache pour le propriétaire de la mission
        if (missionOwnerId && missionOwnerId !== userId) {
          await invalidateMissionCache(existingComment.mission_id, missionOwnerId);
          logger.info('Cache invalidé pour le propriétaire de la mission après modification de commentaire:', { missionId: existingComment.mission_id, missionOwnerId });
        }
      }

      // Déchiffrer le profil de l'utilisateur du commentaire modifié
      const decryptedUserProfile = updatedComment.user?.user_profil?.[0] ?
        await decryptProfilDataAsync(updatedComment.user.user_profil[0]) : null;

      const processedComment = {
        ...updatedComment,
        user: {
          ...updatedComment.user,
          user_profil: decryptedUserProfile ? [decryptedUserProfile] : []
        }
      };

      res.json(processedComment);
    } catch (error) {
      logger.error('Erreur lors de la modification du commentaire:', error);
      res.status(500).json({ error: 'Erreur lors de la modification du commentaire' });
    }
  },

  // Supprimer un commentaire
  deleteComment: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const { commentId } = req.params;

      // Vérifier si le commentaire existe et appartient à l'utilisateur
      const { data: existingComment, error: fetchError } = await supabase
        .from('user_mission_comments')
        .select('created_at, mission_id')
        .eq('id', commentId)
        .eq('user_id', userId)
        .single();

      if (fetchError || !existingComment) {
        return res.status(404).json({ error: 'Commentaire non trouvé' });
      }

      // Vérifier si moins de 2 minutes se sont écoulées
      const createdAt = new Date(existingComment.created_at);
      const now = new Date();
      const diffInMinutes = (now.getTime() - createdAt.getTime()) / 1000 / 60;

      if (diffInMinutes > 2) {
        res.status(403).json({ error: 'Le délai de suppression (2 minutes) est dépassé' });
      }

      // Récupérer l'ID de la mission pour invalider le cache
      const missionId = existingComment.mission_id;

      // Récupérer le propriétaire de la mission pour invalider son cache
      let missionOwnerId = null;
      if (missionId) {
        const { data: mission, error: missionError } = await supabase
          .from('user_missions')
          .select('user_id')
          .eq('id', missionId)
          .single();

        if (!missionError && mission) {
          missionOwnerId = mission.user_id;
        }
      }

      // Supprimer le commentaire
      const { error } = await supabase
        .from('user_mission_comments')
        .delete()
        .eq('id', commentId)
        .eq('user_id', userId);

      if (error) throw error;

      // Invalider le cache pour les commentaires de la mission
      if (missionId) {
        await invalidateMissionCache(missionId, userId);
        logger.info('Cache invalidé après suppression de commentaire:', { missionId, userId });

        // Invalider également le cache pour le propriétaire de la mission
        if (missionOwnerId && missionOwnerId !== userId) {
          await invalidateMissionCache(missionId, missionOwnerId);
          logger.info('Cache invalidé pour le propriétaire de la mission après suppression de commentaire:', { missionId, missionOwnerId });
        }
      }

      res.json({ message: 'Commentaire supprimé avec succès' });
    } catch (error) {
      logger.error('Erreur lors de la suppression du commentaire:', error);
      res.status(500).json({ error: 'Erreur lors de la suppression du commentaire' });
    }
  },

  // Récupérer les commentaires d'une mission
  getMissionComments: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const missionId = req.params.missionId;
      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 30, 30); // Maximum 30 commentaires par page
      const offset = (page - 1) * limit;

      // Générer une clé de cache unique pour cette requête
      const cacheKey = generateCacheKey('mission_comments', { userId, missionId, page, limit });

      // Vérifier si les données sont en cache
      const cachedData = await redis.get(cacheKey);
      if (cachedData) {
        logger.info('Récupération des commentaires de mission depuis le cache');
        return res.json(JSON.parse(cachedData));
      }

      // Récupérer d'abord la mission pour vérifier si l'utilisateur en est l'auteur
      const { data: mission, error: missionError } = await supabase
        .from('user_missions')
        .select('user_id')
        .eq('id', missionId)
        .single();

      if (missionError) {
        logger.error('Erreur lors de la récupération de la mission:', missionError);
        return res.status(500).json({ error: 'Erreur lors de la récupération de la mission' });
      }

      // Vérifier si l'utilisateur est un modérateur
      const { data: userRole, error: roleError } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();

      if (roleError) {
        logger.error('Erreur lors de la vérification du rôle:', roleError);
        return res.status(500).json({ error: 'Erreur lors de la vérification du rôle' });
      }

      const isModerator = userRole?.role === 'jobmodo' || userRole?.role === 'jobpadm';
      const isMissionAuthor = mission.user_id === userId;

      // Construire la requête de base
      let query = supabase
        .from('user_mission_comments')
        .select(`
          *,
          user:users!user_id (
            id,
            profil_verifier,
            user_profil:user_profil!user_id (
              nom,
              prenom,
              photo_url,
              type_de_profil,
              statut_entreprise,
              slug
            ),
            services:user_services!user_id (
              id,
              category_id,
              subcategory_id,
              statut
            )
          ),
          replies:user_mission_comments!parent_id (
            *,
            user:users!user_id (
              id,
              profil_verifier,
              user_profil:user_profil!user_id (
                nom,
                prenom,
                photo_url,
                type_de_profil,
                statut_entreprise,
                slug
              ),
              services:user_services!user_id (
                id,
                category_id,
                subcategory_id,
                statut
              )
            )
          )
        `, { count: 'exact' })
        .eq('mission_id', missionId)
        .is('parent_id', null);

      const { data: rootComments, error: rootError, count } = await query
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (rootError) {
        logger.error('Erreur lors de la récupération des commentaires:', rootError);
        return res.status(500).json({ error: 'Erreur lors de la récupération des commentaires' });
      }

      // Traitement des commentaires avec déchiffrement en parallèle
      const commentsWithProfiles = await Promise.all(rootComments.map(async (comment) => {
        // Déchiffrer le profil de l'utilisateur du commentaire
        const decryptedUserProfile = comment.user?.user_profil?.[0] ?
          await decryptProfilDataAsync(comment.user.user_profil[0]) : null;

        return {
          ...comment,
          user: {
            ...comment.user,
            user_profil: decryptedUserProfile ? [decryptedUserProfile] : []
          },
          comment: (!comment.is_private || isModerator || isMissionAuthor || comment.user_id === userId)
            ? comment.comment
            : "Ce message est privé",
          canEdit: comment.user_id === userId &&
                  (new Date().getTime() - new Date(comment.created_at).getTime()) <= 120000, // 2 minutes
          isOwnComment: comment.user_id === userId,
          replies: await Promise.all(
            (comment.replies || [])
              .map(async (reply: any) => {
                // Déchiffrer le profil de l'utilisateur de la réponse
                const decryptedReplyUserProfile = reply.user?.user_profil?.[0] ?
                  await decryptProfilDataAsync(reply.user.user_profil[0]) : null;

                return {
                  ...reply,
                  user: {
                    ...reply.user,
                    user_profil: decryptedReplyUserProfile ? [decryptedReplyUserProfile] : []
                  },
                  comment: (!reply.is_private || isModerator || isMissionAuthor || reply.user_id === userId)
                    ? reply.comment
                    : "Ce message est privé",
                  canEdit: reply.user_id === userId &&
                          (new Date().getTime() - new Date(reply.created_at).getTime()) <= 120000,
                  isOwnComment: reply.user_id === userId
                };
              })
          ).then(replies => replies.sort((a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()))
        }
      }));

      const response = {
        comments: commentsWithProfiles,
        hasMore: count ? offset + limit < count : false,
        total: count || 0
      };

      // Mettre en cache les résultats
      // Durée de cache plus courte pour les commentaires (1 minute)
      await redis.set(cacheKey, JSON.stringify(response), 'EX', 60);
      logger.info('Commentaires de mission mis en cache');

      res.json(response);

    } catch (error) {
      logger.error('Erreur lors de la récupération des commentaires:', error);
      res.status(500).json({ error: 'Erreur lors de la récupération des commentaires' });
    }
  },

  // Recommander une mission
  recommendMission: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const missionId = req.params.id;

      // Vérifier si la recommandation existe déjà
      const { data: existingRecommendation, error: checkError } = await supabase
        .from('user_mission_recommendations')
        .select('id')
        .eq('user_id', userId)
        .eq('mission_id', missionId)
        .single();

      if (existingRecommendation) {
        // Si la recommandation existe, on la supprime
        const { error } = await supabase
          .from('user_mission_recommendations')
          .delete()
          .eq('user_id', userId)
          .eq('mission_id', missionId);

        if (error) throw error;

        res.json({ message: 'Recommandation retirée avec succès' });
      } else {
        // Sinon, on l'ajoute
        const { error } = await supabase
          .from('user_mission_recommendations')
          .insert([{
            user_id: userId,
            mission_id: missionId
          }]);

        if (error) throw error;

        res.json({ message: 'Mission recommandée avec succès' });
      }
    } catch (error) {
      logger.error('Erreur lors de la recommandation/dé-recommandation de la mission:', error);
      res.status(500).json({ error: 'Erreur lors de la recommandation/dé-recommandation de la mission' });
    }
  },

  // Récupérer les détails d'une mission
  getMissionDetails: async (req: Request, res: Response) => {
    try {
      const missionId = req.params.id;
      const userId = req.user?.userId;

      // Générer une clé de cache unique pour cette requête
      const cacheKey = generateCacheKey('mission_details', { missionId, userId });

      // Vérifier si les données sont en cache
      const cachedData = await redis.get(cacheKey);
      if (cachedData) {
        logger.info('Récupération des détails de mission depuis le cache');
        return res.json(JSON.parse(cachedData));
      }

      const { data: mission, error } = await supabase
        .from('user_missions')
        .select(`
          *,
          photos:user_mission_photos (
            id,
            photo_url,
            order_index
          ),
          applications:user_mission_candidature (
            id,
            jobbeur_id,
            statut,
            created_at
          ),
          likes:user_mission_likes (
            id,
            user_id
          ),
          comments:user_mission_comments (
            id,
            user_id,
            comment
          )
        `)
        .eq('id', missionId)
        .single();

      if (error) {
        if (error.code === 'PGRST116' || error.message?.includes('No rows')) {
          return res.status(404).json({ error: 'Mission introuvable' });
        }
        throw error;
      }

      // Récupérer le profil de l'utilisateur qui a créé la mission
      const { data: userProfile, error: profileError } = await supabase
        .from('user_profil')
        .select('user_id, nom, prenom, type_de_profil, photo_url, statut_entreprise, slug')
        .eq('user_id', mission.user_id)
        .single();

      if (profileError) {
        logger.error('Erreur lors de la récupération du profil utilisateur:', profileError);
      }

      // Déchiffrer les données du profil utilisateur
      const decryptedUserProfile = userProfile ? await decryptProfilDataAsync(userProfile) : null;

      // Ajouter les informations supplémentaires
      const missionWithDetails = {
        ...mission,
        user_profile: decryptedUserProfile || {
          nom: '',
          prenom: '',
          type_de_profil: 'particulier',
          photo_url: null,
          statut_entreprise: '',
          slug: ''
        },
        likes_count: mission.likes?.length || 0,
        comments_count: mission.comments?.length || 0,
        applications_count: mission.applications?.length || 0,
        user_has_liked: mission.likes?.some((like: { user_id: string }) => like.user_id === userId) || false,
        user_has_commented: mission.comments?.some((comment: { user_id: string }) => comment.user_id === userId) || false
      };

      // Mettre en cache les résultats
      await redis.set(cacheKey, JSON.stringify(missionWithDetails), 'EX', CACHE_DURATION);
      logger.info('Détails de mission mis en cache');

      res.json(missionWithDetails);
    } catch (error) {
      logger.error('Erreur lors de la récupération des détails de la mission:', error);
      res.status(500).json({ error: 'Erreur lors de la récupération des détails de la mission' });
    }
  },

  // Récupérer les propositions de l'utilisateur
  getPropositions: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({ error: 'Utilisateur non authentifié' });
      }

      // Générer une clé de cache unique pour cette requête
      const cacheKey = generateCacheKey('user_propositions', { userId });

      // Vérifier si les données sont en cache
      const cachedData = await redis.get(cacheKey);
      if (cachedData) {
        logger.info('Récupération des propositions depuis le cache');
        return res.json(JSON.parse(cachedData));
      }

      const { data: proposals, error } = await supabase
        .from('user_mission_candidature')
        .select(`
          *,
          mission:user_missions!inner(
            id,
            titre,
            description,
            budget,
            budget_defini,
            adresse,
            ville,
            code_postal,
            date_mission,
            category_id,
            subcategory_id,
            payment_method,
            is_urgent,
            is_closed,
            user_id,
            user:users!inner(
              id,
              profil_verifier,
              user_profil!user_id(
                nom,
                prenom,
                photo_url,
                type_de_profil,
                statut_entreprise,
                slug
              )
            )
          )
        `)
        .eq('jobbeur_id', userId)
        .order('created_at', { ascending: false });

      logger.info('Récupération des propositions reçues :', proposals);

      if (error) {
        logger.error('Erreur lors de la récupération des propositions:', error);
        return res.status(500).json({ error: 'Erreur lors de la récupération des propositions' });
      }

      const formattedProposals = await Promise.all(proposals?.map(async (proposal) => {
        // Déchiffrer les données de profil de l'utilisateur de la mission
        const decryptedMissionUserProfile = proposal.mission?.user?.user_profil?.[0] ?
          await decryptProfilDataAsync(proposal.mission.user.user_profil[0]) : null;

        return {
          ...proposal,
          mission: {
            ...proposal.mission,
            user_profile: decryptedMissionUserProfile || {
              nom: '',
              prenom: '',
              type_de_profil: 'particulier',
              photo_url: null,
              statut_entreprise: '',
              slug: ''
            }
          }
        };
      }) || []);

      logger.info('Propositions reçues récupérées et formatées :', formattedProposals);

      const response = { data: formattedProposals };

      // Mettre en cache les résultats
      await redis.set(cacheKey, JSON.stringify(response), 'EX', CACHE_DURATION);
      logger.info('Propositions mises en cache');

      res.json(response);
    } catch (error) {
      logger.error('Erreur lors de la récupération des propositions:', error);
      res.status(500).json({ error: 'Erreur lors de la récupération des propositions' });
    }
  },

  // Récupérer les propositions envoyées par l'utilisateur
  getSentProposals: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const offset = (page - 1) * limit;

      // Récupérer les paramètres de filtrage
      const {
        status,
        search,
        categories,
        subcategories,
        budget_types,
        payment_methods,
        missionId,
        profile_types,
        offer_status,
        sort_by,
        is_urgent
      } = req.query;

      // Générer une clé de cache unique pour cette requête
      const cacheKey = generateCacheKey('sent_proposals', {
        userId,
        page,
        limit,
        status,
        search,
        categories,
        subcategories,
        budget_types,
        payment_methods,
        missionId,
        profile_types,
        offer_status,
        sort_by,
        is_urgent
      });

      // Vérifier si les données sont en cache
      const cachedData = await redis.get(cacheKey);
      if (cachedData) {
        logger.info('Récupération des propositions envoyées depuis le cache');
        return res.json(JSON.parse(cachedData));
      }

      // Construire la requête avec les filtres
      let query = supabase
        .from('user_mission_candidature')
        .select(`
          *,
          mission:user_missions(
            *,
            user:users(
              id,
              user_profil(*)
            )
          )
        `, { count: 'exact' })
        .eq('jobbeur_id', userId)
        .order('created_at', { ascending: false });

      logger.info('Récupération des propositions envoyées :', query);

      if (missionId) {
        query = query.eq('mission_id', missionId);
      }

      // Filtre pour les missions urgentes
      if (is_urgent === 'true') {
        query = query.eq('mission.is_urgent', true);
      }

      // CORRECTION: Appliquer les filtres de statut correctement
      // Ignorer complètement offer_status s'il est vide ou undefined
      if (offer_status && offer_status !== 'undefined' && offer_status !== '') {
        const offerStatusArray = typeof offer_status === 'string' ? offer_status.split(',').filter(Boolean) : Array.isArray(offer_status) ? offer_status : [offer_status];
        if (offerStatusArray.length > 0) {
          logger.info('Filtrage par statut d\'offre (propositions envoyées):', {
            offer_status,
            offerStatusArray,
            userId
          });
          query = query.in('statut', offerStatusArray);
        }
      }
      // Sinon, si status est défini, l'utiliser
      else if (status) {
        const statusArray = convertStatusToArray(status);
        query = query.in('statut', statusArray);
      }

      // Fonction pour appliquer le filtre de recherche
      const applySearchFilter = (baseQuery: any, searchTerm: string) => {
        return baseQuery.filter('mission.titre', 'ilike', `%${searchTerm}%`);
      };

      // Appliquer le filtre de recherche
      if (search && typeof search === 'string') {
        const searchTerm = search.toLowerCase();
        logger.info('Application du filtre de recherche:', { searchTerm });
        try {
          query = applySearchFilter(query, searchTerm);
          logger.info('Filtre de recherche appliqué avec succès');
        } catch (error) {
          logger.error('Erreur lors de l\'application du filtre de recherche:', error);
          // En cas d'erreur, on continue sans le filtre de recherche
        }
      }

      if (categories) {
        const categoriesArray = typeof categories === 'string' ? categories.split(',') : Array.isArray(categories) ? categories : [categories];
        if (categoriesArray.length > 0) {
          query = query.in('mission.category_id', categoriesArray);
        }
      }

      if (subcategories) {
        const subcategoriesArray = typeof subcategories === 'string' ? subcategories.split(',') : Array.isArray(subcategories) ? subcategories : [subcategories];
        if (subcategoriesArray.length > 0) {
          query = query.in('mission.subcategory_id', subcategoriesArray);
        }
      }

      if (budget_types) {
        const budgetTypesArray = Array.isArray(budget_types) ? budget_types : [budget_types];
        if (budgetTypesArray.includes('budget_defini')) {
          query = query.eq('mission.budget_defini', true);
        } else if (budgetTypesArray.includes('budget_non_defini')) {
          query = query.eq('mission.budget_defini', false);
        }
      }

      if (payment_methods) {
        const paymentMethodsArray = typeof payment_methods === 'string' ? payment_methods.split(',') : Array.isArray(payment_methods) ? payment_methods : [payment_methods];
        if (paymentMethodsArray.length > 0) {
          query = query.in('mission.payment_method', paymentMethodsArray);
        }
      }

      // Nouveaux filtres pour le type de profil
      if (profile_types) {
        const profileArray = typeof profile_types === 'string' ? profile_types.split(',') : Array.isArray(profile_types) ? profile_types : [profile_types];
        if (profileArray.length > 0) {
          query = query.in('mission.user.user_profil.type_de_profil', profileArray);
        }
      }

      // Appliquer le tri
      if (sort_by) {
        switch (sort_by) {
          case 'montant_propose':
            query = query.order('montant_propose', { ascending: false });
            break;
          case 'date_contre_offre':
            query = query.order('date_contre_offre', { ascending: false, nullsFirst: false });
            break;
          case 'date_refus':
            query = query.order('date_refus', { ascending: false, nullsFirst: false });
            break;
          case 'date_acceptation':
            query = query.order('date_acceptation', { ascending: false, nullsFirst: false });
            break;
          case 'montant_contre_offre':
            query = query.order('montant_contre_offre', { ascending: false, nullsFirst: false });
            break;
          case 'montant_contre_offre_jobbeur':
            query = query.order('montant_contre_offre_jobbeur', { ascending: false, nullsFirst: false });
            break;
          case 'date_creation':
            query = query.order('created_at', { ascending: false });
            break;
          default:
            query = query.order('created_at', { ascending: false });
        }
      }

      // Appliquer la pagination
      query = query.range(offset, offset + limit - 1);

      const { data: proposals, error, count } = await query;

      if (error) {
        logger.error('Erreur lors de la récupération des propositions envoyées:', error);
        return res.status(500).json({ error: 'Erreur lors de la récupération des propositions envoyées' });
      }

      // Formater les propositions pour inclure les informations du profil utilisateur
      const formattedProposals = await Promise.all(proposals.map(async proposal => {
        // Déchiffrer les données de profil de l'utilisateur de la mission
        const decryptedMissionUserProfile = proposal.mission?.user?.user_profil?.[0] ?
        await decryptProfilDataAsync(proposal.mission.user.user_profil[0]) : null;

        return {
          ...proposal,
          mission: {
            ...proposal.mission,
            user_profile: decryptedMissionUserProfile || {
              nom: '',
              prenom: '',
              type_de_profil: 'particulier',
              photo_url: null,
              statut_entreprise: '',
              slug: ''
            }
          }
        };
      }));

      const response = {
        data: formattedProposals,
        total: count || 0,
        page,
        limit
      };

      // Mettre en cache les résultats
      await redis.set(cacheKey, JSON.stringify(response), 'EX', CACHE_DURATION);
      logger.info('Propositions envoyées mises en cache');

      res.json(response);
    } catch (error) {
      logger.error('Erreur lors de la récupération des propositions envoyées:', error);
      res.status(500).json({ error: 'Erreur lors de la récupération des propositions envoyées' });
    }
  },

  // Récupérer les propositions reçues par l'utilisateur
  getReceivedProposals: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const offset = (page - 1) * limit;

      // Récupérer les paramètres de filtrage
      const {
        status,
        search,
        categories,
        subcategories,
        budget_types,
        payment_methods,
        missionId,
        profile_types,
        offer_status,
        sort_by,
        is_urgent
      } = req.query;

      // Générer une clé de cache unique pour cette requête
      const cacheKey = generateCacheKey('received_proposals', {
        userId,
        page,
        limit,
        status,
        search,
        categories,
        subcategories,
        budget_types,
        payment_methods,
        missionId,
        profile_types,
        offer_status,
        sort_by,
        is_urgent
      });

      // Vérifier si les données sont en cache
      const cachedData = await redis.get(cacheKey);
      if (cachedData) {
        logger.info('Récupération des propositions reçues depuis le cache');
        return res.json(JSON.parse(cachedData));
      }

     // Construire la requête avec les filtres
      let query = supabase
        .from('user_mission_candidature')
        .select(`
          *,
          mission:user_missions!inner(
            *,
            user:users(
              id,
              user_profil(*)
            )
          )
        `, { count: 'exact' })
        .eq('mission.user_id', userId)
        .order('created_at', { ascending: false });

      logger.info('Récupération des propositions reçues :', query);

      if (missionId) {
        query = query.eq('mission_id', missionId);
      }

      // Filtre pour les missions urgentes
      if (is_urgent === 'true') {
        query = query.eq('mission.is_urgent', true);
      }

      // CORRECTION: Appliquer les filtres de statut correctement
      // Ignorer complètement offer_status s'il est vide ou undefined
      if (offer_status && offer_status !== 'undefined' && offer_status !== '') {
        const offerStatusArray = typeof offer_status === 'string' ? offer_status.split(',').filter(Boolean) : Array.isArray(offer_status) ? offer_status : [offer_status];
        if (offerStatusArray.length > 0) {
          logger.info('Filtrage par statut d\'offre (propositions reçues):', {
            offer_status,
            offerStatusArray,
            userId
          });
          query = query.in('statut', offerStatusArray);
        }
      }
      // Sinon, si status est défini, l'utiliser
      else if (status) {
        const statusArray = convertStatusToArray(status);
        query = query.in('statut', statusArray);
      }

      // Fonction pour appliquer le filtre de recherche
      const applySearchFilter = (baseQuery: any, searchTerm: string) => {
        return baseQuery.filter('mission.titre', 'ilike', `%${searchTerm}%`);
      };

      // Appliquer le filtre de recherche
      if (search && typeof search === 'string') {
        const searchTerm = search.toLowerCase();
        logger.info('Application du filtre de recherche:', { searchTerm });
        try {
          query = applySearchFilter(query, searchTerm);
          logger.info('Filtre de recherche appliqué avec succès');
        } catch (error) {
          logger.error('Erreur lors de l\'application du filtre de recherche:', error);
          // En cas d'erreur, on continue sans le filtre de recherche
        }
      }

      if (categories) {
        const categoriesArray = typeof categories === 'string' ? categories.split(',') : Array.isArray(categories) ? categories : [categories];
        if (categoriesArray.length > 0) {
          query = query.in('mission.category_id', categoriesArray);
        }
      }

      if (subcategories) {
        const subcategoriesArray = typeof subcategories === 'string' ? subcategories.split(',') : Array.isArray(subcategories) ? subcategories : [subcategories];
        if (subcategoriesArray.length > 0) {
          query = query.in('mission.subcategory_id', subcategoriesArray);
        }
      }

      if (budget_types) {
        const budgetTypesArray = Array.isArray(budget_types) ? budget_types : [budget_types];
        if (budgetTypesArray.includes('budget_defini')) {
          query = query.eq('mission.budget_defini', true);
        } else if (budgetTypesArray.includes('budget_non_defini')) {
          query = query.eq('mission.budget_defini', false);
        }
      }

      if (payment_methods) {
        const paymentMethodsArray = typeof payment_methods === 'string' ? payment_methods.split(',') : Array.isArray(payment_methods) ? payment_methods : [payment_methods];
        if (paymentMethodsArray.length > 0) {
          query = query.in('mission.payment_method', paymentMethodsArray);
        }
      }

      // Nouveaux filtres pour le type de profil
      if (profile_types) {
        const profileArray = typeof profile_types === 'string' ? profile_types.split(',') : Array.isArray(profile_types) ? profile_types : [profile_types];
        if (profileArray.length > 0) {
          query = query.in('mission.user.user_profil.type_de_profil', profileArray);
        }
      }

      // Appliquer le tri
      if (sort_by) {
        switch (sort_by) {
          case 'montant_propose':
            query = query.order('montant_propose', { ascending: false });
            break;
          case 'date_contre_offre':
            query = query.order('date_contre_offre', { ascending: false, nullsFirst: false });
            break;
          case 'date_refus':
            query = query.order('date_refus', { ascending: false, nullsFirst: false });
            break;
          case 'date_acceptation':
            query = query.order('date_acceptation', { ascending: false, nullsFirst: false });
            break;
          case 'montant_contre_offre':
            query = query.order('montant_contre_offre', { ascending: false, nullsFirst: false });
            break;
          case 'montant_contre_offre_jobbeur':
            query = query.order('montant_contre_offre_jobbeur', { ascending: false, nullsFirst: false });
            break;
          default:
            // Par défaut, on trie par date de création (déjà appliqué)
            break;
        }
      }

      // Ajouter la pagination
      query = query.range(offset, offset + limit - 1);

      const { data: proposals, error, count } = await query;

      if (error) {
        logger.error('Erreur lors de la récupération des propositions reçues:', error);
        throw error;
      }

      // Formater les propositions pour inclure les informations du profil utilisateur
        const formattedProposals = await Promise.all(proposals.map(async proposal => {
        // Déchiffrer les données de profil de l'utilisateur de la mission
        const decryptedMissionUserProfile = proposal.mission?.user?.user_profil?.[0] ?
        await decryptProfilDataAsync(proposal.mission.user.user_profil[0]) : null;

        return {
          ...proposal,
          mission: {
            ...proposal.mission,
            user_profile: decryptedMissionUserProfile || {
              nom: '',
              prenom: '',
              type_de_profil: 'particulier',
              photo_url: null,
              statut_entreprise: '',
              slug: ''
            }
          }
        };
      }));

      const response = {
        data: formattedProposals,
        total: count || 0,
        page,
        limit
      };

      // Mettre en cache les résultats
      await redis.set(cacheKey, JSON.stringify(response), 'EX', CACHE_DURATION);
      logger.info('Propositions reçues mises en cache');

      return res.status(200).json(response);
    } catch (error) {
      logger.error('Erreur lors de la récupération des propositions reçues:', error);
      return res.status(500).json({ message: 'Erreur serveur', success: false });
    }
  },

  // Accepter une proposition
  acceptProposal: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const missionId = req.params.id;
      const proposalId = req.params.proposalId;

      // Vérifier si la mission existe et appartient à l'utilisateur
      const { data: mission, error: missionError } = await supabase
        .from('user_missions')
        .select('id, user_id, titre, is_closed')
        .eq('id', missionId)
        .single();

      if (missionError) {
        logger.error('Erreur lors de la récupération de la mission:', missionError);
        return res.status(500).json({ error: 'Erreur lors de la récupération de la mission' });
      }

      if (!mission) {
        return res.status(404).json({ error: 'Mission non trouvée' });
      }

      if (mission.is_closed) {
        res.status(400).json({ error: 'Cette mission est fermée et n\'accepte plus de propositions' });
      }

      // Vérifier si la proposition existe
      const { data: proposal, error: proposalError } = await supabase
        .from('user_mission_candidature')
        .select('id, jobbeur_id, montant_propose, montant_contre_offre, montant_contre_offre_jobbeur, statut')
        .eq('id', proposalId)
        .eq('mission_id', missionId)
        .single();

      if (proposalError) {
        logger.error('Erreur lors de la récupération de la proposition:', proposalError);
        return res.status(500).json({ error: 'Erreur lors de la récupération de la proposition' });
      }

      if (!proposal) {
        return res.status(404).json({ error: 'Proposition non trouvée' });
      }

      if (proposal.statut !== 'en_attente' && proposal.statut !== 'contre_offre' && proposal.statut !== 'contre_offre_jobbeur') {
        res.status(400).json({ error: 'Cette proposition ne peut plus être acceptée' });
      }

      // Mettre à jour la proposition
      const { error: updateError } = await supabase
        .from('user_mission_candidature')
        .update({
          statut: 'acceptée',
          date_acceptation: new Date().toISOString()
        })
        .eq('id', proposalId);

      if (updateError) {
        logger.error('Erreur lors de la mise à jour de la proposition:', updateError);
        return res.status(500).json({ error: 'Erreur lors de la mise à jour de la proposition' });
      }

      // Mettre à jour le statut de la mission en "terminee"
      const { error: updateMissionError } = await supabase
        .from('user_missions')
        .update({
          statut: 'terminee',
          is_closed: true
        })
        .eq('id', missionId);

      if (updateMissionError) {
        logger.error('Erreur lors de la mise à jour du statut de la mission:', updateMissionError);
        return res.status(500).json({ error: 'Erreur lors de la mise à jour du statut de la mission' });
      }

      // Récupérer tous les jobbeurs qui ont postulé à cette mission
      const { data: allProposals, error: allProposalsError } = await supabase
        .from('user_mission_candidature')
        .select('jobbeur_id')
        .eq('mission_id', missionId);

      if (allProposalsError) {
        logger.error('Erreur lors de la récupération des propositions:', allProposalsError);
      } else {
        // Invalider le cache pour tous les jobbeurs qui ont postulé
        for (const prop of allProposals) {
          await invalidateMissionCache(missionId, prop.jobbeur_id);
        }
      }

      // Invalider le cache pour la mission et les propositions
      await invalidateMissionCache(missionId, userId);
      await invalidateMissionCache(missionId, proposal.jobbeur_id);
      logger.info('Cache invalidé après acceptation de proposition:', { missionId, userId, jobbeurId: proposal.jobbeur_id });

      // Récupérer les informations du propriétaire de la mission
      const { data: missionOwner, error: ownerError } = await supabase
        .from('user_profil')
        .select('nom, prenom')
        .eq('user_id', userId)
        .single();

      if (ownerError) {
        logger.error('Erreur lors de la récupération du profil:', ownerError);
      }

      // Déchiffrer les données du propriétaire de mission
      const decryptedMissionOwner = missionOwner ? await decryptProfilDataAsync(missionOwner) : null;

      // Déterminer le montant final en fonction du statut
      let montantFinal = proposal.montant_propose;
      if (proposal.statut === 'contre_offre' && proposal.montant_contre_offre) {
        montantFinal = proposal.montant_contre_offre;
      } else if (proposal.statut === 'contre_offre_jobbeur' && proposal.montant_contre_offre_jobbeur) {
        montantFinal = proposal.montant_contre_offre_jobbeur;
      }

      // Journalisation de l'action utilisateur
      if (userId) {
        await logUserActivity(
          userId,
          'proposal_accept',
          missionId,
          'mission',
          {
            proposal_id: proposalId,
            jobbeur_id: proposal.jobbeur_id,
            montant: montantFinal
          },
          getIpFromRequest(req)
        );
      }

      // Créer une notification pour le jobbeur
      const { error: notifError } = await supabase
        .from('user_notifications')
        .insert([{
          user_id: proposal.jobbeur_id,
          type: 'mission',
          title: 'Proposition acceptée',
          content: `${decryptedMissionOwner?.prenom} ${decryptedMissionOwner?.nom?.toUpperCase()?.charAt(0)}. a accepté votre proposition de ${montantFinal}€ pour la mission "${mission.titre}"`,
          link: `/dashboard/missions/offres?tab=0&mission=${missionId}`,
          is_read: false,
          is_archived: false
        }]);

      if (notifError) {
        logger.error('Erreur lors de la création de la notification:', notifError);
      }

      // Vérifier et récompenser le parrainage si nécessaire
      try {
        if (userId) {
          logger.info('Vérification du parrainage pour le propriétaire de la mission (filleul):', userId);
          await rewardReferral(userId);
        } else {
          logger.error('Impossible de vérifier le parrainage : userId est undefined');
        }
      } catch (referralError) {
        logger.error('Erreur lors de la récompense du parrainage:', referralError);
        // On continue l'exécution même en cas d'erreur de récompense
      }

      // Créer une notification pour le client (propriétaire de la mission)
      const { data: jobbeurProfile, error: jobbeurProfileError } = await supabase
        .from('user_profil')
        .select('nom, prenom')
        .eq('user_id', proposal.jobbeur_id)
        .single();

      if (jobbeurProfileError) {
        logger.error('Erreur lors de la récupération du profil du jobbeur:', jobbeurProfileError);
      } else {
        // Déchiffrer les données du profil jobbeur
        const decryptedJobbeurProfile = jobbeurProfile ? await decryptProfilDataAsync(jobbeurProfile) : null;
        // Déterminer le montant final en fonction du statut
        let montantFinal = proposal.montant_propose;
        if (proposal.statut === 'contre_offre' && proposal.montant_contre_offre) {
          montantFinal = proposal.montant_contre_offre;
        } else if (proposal.statut === 'contre_offre_jobbeur' && proposal.montant_contre_offre_jobbeur) {
          montantFinal = proposal.montant_contre_offre_jobbeur;
        }

        const { error: clientNotifError } = await supabase
          .from('user_notifications')
          .insert([{
            user_id: userId,
            type: 'mission',
            title: 'Proposition acceptée',
            content: `Vous avez accepté la proposition de ${decryptedJobbeurProfile?.prenom} ${decryptedJobbeurProfile?.nom?.toUpperCase()?.charAt(0)}. pour un montant de ${montantFinal}€ pour votre mission "${mission.titre}"`,
            link: `/dashboard/missions/offres?tab=1&mission=${missionId}`,
            is_read: false,
            is_archived: false
          }]);

        if (clientNotifError) {
          logger.error('Erreur lors de la création de la notification pour le client:', clientNotifError);
        }
      }

      // Envoyer un email au jobbeur
      const { data: jobbeur, error: jobbeurError } = await supabase
        .from('users')
        .select('email')
        .eq('id', proposal.jobbeur_id)
        .single();

      if (!jobbeurError && jobbeur) {
        // Déchiffrer l'email du jobbeur
        const decryptedJobbeurEmail = await decryptUserDataAsync(jobbeur);

        // Déterminer le montant final en fonction du statut
        let montantFinal = proposal.montant_propose;
        if (proposal.statut === 'contre_offre' && proposal.montant_contre_offre) {
          montantFinal = proposal.montant_contre_offre;
        } else if (proposal.statut === 'contre_offre_jobbeur' && proposal.montant_contre_offre_jobbeur) {
          montantFinal = proposal.montant_contre_offre_jobbeur;
        }

        await sendProposalAcceptedEmail(decryptedJobbeurEmail.email, {
          missionTitle: mission.titre,
          ownerName: `${decryptedMissionOwner?.prenom} ${decryptedMissionOwner?.nom?.toUpperCase()?.charAt(0)}.`,
          amount: montantFinal,
          missionId: missionId
        });
        logger.info(`[EMAIL_SENT] Email de proposition acceptée envoyé`, { email: decryptedJobbeurEmail.email });
      }

      res.status(200).json({
        message: 'Proposition acceptée avec succès',
        proposal: {
          id: proposal.id,
          statut: 'acceptée'
        }
      });
    } catch (error) {
      logger.error('Erreur lors de l\'acceptation de la proposition:', error);
      res.status(500).json({ error: 'Erreur lors de l\'acceptation de la proposition' });
    }
  },

  // Refuser une proposition
  rejectProposal: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const missionId = req.params.id;
      const proposalId = req.params.proposalId;

      // Vérifier si la mission existe et appartient à l'utilisateur
      const { data: mission, error: missionError } = await supabase
        .from('user_missions')
        .select('id, user_id, titre, is_closed')
        .eq('id', missionId)
        .single();

      if (missionError) {
        logger.error('Erreur lors de la récupération de la mission:', missionError);
        return res.status(500).json({ error: 'Erreur lors de la récupération de la mission' });
      }

      if (!mission) {
        return res.status(404).json({ error: 'Mission non trouvée' });
      }

      // Vérifier si la proposition existe
      const { data: proposal, error: proposalError } = await supabase
        .from('user_mission_candidature')
        .select('id, jobbeur_id, montant_propose, statut')
        .eq('id', proposalId)
        .eq('mission_id', missionId)
        .single();

      if (proposalError) {
        logger.error('Erreur lors de la récupération de la proposition:', proposalError);
        return res.status(500).json({ error: 'Erreur lors de la récupération de la proposition' });
      }

      if (!proposal) {
        return res.status(404).json({ error: 'Proposition non trouvée' });
      }

      if (proposal.statut !== 'en_attente' && proposal.statut !== 'contre_offre' && proposal.statut !== 'contre_offre_jobbeur') {
        res.status(400).json({ error: 'Cette proposition ne peut plus être refusée' });
      }

      // Mettre à jour la proposition
      const { error: updateError } = await supabase
        .from('user_mission_candidature')
        .update({
          statut: 'refusée',
          date_refus: new Date().toISOString()
        })
        .eq('id', proposalId);

      if (updateError) {
        logger.error('Erreur lors de la mise à jour de la proposition:', updateError);
        return res.status(500).json({ error: 'Erreur lors de la mise à jour de la proposition' });
      }

      // Si la mission est fermée, invalider le cache pour tous les jobbeurs qui ont postulé
      if (mission.is_closed) {
        const { data: allProposals, error: allProposalsError } = await supabase
          .from('user_mission_candidature')
          .select('jobbeur_id')
          .eq('mission_id', missionId);

        if (allProposalsError) {
          logger.error('Erreur lors de la récupération des propositions:', allProposalsError);
        } else {
          // Invalider le cache pour tous les jobbeurs qui ont postulé
          for (const prop of allProposals) {
            await invalidateMissionCache(missionId, prop.jobbeur_id);
          }
        }
      }

      // Invalider le cache pour la mission et les propositions
      await invalidateMissionCache(missionId, userId);
      await invalidateMissionCache(missionId, proposal.jobbeur_id);
      logger.info('Cache invalidé après refus de proposition:', { missionId, userId, jobbeurId: proposal.jobbeur_id });

      // Journalisation de l'action utilisateur
      if (userId) {
        await logUserActivity(
          userId,
          'proposal_reject',
          missionId,
          'mission',
          {
            proposal_id: proposalId,
            jobbeur_id: proposal.jobbeur_id,
            montant: proposal.montant_propose
          },
          getIpFromRequest(req)
        );
      }

      // Récupérer les informations du propriétaire de la mission
      const { data: missionOwner, error: ownerError } = await supabase
        .from('user_profil')
        .select('nom, prenom')
        .eq('user_id', userId)
        .single();

      if (ownerError) {
        logger.error('Erreur lors de la récupération du profil:', ownerError);
      }

      // Déchiffrer les données du propriétaire de mission
      const decryptedMissionOwnerReject = missionOwner ? await decryptProfilDataAsync(missionOwner) : null;

      // Créer une notification pour le jobbeur
      const { error: notifError } = await supabase
        .from('user_notifications')
        .insert([{
          user_id: proposal.jobbeur_id,
          type: 'mission',
          title: 'Proposition refusée',
          content: `${decryptedMissionOwnerReject?.prenom} ${decryptedMissionOwnerReject?.nom?.toUpperCase()?.charAt(0)}. a refusé votre proposition pour la mission "${mission.titre}"`,
          link: `/dashboard/missions/offres?tab=0&mission=${missionId}`,
          is_read: false,
          is_archived: false
        }]);

      if (notifError) {
        logger.error('Erreur lors de la création de la notification:', notifError);
      }

      // Envoyer un email au jobbeur
      const { data: jobbeur, error: jobbeurError } = await supabase
        .from('users')
        .select('email')
        .eq('id', proposal.jobbeur_id)
        .single();

      if (!jobbeurError && jobbeur) {
        // Déchiffrer l'email du jobbeur
        const decryptedJobbeurEmail = await decryptUserDataAsync(jobbeur);

        await sendProposalRejectedEmail(decryptedJobbeurEmail.email, {
          missionTitle: mission.titre,
          ownerName: `${decryptedMissionOwnerReject?.prenom} ${decryptedMissionOwnerReject?.nom?.toUpperCase()?.charAt(0)}.`,
          missionId: missionId
        });
        logger.info(`[EMAIL_SENT] Email de proposition refusée envoyé`, { email: decryptedJobbeurEmail.email });
      }

      res.status(200).json({
        message: 'Proposition refusée avec succès',
        proposal: {
          id: proposal.id,
          statut: 'refusée'
        }
      });
    } catch (error) {
      logger.error('Erreur lors du refus de la proposition:', error);
      res.status(500).json({ error: 'Erreur lors du refus de la proposition' });
    }
  },

  // Faire une contre-offre pour une proposition (par le jobbeur)
  makeJobbeurCounterOffer: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const missionId = req.params.id;
      const { amount, message, proposalId } = req.body;

      // Vérifier si la mission existe
      const { data: mission, error: missionError } = await supabase
        .from('user_missions')
        .select('id, user_id, titre, is_closed')
        .eq('id', missionId)
        .single();

      if (missionError) {
        logger.error('Erreur lors de la récupération de la mission:', missionError);
        return res.status(500).json({ error: 'Erreur lors de la récupération de la mission' });
      }

      if (!mission) {
        return res.status(404).json({ error: 'Mission non trouvée' });
      }

      if (mission.is_closed) {
        res.status(400).json({ error: 'Cette mission est fermée et n\'accepte plus de propositions' });
      }

      // Récupérer la proposition
      const { data: proposal, error: proposalError } = await supabase
        .from('user_mission_candidature')
        .select('id, jobbeur_id, montant_propose, montant_contre_offre, statut')
        .eq('id', proposalId)
        .eq('mission_id', missionId)
        .single();

      if (proposalError) {
        logger.error('Erreur lors de la récupération de la proposition:', proposalError);
        return res.status(500).json({ error: 'Erreur lors de la récupération de la proposition' });
      }

      if (!proposal) {
        return res.status(404).json({ error: 'Proposition non trouvée' });
      }

      // Vérifier que l'utilisateur est le jobbeur qui a fait la proposition
      if (proposal.jobbeur_id !== userId) {
        res.status(403).json({ error: 'Vous n\'êtes pas autorisé à faire une contre-offre pour cette proposition' });
      }

      // Vérifier que la proposition est bien en statut "contre_offre"
      if (proposal.statut !== 'contre_offre') {
        res.status(400).json({ error: 'Cette proposition n\'est pas en statut de contre-offre' });
      }

      // Vérifier que le montant de la contre-offre est différent du montant proposé par le client
      // if (amount === proposal.montant_contre_offre) {
      //   res.status(400).json({
      //     error: 'Le montant de la contre-offre doit être différent du montant actuel proposé',
      //     message: 'Le montant de la contre-offre doit être différent du montant actuel proposé',
      //     success: false,
      //     toastType: "warning"
      //   });
      // }

      // Mettre à jour la proposition avec la contre-offre du jobbeur
      const { error: updateError } = await supabase
        .from('user_mission_candidature')
        .update({
          statut: 'contre_offre_jobbeur',
          montant_contre_offre_jobbeur: amount,
          message_contre_offre_jobbeur: message,
          date_contre_offre_jobbeur: new Date().toISOString()
        })
        .eq('id', proposalId);

      if (updateError) {
        logger.error('Erreur lors de la mise à jour de la proposition:', updateError);
        return res.status(500).json({ error: 'Erreur lors de la mise à jour de la proposition' });
      }

      // Invalider le cache pour la mission et les propositions
      await invalidateMissionCache(missionId, userId);
      await invalidateMissionCache(missionId, mission.user_id);
      logger.info('Cache invalidé après contre-offre du jobbeur:', { missionId, userId, missionOwnerId: mission.user_id });

      // Journalisation de l'action utilisateur
      if (userId) {
        await logUserActivity(
          userId,
          'jobbeur_counter_offer',
          missionId,
          'mission',
          {
            proposal_id: proposalId,
            montant_original: proposal.montant_contre_offre,
            montant_contre_offre: amount,
            mission_owner_id: mission.user_id
          },
          getIpFromRequest(req)
        );
      }

      // Récupérer les informations du jobbeur
      const { data: jobbeur, error: jobbeurError } = await supabase
        .from('user_profil')
        .select('nom, prenom')
        .eq('user_id', userId)
        .single();

      if (jobbeurError) {
        logger.error('Erreur lors de la récupération du profil:', jobbeurError);
      }

      // Déchiffrer les données du profil jobbeur
      const decryptedJobbeurProfile = jobbeur ? await decryptProfilDataAsync(jobbeur) : null;

      // Créer une notification pour le propriétaire de la mission
      const { error: notifError } = await supabase
        .from('user_notifications')
        .insert([{
          user_id: mission.user_id,
          type: 'mission',
          title: 'Nouvelle contre-offre reçue',
          content: `${decryptedJobbeurProfile?.prenom} ${decryptedJobbeurProfile?.nom?.charAt(0).toUpperCase()}. a fait une contre-offre de ${amount}€ pour la mission "${mission.titre}"`,
          link: `/dashboard/missions/offres?tab=1&mission=${missionId}`,
          is_read: false,
          is_archived: false
        }]);

      if (notifError) {
        logger.error('Erreur lors de la création de la notification:', notifError);
      }

      // Envoyer un email au propriétaire de la mission
      const { data: missionOwner, error: ownerError } = await supabase
        .from('users')
        .select('email')
        .eq('id', mission.user_id)
        .single();

      if (!ownerError && missionOwner) {
        // Déchiffrer l'email du propriétaire de la mission
        const decryptedMissionOwnerEmail = await decryptUserDataAsync(missionOwner);

        await sendJobbeurCounterOfferEmail(decryptedMissionOwnerEmail.email, {
          missionTitle: mission.titre,
          jobbeurName: `${decryptedJobbeurProfile?.prenom} ${decryptedJobbeurProfile?.nom?.charAt(0).toUpperCase()}.`,
          originalAmount: proposal.montant_contre_offre,
          counterOfferAmount: amount,
          message: message,
          missionId: missionId,
          proposalId: proposalId
      });
        logger.info(`[EMAIL_SENT] Email de contre-offre jobbeur envoyé`, { email: decryptedMissionOwnerEmail.email });
      }

      res.status(200).json({
        message: 'Contre-offre envoyée avec succès',
        counterOffer: {
          amount: amount,
          message: message,
          date: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('Erreur lors de l\'envoi de la contre-offre:', error);
      res.status(500).json({ error: 'Erreur lors de l\'envoi de la contre-offre' });
    }
  },

  // Récupérer les statistiques des propositions envoyées
  getSentProposalStats: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ message: 'Utilisateur non authentifié', success: false });
      }

      // Récupérer les paramètres de filtrage
      const {
        status,
        search,
        budget_types,
        payment_methods,
        is_urgent,
        profile_types,
        offer_status,
        sort_by,
        missionId,
        category_filters
      } = req.query;

      // Générer une clé de cache unique pour cette requête
      const cacheKey = generateCacheKey('sent_proposal_stats', {
        userId,
        status,
        search,
        budget_types,
        payment_methods,
        is_urgent,
        profile_types,
        offer_status,
        sort_by,
        missionId,
        category_filters
      });

      // Vérifier si les données sont en cache
      const cachedData = await redis.get(cacheKey);
      if (cachedData) {
        logger.info('Récupération des statistiques des propositions envoyées depuis le cache');
        return res.status(200).json(JSON.parse(cachedData));
      }

      // Construire la requête de base
      let query = supabase
        .from('user_mission_candidature')
        .select(`
          id,
          mission_id,
          jobbeur_id,
          statut,
          montant_propose,
          message,
          created_at,
          updated_at,
          montant_contre_offre,
          message_contre_offre,
          date_contre_offre,
          montant_contre_offre_jobbeur,
          message_contre_offre_jobbeur,
          date_contre_offre_jobbeur
        `)
        .eq('jobbeur_id', userId);

      // Filtrer par mission spécifique si fourni
      if (missionId) {
        query = query.eq('mission_id', missionId);
      }

      // Filtrer par statut d'offre si fourni
      if (offer_status) {
        const statuses = convertStatusToArray(offer_status);
        query = query.in('statut', statuses);
      }

      // Exécuter la requête
      const { data: proposals, error } = await query;

      if (error) {
        logger.error('Erreur lors de la récupération des statistiques des propositions envoyées:', error);
        return res.status(500).json({ message: 'Erreur lors de la récupération des statistiques', success: false });
      }

      // Calculer les statistiques
      const stats = {
        total: proposals.length,
        enAttente: proposals.filter(p => p.statut === 'en_attente').length,
        acceptees: proposals.filter(p => p.statut === 'acceptée').length,
        refusees: proposals.filter(p => p.statut === 'refusée').length,
        contreOffres: proposals.filter(p => p.statut === 'contre_offre' || p.statut === 'contre_offre_jobbeur').length,
        montantMoyen: proposals.length > 0
          ? Math.round(proposals.reduce((acc, p) => acc + (p.montant_propose || 0), 0) / proposals.length)
          : 0,
        montantTotal: proposals.length > 0
          ? proposals.reduce((acc, p) => acc + (p.montant_propose || 0), 0)
          : 0
      };

      // Mettre en cache les résultats
      await redis.set(cacheKey, JSON.stringify(stats), 'EX', CACHE_DURATION);
      logger.info('Statistiques des propositions envoyées mises en cache');

      res.status(200).json(stats);
    } catch (error) {
      logger.error('Erreur lors de la récupération des statistiques des propositions envoyées:', error);
      return res.status(500).json({ message: 'Erreur serveur', success: false });
    }
  },

  // Récupérer les statistiques des propositions reçues
  getReceivedProposalStats: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({ message: 'Utilisateur non authentifié', success: false });
      }

      // Récupérer les paramètres de filtrage
      const {
        status,
        search,
        budget_types,
        payment_methods,
        is_urgent,
        profile_types,
        offer_status,
        sort_by,
        missionId,
        category_filters
      } = req.query;

      // Générer une clé de cache unique pour cette requête
      const cacheKey = generateCacheKey('received_proposal_stats', {
        userId,
        status,
        search,
        budget_types,
        payment_methods,
        is_urgent,
        profile_types,
        offer_status,
        sort_by,
        missionId,
        category_filters
      });

      // Vérifier si les données sont en cache
      const cachedData = await redis.get(cacheKey);
      if (cachedData) {
        logger.info('Récupération des statistiques des propositions reçues depuis le cache');
        return res.status(200).json(JSON.parse(cachedData));
      }

      // D'abord, récupérer les missions de l'utilisateur
      let missionsQuery = supabase
        .from('user_missions')
        .select('id')
        .eq('user_id', userId);

      // Filtrer par mission spécifique si fourni
      if (missionId) {
        missionsQuery = missionsQuery.eq('id', missionId);
      }

      const { data: userMissions, error: missionsError } = await missionsQuery;

      if (missionsError) {
        logger.error('Erreur lors de la récupération des missions de l\'utilisateur:', missionsError);
        return res.status(500).json({ message: 'Erreur lors de la récupération des missions', success: false });
      }

      if (userMissions.length === 0) {
        const emptyStats = {
          total: 0,
          enAttente: 0,
          acceptees: 0,
          refusees: 0,
          contreOffres: 0,
          montantMoyen: 0,
          montantTotal: 0
        };

        // Mettre en cache les résultats vides
        await redis.set(cacheKey, JSON.stringify(emptyStats), 'EX', CACHE_DURATION);
        logger.info('Statistiques vides des propositions reçues mises en cache');

        return res.status(200).json(emptyStats);
      }

      // Récupérer les propositions pour ces missions
      const missionIds = userMissions.map(mission => mission.id);

      let proposalsQuery = supabase
        .from('user_mission_candidature')
        .select(`
          id,
          mission_id,
          jobbeur_id,
          statut,
          montant_propose,
          message,
          created_at,
          updated_at,
          montant_contre_offre,
          message_contre_offre,
          date_contre_offre,
          montant_contre_offre_jobbeur,
          message_contre_offre_jobbeur,
          date_contre_offre_jobbeur
        `)
        .in('mission_id', missionIds);

      // Filtrer par statut d'offre si fourni
      if (offer_status) {
        const statuses = convertStatusToArray(offer_status);
        proposalsQuery = proposalsQuery.in('statut', statuses);
      }

      const { data: proposals, error: proposalsError } = await proposalsQuery;

      if (proposalsError) {
        logger.error('Erreur lors de la récupération des propositions reçues:', proposalsError);
        return res.status(500).json({ message: 'Erreur lors de la récupération des propositions', success: false });
      }

      // Calculer les statistiques
      const stats = {
        total: proposals.length,
        enAttente: proposals.filter(p => p.statut === 'en_attente').length,
        acceptees: proposals.filter(p => p.statut === 'acceptée').length,
        refusees: proposals.filter(p => p.statut === 'refusée').length,
        contreOffres: proposals.filter(p => p.statut === 'contre_offre' || p.statut === 'contre_offre_jobbeur').length,
        montantMoyen: proposals.length > 0
          ? Math.round(proposals.reduce((acc, p) => acc + (p.montant_propose || 0), 0) / proposals.length)
          : 0,
        montantTotal: proposals.length > 0
          ? proposals.reduce((acc, p) => acc + (p.montant_propose || 0), 0)
          : 0
      };

      // Mettre en cache les résultats
      await redis.set(cacheKey, JSON.stringify(stats), 'EX', CACHE_DURATION);
      logger.info('Statistiques des propositions reçues mises en cache');

      return res.status(200).json(stats);
    } catch (error) {
      logger.error('Erreur lors de la récupération des statistiques des propositions reçues:', error);
      return res.status(500).json({ message: 'Erreur serveur', success: false });
    }
  },

  // Fonction pour vérifier si l'utilisateur a déjà fait une offre pour une mission spécifique
  getUserProposalForMission: async (req: Request, res: Response) => {
    try {
      const { id: missionId } = req.params;
      const userId = req.user?.userId;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "Utilisateur non authentifié"
        });
      }

      // Générer une clé de cache unique pour cette requête
      const cacheKey = generateCacheKey('user_proposal_for_mission', { userId, missionId });

      // Vérifier si les données sont en cache
      const cachedData = await redis.get(cacheKey);
      if (cachedData) {
        logger.info('Récupération de la proposition utilisateur depuis le cache');
        return res.status(200).json(JSON.parse(cachedData));
      }

      // Vérifier si la mission existe
      const { data: missionExists, error: missionError } = await supabase
        .from('user_missions')
        .select('id')
        .eq('id', missionId)
        .single();

      if (missionError) {
        return res.status(404).json({
          success: false,
          message: "Mission non trouvée"
        });
      }

      // Récupérer la proposition de l'utilisateur pour cette mission
      const { data: proposal, error } = await supabase
        .from('user_mission_candidature')
        .select(`
          id,
          mission_id,
          jobbeur_id,
          statut,
          montant_propose,
          message,
          created_at,
          updated_at,
          montant_contre_offre,
          message_contre_offre,
          date_contre_offre,
          montant_contre_offre_jobbeur,
          message_contre_offre_jobbeur,
          date_contre_offre_jobbeur
        `)
        .eq('mission_id', missionId)
        .eq('jobbeur_id', userId)
        .single();

      if (error) {
        // Si aucune proposition n'est trouvée, renvoyer un statut 404
        if (error.code === 'PGRST116') {
          const response = {
            success: false,
            message: "Aucune proposition trouvée pour cette mission"
          };

          // Mettre en cache la réponse négative
          await redis.set(cacheKey, JSON.stringify(response), 'EX', CACHE_DURATION);
          logger.info('Absence de proposition mise en cache');

          return res.status(200).json(response);
        }

        // Pour toute autre erreur, renvoyer un statut 500
        return res.status(500).json({
          success: false,
          message: "Erreur lors de la récupération de la proposition",
          error: error.message
        });
      }

      // Préparer la réponse
      const response = {
        success: true,
        proposal
      };

      // Mettre en cache les résultats
      await redis.set(cacheKey, JSON.stringify(response), 'EX', CACHE_DURATION);
      logger.info('Proposition utilisateur mise en cache');

      // Renvoyer la proposition trouvée
      return res.status(200).json(response);
    } catch (error: any) {
      logger.error("Erreur lors de la vérification de la proposition:", error);
      return res.status(500).json({
        success: false,
        message: "Erreur serveur lors de la vérification de la proposition",
        error: error.message
      });
    }
  },

  // Envoyer les informations de contact à un offreur
  sendContactInfo: async (req: Request, res: Response) => {
    try {
      const { id: missionId, proposalId } = req.params;
      const { contactInfo, jobbeurId } = req.body;
      const userId = req.user?.userId;

      // Log des données reçues
      logger.info('Données contactInfo reçues:', {
        contactInfo: contactInfo,
        jobbeurId: jobbeurId,
        userId: userId
      });

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
      }

      // Vérifier que l'utilisateur est bien le propriétaire de la mission
      const { data: mission, error: missionError } = await supabase
        .from('user_missions')
        .select('user_id')
        .eq('id', missionId)
        .single();

      if (missionError || !mission) {
        logger.error('Erreur lors de la récupération de la mission:', missionError);
        return res.status(404).json({
          success: false,
          message: 'Mission non trouvée'
        });
      }

      if (mission.user_id !== userId) {
        return res.status(403).json({
          success: false,
          message: 'Seul le propriétaire de la mission peut partager ses informations de contact'
        });
      }

      // Vérifier que la proposition existe et appartient à la mission
      const { data: proposal, error: proposalError } = await supabase
        .from('user_mission_candidature')
        .select('jobbeur_id, statut')
        .eq('id', proposalId)
        .eq('mission_id', missionId)
        .single();

      if (proposalError || !proposal) {
        logger.error('Erreur lors de la récupération de la proposition:', proposalError);
        return res.status(404).json({
          success: false,
          message: 'Proposition non trouvée'
        });
      }

      // Vérifier que le jobbeur est bien celui de la proposition
      if (proposal.jobbeur_id !== jobbeurId) {
        return res.status(403).json({
          success: false,
          message: 'Le jobbeur spécifié ne correspond pas à celui de la proposition'
        });
      }

      // Vérifier s'il y a eu un envoi récent (dans les 60 dernières minutes)
      const sixtyMinutesAgo = new Date();
      sixtyMinutesAgo.setMinutes(sixtyMinutesAgo.getMinutes() - 60);

      const { data: recentNotifications, error: recentNotificationsError } = await supabase
        .from('user_notifications')
        .select('created_at')
        .eq('user_id', jobbeurId)
        .eq('type', 'mission')
        .eq('title', 'Informations de contact reçues')
        .gte('created_at', sixtyMinutesAgo.toISOString())
        .order('created_at', { ascending: false });

      if (!recentNotificationsError && recentNotifications && recentNotifications.length > 0) {
        return res.status(429).json({
          success: false,
          message: 'Vous avez déjà envoyé les informations de contact récemment. Veuillez attendre 60 minutes avant de réessayer.'
        });
      }

      // Récupérer les informations du client pour personnaliser le message
      const { data: clientData, error: clientDataError } = await supabase
        .from('user_profil')
        .select('nom, prenom')
        .eq('user_id', userId)
        .single();

      if (clientDataError) {
        logger.error('Erreur lors de la récupération des informations du client:', clientDataError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération des informations du client'
        });
      }

      // Déchiffrer les données du client
      const decryptedClientData = clientData ? await decryptProfilDataAsync(clientData) : null;

      // Formater le nom du client (prénom + première lettre du nom suivie d'un point)
      const clientName = `${decryptedClientData?.prenom} ${decryptedClientData?.nom?.charAt(0).toUpperCase()}.`;

      // Récupérer les informations de la mission
      const { data: missionInfo, error: missionInfoError } = await supabase
        .from('user_missions')
        .select('titre')
        .eq('id', missionId)
        .single();

      if (missionInfoError || !missionInfo) {
        logger.error('Erreur lors de la récupération des informations de la mission:', missionInfoError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération des informations de la mission'
        });
      }

      // Créer une notification pour le jobbeur avec les informations de contact
      const { error: notificationError } = await supabase
        .from('user_notifications')
        .insert([{
          user_id: jobbeurId,
          type: 'mission',
          title: 'Informations de contact reçues',
          content: `${clientName} a partagé ses informations de contact avec vous pour la mission "${missionInfo.titre}".<br><br>${contactInfo.nom_prenom ? `Nom et prénom: ${contactInfo.nom_prenom.nom} ${contactInfo.nom_prenom.prenom}` : 'Nom et prénom: Non partagés'}<br>Email: ${contactInfo.email || 'Non partagé'}<br>Téléphone: ${contactInfo.telephone || 'Non partagé'}<br>Adresse: ${contactInfo.adresse ? `${contactInfo.adresse.numero} ${contactInfo.adresse.rue}, ${contactInfo.adresse.code_postal} ${contactInfo.adresse.ville}, ${contactInfo.adresse.pays}` : 'Non partagée'}<br><br>🔒 Conseil de sécurité : Pour votre première rencontre, privilégiez un lieu public.<br>💰 Echanger en Jobi : Utilisez vos Jobi pour bénéficier de notre protection.<br><br>Un email contenant ces informations et des conseils supplémentaires vous a également été envoyé.`,
          link: `/dashboard/missions/${missionId}`,
          is_read: false,
          is_archived: false
        }]);

      if (notificationError) {
        logger.error('Erreur lors de la création de la notification:', notificationError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de l\'envoi des informations de contact'
        });
      }

      // Si l'utilisateur a activé les notifications par email, envoyer un email
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('email')
        .eq('id', jobbeurId)
        .single();

      if (userError) {
        logger.error('Erreur lors de la récupération des préférences de notification du jobbeur:', userError);
      } else if (userData?.email) {
        try {
          // Déchiffrer l'email du jobbeur
          const decryptedUserData = await decryptUserDataAsync(userData);

          // Envoyer l'email avec les informations de contact
          logger.info('Envoi de l\'email avec les informations de contact au jobbeur:', {
            missionTitle: missionInfo.titre,
            clientName: clientName,
            nom_prenom: contactInfo.nom_prenom ? `${contactInfo.nom_prenom.nom} ${contactInfo.nom_prenom.prenom}` : undefined,
            email: contactInfo.email || undefined,
            telephone: contactInfo.telephone || undefined,
            adresse: contactInfo.adresse ? `${contactInfo.adresse.numero} ${contactInfo.adresse.rue}, ${contactInfo.adresse.code_postal} ${contactInfo.adresse.ville}, ${contactInfo.adresse.pays}` : undefined,
            message: `${clientName} a partagé ses informations de contact avec vous pour la mission "${missionInfo.titre}".`,
            missionId: missionId
          });

          await sendContactInfoEmail(decryptedUserData.email, {
            missionTitle: missionInfo.titre,
            clientName: clientName,
            nom_prenom: contactInfo.nom_prenom ? `${contactInfo.nom_prenom.nom} ${contactInfo.nom_prenom.prenom}` : undefined,
            email: contactInfo.email || undefined,
            telephone: contactInfo.telephone || undefined,
            adresse: contactInfo.adresse ? `${contactInfo.adresse.numero} ${contactInfo.adresse.rue}, ${contactInfo.adresse.code_postal} ${contactInfo.adresse.ville}, ${contactInfo.adresse.pays}` : undefined,
            message: `${clientName} a partagé ses informations de contact avec vous pour la mission "${missionInfo.titre}".`,
            missionId: missionId
          });
          logger.info(`[EMAIL_SENT] Email d'informations de contact envoyé`, { email: decryptedUserData.email });
        } catch (error) {
          logger.error('Erreur lors de l\'envoi de l\'email avec les informations de contact:', error);
        }
      } else {
        logger.info('Le jobbeur n\'a pas d\'email pour partager les informations de contact');
      }

      // Invalider le cache pour le propriétaire de la mission et le jobbeur
      await invalidateMissionCache(missionId, userId);
      await invalidateMissionCache(missionId, jobbeurId);
      logger.info('Cache invalidé après envoi des informations de contact:', { missionId, userId, jobbeurId });

      return res.status(200).json({
        success: true,
        message: 'Informations de contact envoyées avec succès'
      });
    } catch (error) {
      logger.error('Erreur lors de l\'envoi des informations de contact:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de l\'envoi des informations de contact'
      });
    }
  },

  // Modérer une mission (changer son statut)
  moderateMission: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const missionId = req.params.id;
      const { statut, reason } = req.body;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
      }

      // Vérifier que l'utilisateur est un administrateur
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();

      if (userError || !userData || userData.role !== 'jobpadm') {
        return res.status(403).json({
          success: false,
          message: 'Accès refusé. Seuls les administrateurs peuvent modérer les missions.'
        });
      }

      // Vérifier que la mission existe
      const { data: mission, error: missionError } = await supabase
        .from('user_missions')
        .select('id, user_id, titre, statut')
        .eq('id', missionId)
        .single();

      if (missionError || !mission) {
        return res.status(404).json({
          success: false,
          message: 'Mission non trouvée'
        });
      }

      // Si on change le statut vers 'annulee' ou 'en_moderation', refuser toutes les candidatures en attente
      if ((statut === 'annulee' || statut === 'en_moderation') && mission.statut !== statut) {
        const { data: candidatures, error: candidaturesError } = await supabase
          .from('user_mission_candidature')
          .select('id, jobbeur_id')
          .eq('mission_id', missionId)
          .eq('statut', 'en_attente');

        if (!candidaturesError && candidatures && candidatures.length > 0) {
          // Refuser toutes les candidatures en attente
          const { error: updateError } = await supabase
            .from('user_mission_candidature')
            .update({ 
              statut: 'refusée',
              date_refus: new Date().toISOString()
            })
            .eq('mission_id', missionId)
            .eq('statut', 'en_attente');

          if (updateError) {
            logger.error('Erreur lors du refus des candidatures:', updateError);
          } else {
            // Notifier chaque jobbeur que sa candidature a été refusée
            for (const candidature of candidatures) {
              await supabase.from('user_notifications').insert({
                user_id: candidature.jobbeur_id,
                type: 'mission',
                title: 'Candidature refusée automatiquement',
                content: `Votre candidature pour la mission "${mission.titre}" a été automatiquement refusée suite à une modération administrative.`,
                link: `/dashboard/missions/${missionId}`,
                is_read: false,
                is_archived: false
              });
            }
          }
        }
      }

      // Mettre à jour le statut de la mission
      const { error: updateError } = await supabase
        .from('user_missions')
        .update({ 
          statut: statut,
          updated_at: new Date().toISOString()
        })
        .eq('id', missionId);

      if (updateError) {
        logger.error('Erreur lors de la mise à jour du statut de la mission:', updateError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la modération de la mission'
        });
      }

      // Notifier le propriétaire de la mission
      await supabase.from('user_notifications').insert({
        user_id: mission.user_id,
        type: 'system',
        title: 'Statut de mission modifié',
        content: `Le statut de votre mission "${mission.titre}" a été modifié par la modération.${reason ? ` Motif: ${reason}` : ''}`,
        link: `/dashboard/missions/${missionId}`,
        is_read: false,
        is_archived: false
      });

      // Invalider le cache
      await invalidateMissionCache(missionId, mission.user_id);

      logger.info(`Mission ${missionId} modérée par l'admin ${userId}. Nouveau statut: ${statut}`);

      return res.status(200).json({
        success: true,
        message: 'Mission modérée avec succès'
      });
    } catch (error) {
      logger.error('Erreur lors de la modération de la mission:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la modération'
      });
    }
  },

  // Supprimer une mission (administrateur)
  adminDeleteMission: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const missionId = req.params.id;
      const { reason } = req.body;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
      }

      // Vérifier que l'utilisateur est un administrateur
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();

      if (userError || !userData || userData.role !== 'jobpadm') {
        return res.status(403).json({
          success: false,
          message: 'Accès refusé. Seuls les administrateurs peuvent supprimer les missions.'
        });
      }

      // Vérifier que la mission existe
      const { data: mission, error: missionError } = await supabase
        .from('user_missions')
        .select('id, user_id, titre')
        .eq('id', missionId)
        .single();

      if (missionError || !mission) {
        return res.status(404).json({
          success: false,
          message: 'Mission non trouvée'
        });
      }

      // Récupérer toutes les candidatures pour les refuser avant suppression
      const { data: candidatures, error: candidaturesError } = await supabase
        .from('user_mission_candidature')
        .select('id, jobbeur_id, statut')
        .eq('mission_id', missionId);

      if (!candidaturesError && candidatures && candidatures.length > 0) {
        // Refuser toutes les candidatures en attente
        const candidaturesEnAttente = candidatures.filter(c => c.statut === 'en_attente');
        
        if (candidaturesEnAttente.length > 0) {
          const { error: updateError } = await supabase
            .from('user_mission_candidature')
            .update({ 
              statut: 'refusée',
              date_refus: new Date().toISOString()
            })
            .eq('mission_id', missionId)
            .eq('statut', 'en_attente');

          if (updateError) {
            logger.error('Erreur lors du refus des candidatures avant suppression:', updateError);
          } else {
            // Notifier chaque jobbeur que sa candidature a été refusée
            for (const candidature of candidaturesEnAttente) {
              await supabase.from('user_notifications').insert({
                user_id: candidature.jobbeur_id,
                type: 'mission',
                title: 'Mission supprimée',
                content: `La mission "${mission.titre}" pour laquelle vous aviez postulé a été supprimée par la modération.`,
                link: null,
                is_read: false,
                is_archived: false
              });
            }
          }
        }
      }

      // Supprimer la mission (les candidatures seront supprimées automatiquement par CASCADE)
      const { error: deleteError } = await supabase
        .from('user_missions')
        .delete()
        .eq('id', missionId);

      if (deleteError) {
        logger.error('Erreur lors de la suppression de la mission:', deleteError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la suppression de la mission'
        });
      }

      // Notifier le propriétaire de la mission
      await supabase.from('user_notifications').insert({
        user_id: mission.user_id,
        type: 'system',
        title: 'Mission supprimée',
        content: `Votre mission "${mission.titre}" a été supprimée par la modération.${reason ? ` Motif: ${reason}` : ''}`,
        link: null,
        is_read: false,
        is_archived: false
      });

      // Invalider le cache
      await invalidateMissionCache(missionId, mission.user_id);

      logger.info(`Mission ${missionId} supprimée par l'admin ${userId}`);

      return res.status(200).json({
        success: true,
        message: 'Mission supprimée avec succès'
      });
    } catch (error) {
      logger.error('Erreur lors de la suppression de la mission:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la suppression'
      });
    }
  }
};