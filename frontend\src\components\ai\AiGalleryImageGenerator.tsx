import React, { useState } from 'react';
import { Sparkles } from 'lucide-react';
import { Box, Typography } from '@mui/material';
import AiImageGenerationModal from './AiImageGenerationModal';
import { useAiCredits } from '../../hooks/useAiCredits';
import { IMAGE_GENERATION_COST } from '../../hooks/useAiImageGeneration';

interface AiGalleryImageGeneratorProps {
  onImageGenerated: (imageUrl: string, imageBase64: string, photoObj?: { photo_url: string; id?: string; caption?: string; created_at?: string; updated_at?: string; order_index?: number }) => void;
  galleryId?: string;
  galleryName?: string;
  galleryDescription?: string;
  isPurposeFeatured?: boolean;
  isCover?: boolean;
  className?: string;
}

/**
 * Composant pour générer une image de galerie avec l'IA
 */
const AiGalleryImageGenerator: React.FC<AiGalleryImageGeneratorProps> = ({
  onImageGenerated,
  galleryId = '',
  galleryName = '',
  galleryDescription = '',
  isPurposeFeatured = false,
  isCover,
  className = ''
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { credits } = useAiCredits();

  // Générer un prompt par défaut basé sur le nom et la description de la galerie
  const generateDefaultPrompt = (): string => {
    let prompt = '';
    
    if (galleryName) {
      prompt += galleryName;
    }
    
    if (galleryDescription) {
      // Extraire le texte brut de la description HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = galleryDescription;
      const textDescription = tempDiv.textContent || tempDiv.innerText || '';
      
      // Limiter la longueur de la description pour le prompt
      const maxDescriptionLength = 200;
      const truncatedDescription = textDescription.length > maxDescriptionLength
        ? textDescription.substring(0, maxDescriptionLength) + '...'
        : textDescription;
      
      if (prompt) {
        prompt += ': ';
      }
      
      prompt += truncatedDescription;
    }
    
    // Si aucun nom ni description n'est fourni, utiliser un prompt générique
    if (!prompt) {
      prompt = isPurposeFeatured 
        ? "Photo professionnelle lumineuse et valorisante illustrant un service à domicile (ex : jardinage, bricolage, garde d'animaux), style moderne, ambiance accueillante"
        : "Photo de réalisation professionnelle pour une galerie sur le thème de la plomberie : résultat soigné, client satisfait, environnement agréable, style naturel et authentique";
    }
    
    return prompt;
  };

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleImageGenerated = (imageUrl: string, imageBase64: string, photoObj?: { photo_url: string; id?: string; caption?: string; created_at?: string; updated_at?: string; order_index?: number }) => {
    onImageGenerated(imageUrl, imageBase64, photoObj);
    setIsModalOpen(false);
  };

  return (
    <>
      <Box 
        className={`flex flex-col items-center justify-center p-4 border-2 border-dashed border-[#FFE4BA] rounded-xl hover:border-[#FF6B2C] transition-all cursor-pointer ${className}`}
        onClick={handleOpenModal}
      >
        <Sparkles className="h-6 w-6 text-[#FF6B2C] mb-2" />
        <Typography variant="body2" className="text-center font-medium mb-1">
          Générer avec l'IA
        </Typography>
        <Typography variant="caption" className="text-center text-gray-500">
          {credits >= IMAGE_GENERATION_COST 
            ? `Coût: ${IMAGE_GENERATION_COST} crédits IA` 
            : `Crédits insuffisants (${credits}/${IMAGE_GENERATION_COST})`}
        </Typography>
      </Box>

      <AiImageGenerationModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onImageGenerated={handleImageGenerated}
        purpose={isPurposeFeatured ? 'featured_photo' : 'gallery_photo'}
        defaultPrompt={generateDefaultPrompt()}
        galleryId={galleryId}
        galleryName={galleryName}
        galleryDescription={galleryDescription}
        isCover={isCover ?? !isPurposeFeatured}
      />
    </>
  );
};

export default AiGalleryImageGenerator;
