import { fetchCsrfToken } from '../services/csrf';
import { logger } from './logger';
import { getCookie } from './cookieUtils';

interface HeadersCache {
  headers: Record<string, string>;
  timestamp: number;
  promise: Promise<Record<string, string>> | null;
}

// Réduire la durée du cache pour éviter les problèmes d'authentification
const CACHE_DURATION = 60000; // 1 minute au lieu de 5 minutes
let headersCache: HeadersCache = {
  headers: {},
  timestamp: 0,
  promise: null
};

export const getCommonHeaders = async (useCache: boolean = true): Promise<Record<string, string>> => {
  const now = Date.now();

  // Si on ne veut pas utiliser le cache, le vider
  if (!useCache) {
    clearHeadersCache();
  }

  // Si le cache est valide et qu'on veut l'utiliser
  if (useCache && Object.keys(headersCache.headers).length > 0 &&
      (now - headersCache.timestamp) < CACHE_DURATION) {
    return { ...headersCache.headers };
  }

  // Si une requête est déjà en cours, retourner sa promesse
  if (headersCache.promise) {
    try {
      return await headersCache.promise;
    } catch (error) {
      headersCache.promise = null;
      throw error;
    }
  }

  // Créer une nouvelle promesse pour la requête
  headersCache.promise = (async () => {
    try {
      const csrfToken = await fetchCsrfToken();
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-CSRF-Token': csrfToken
      };

      // Mettre à jour le cache
      headersCache = {
        headers,
        timestamp: now,
        promise: null
      };

      return { ...headers };
    } catch (error) {
      // En cas d'erreur, on reset le cache
      headersCache = {
        headers: {},
        timestamp: 0,
        promise: null
      };
      throw error;
    }
  })();

  return headersCache.promise;
};

export const getMultipartHeaders = async (): Promise<Record<string, string>> => {
  const now = Date.now();

  // Retourner les headers du cache s'ils sont valides
  if (Object.keys(headersCache.headers).length > 0 && 
      (now - headersCache.timestamp) < CACHE_DURATION) {
    const headers: Record<string, string> = {
      'X-CSRF-Token': headersCache.headers['X-CSRF-Token']
    };
    return headers;
  }

  // Si une requête est déjà en cours, retourner sa promesse
  if (headersCache.promise) {
    try {
      const baseHeaders = await headersCache.promise;
      const headers: Record<string, string> = {
        'X-CSRF-Token': baseHeaders['X-CSRF-Token']
      };
      return headers;
    } catch (error) {
      headersCache.promise = null;
      throw error;
    }
  }

  // Créer une nouvelle promesse pour la requête
  headersCache.promise = (async () => {
    try {
      const csrfToken = await fetchCsrfToken();
      const headers: Record<string, string> = {
        'X-CSRF-Token': csrfToken
      };

      // Mettre à jour le cache
      headersCache = {
        headers,
        timestamp: now,
        promise: null
      };

      return headers;
    } catch (error) {
      // En cas d'erreur, on reset le cache
      headersCache = {
        headers: {},
        timestamp: 0,
        promise: null
      };
      throw error;
    }
  })();

  return headersCache.promise;
};

export const clearHeadersCache = () => {
  headersCache = {
    headers: {},
    timestamp: 0,
    promise: null
  };
  logger.info('🗑️ Cache des headers effacé manuellement');
};