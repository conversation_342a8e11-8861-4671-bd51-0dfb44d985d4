import cron from 'node-cron';
import logger from '../utils/logger';
import processNewsletterReminders from '../scripts/newsletterReminders';
import { OfferExpirationChecker } from '../scripts/offerExpirationChecker';
import { redis } from './redis';
import { DatabaseService } from '../services/db';
import { supabase } from './supabase';
import { cleanModerationCache } from '../controllers/contentModerationController';
import { cleanupTemporaryModerationImages } from '../services/storage';
import { cleanupExpiredConsents } from '../controllers/aiConsent';
import { processQueue } from '../services/processEmailQueue';
// Importer sessionStore depuis server.ts pour le nettoyage des listeners
// Cette importation sera résolue après le démarrage du serveur
let sessionStore: any;

/**
 * Configuration des tâches planifiées (cron jobs)
 */
export const setupCronJobs = () => {
  // Récupérer la référence au sessionStore depuis server.ts
  try {
    const server = require('../server');
    if (server && server.sessionStore) {
      sessionStore = server.sessionStore;
      logger.info('Référence au sessionStore récupérée avec succès pour les tâches cron');
    }
  } catch (error) {
    logger.warn('Impossible de récupérer la référence au sessionStore:', error);
  }
  logger.info('Configuration des tâches planifiées...');

  // Relances newsletter - tous les jours à 10h00
  cron.schedule('0 10 * * *', async () => {
    logger.info('Exécution de la tâche planifiée: relances newsletter');
    try {
      await processNewsletterReminders();
      logger.info('Tâche de relances newsletter terminée avec succès');
    } catch (error) {
      logger.error('Erreur lors de l\'exécution de la tâche de relances newsletter:', error);
    }
  }, {
    scheduled: true,
    timezone: 'Europe/Paris'
  });

  // Vérification des offres expirées - toutes les heures
  cron.schedule('0 * * * *', async () => {
    logger.info('Exécution de la tâche planifiée: vérification des offres expirées');
    try {
      await OfferExpirationChecker.checkExpiredOffers();
      logger.info('Tâche de vérification des offres expirées terminée avec succès');
    } catch (error) {
      logger.error('Erreur lors de la vérification des offres expirées:', error);
    }
  }, {
    scheduled: true,
    timezone: 'Europe/Paris'
  });

  // Traitement de la file d'attente d'emails - toutes les 30 secondes
  cron.schedule('*/30 * * * * *', async () => {
    try {
      await processQueue().catch((error: Error) => {
        logger.info('Erreur lors du traitement de la file d\'attente d\'emails :', error);
      });
    } catch (error: unknown) {
      logger.info('Erreur lors du traitement de la file d\'attente d\'emails :', error);
    }
  }, {
    scheduled: true,
    timezone: 'Europe/Paris'
  });

  // Surveillance des écouteurs Redis (toutes les 30 minutes)
  cron.schedule('*/30 * * * *', () => {
    logger.info('Exécution de la tâche planifiée: surveillance des écouteurs Redis');
    try {
      // Utilisation de la même constante EXPECTED_LISTENERS que dans server.ts
      // Cette valeur est définie dans server.ts à la ligne 158
      const EXPECTED_LISTENERS = 50; // Nombre maximum d'écouteurs attendus pour Redis

      // Surveillance du nombre d'écouteurs et nettoyage
      redis.eventNames().forEach((eventName: string | symbol) => {
        const eventNameString = eventName.toString();
        const currentListeners = redis.listenerCount(eventName);
        if (currentListeners > EXPECTED_LISTENERS * 0.8) { // Alerte si proche de la limite (80%)
          logger.warn(`Nombre élevé d'écouteurs (${currentListeners}/${EXPECTED_LISTENERS}) pour l'événement ${eventNameString} sur Redis`);

          // Journaliser les détails des écouteurs pour aider au débogage
          logger.info(`Détails des écouteurs pour l'événement ${eventNameString}:`, {
            count: currentListeners,
            threshold: EXPECTED_LISTENERS,
            eventName: eventNameString
          });
        }
      });

      // Vérifier également les écouteurs sur RedisStore
      try {
        const RedisStore = require('connect-redis').RedisStore;
        const storeInstance = Object.values(global).find(val => val instanceof RedisStore);
        if (storeInstance) {
          storeInstance.eventNames().forEach((eventName: string | symbol) => {
            const eventNameString = eventName.toString();
            const currentListeners = storeInstance.listenerCount(eventName);
            if (currentListeners > EXPECTED_LISTENERS * 0.8) {
              logger.warn(`Nombre élevé d'écouteurs (${currentListeners}/${EXPECTED_LISTENERS}) pour l'événement ${eventNameString} sur RedisStore`);
            }
          });
        }
      } catch (error) {
        logger.error('Impossible de vérifier les écouteurs RedisStore:', error);
      }

      logger.info('Tâche de surveillance des écouteurs Redis terminée avec succès');
    } catch (error) {
      logger.error('Erreur lors de la surveillance des écouteurs Redis:', error);
    }
  }, {
    scheduled: true,
    timezone: 'Europe/Paris'
  });

  // Nettoyage des sessions Redis expirées (toutes les heures)
  cron.schedule('0 * * * *', async () => {
    logger.info('Exécution de la tâche planifiée: nettoyage des sessions Redis expirées');
    try {
      const keys = await redis.keys('jobpartiel:sess:*');
      logger.info(`Vérification de ${keys.length} sessions Redis`);

      let expiredCount = 0;
      for (const key of keys) {
        const ttl = await redis.ttl(key);
        if (ttl <= 0) {
          await redis.del(key);
          expiredCount++;
        }
      }

      if (expiredCount > 0) {
        logger.info(`${expiredCount} sessions Redis expirées ont été nettoyées`);
      }

      // Vérifier et nettoyer les listeners potentiellement orphelins sur RedisStore
      try {
        // Utiliser la référence directe au sessionStore si disponible
        if (sessionStore) {
          // Vérifier si le nombre d'écouteurs est anormalement élevé
          sessionStore.eventNames().forEach((eventName: string | symbol) => {
            const eventNameString = eventName.toString();
            const currentListeners = sessionStore.listenerCount(eventName);
            if (currentListeners > 40) { // Seuil arbitraire pour le nettoyage
              logger.warn(`Nettoyage des écouteurs pour l'événement ${eventNameString} sur RedisStore (${currentListeners} écouteurs)`);
              // Supprimer tous les écouteurs et réattacher uniquement ceux nécessaires
              sessionStore.removeAllListeners(eventName);
              // Réattacher les écouteurs essentiels si nécessaire
              if (eventNameString === 'connect' || eventNameString === 'disconnect') {
                sessionStore.on(eventNameString, () => {
                  logger.info(`RedisStore ${eventNameString} event`);
                });
              }
            }
          });

          // Journaliser l'état actuel des écouteurs après nettoyage
          logger.info('État des écouteurs RedisStore après nettoyage:', {
            eventNames: sessionStore.eventNames().map((e: string | symbol) => e.toString()),
            counts: sessionStore.eventNames().map((e: string | symbol) => ({
              event: e.toString(),
              count: sessionStore.listenerCount(e)
            }))
          });
        } else {
          // Fallback à la méthode précédente si sessionStore n'est pas disponible
          const RedisStore = require('connect-redis').RedisStore;
          const storeInstance = Object.values(global).find(val => val instanceof RedisStore);
          if (storeInstance) {
            logger.info('Utilisation du fallback pour nettoyer RedisStore');
            storeInstance.eventNames().forEach((eventName: string | symbol) => {
              const eventNameString = eventName.toString();
              const currentListeners = storeInstance.listenerCount(eventName);
              if (currentListeners > 40) {
                logger.warn(`Nettoyage des écouteurs pour l'événement ${eventNameString} sur RedisStore (${currentListeners} écouteurs)`);
                storeInstance.removeAllListeners(eventName);
                if (eventNameString === 'connect' || eventNameString === 'disconnect') {
                  storeInstance.on(eventNameString, () => {
                    logger.info(`RedisStore ${eventNameString} event`);
                  });
                }
              }
            });
          }
        }
      } catch (error) {
        logger.error('Impossible de nettoyer les écouteurs RedisStore:', error);
      }

      logger.info('Tâche de nettoyage des sessions Redis terminée avec succès');
    } catch (error) {
      logger.error('Erreur lors du nettoyage des sessions Redis expirées:', error);
    }
  }, {
    scheduled: true,
    timezone: 'Europe/Paris'
  });

  // Nettoyage des tokens expirés dans auth_tokens (tous les jours à 2h du matin)
  cron.schedule('0 2 * * *', async () => {
    logger.info('Exécution de la tâche planifiée: nettoyage des tokens expirés dans auth_tokens');
    try {
      const dbService = DatabaseService.getInstance();
      await dbService.cleanExpiredTokens();
      logger.info('Tâche de nettoyage des tokens expirés terminée avec succès');
    } catch (error) {
      logger.error('Erreur lors du nettoyage des tokens expirés:', error);
    }
  }, {
    scheduled: true,
    timezone: 'Europe/Paris'
  });

  // Nettoyage des logs de modération (tous les jours à 4h du matin)
  cron.schedule('0 4 * * *', async () => {
    logger.info('Exécution de la tâche planifiée: nettoyage des logs de modération');
    try {
      // 1. Récupérer l'ID du 20000ème log le plus récent
      const { data: cutoffLog, error: cutoffError } = await supabase
        .from('content_moderation_logs')
        .select('id, created_at')
        .order('created_at', { ascending: false })
        .range(19999, 19999); // Récupère uniquement le 20000ème enregistrement

      if (cutoffError) {
        logger.error('Erreur lors de la récupération du cutoff pour le nettoyage des logs de modération:', cutoffError);
        return;
      }

      if (!cutoffLog || cutoffLog.length === 0) {
        logger.info('Moins de 20000 logs de modération trouvés, aucun nettoyage nécessaire');
        return;
      }

      // 2. Supprimer tous les logs plus anciens que le cutoff
      const { error: deleteError } = await supabase
        .from('content_moderation_logs')
        .delete()
        .lt('created_at', cutoffLog[0].created_at);

      if (deleteError) {
        logger.error('Erreur lors de la suppression des anciens logs de modération:', deleteError);
        return;
      }

      logger.info('Nettoyage des logs de modération terminé avec succès');
    } catch (error) {
      logger.error('Erreur inattendue lors du nettoyage des logs de modération:', error);
    }
  }, {
    scheduled: true,
    timezone: 'Europe/Paris'
  });

  // Nettoyage du cache de modération (toutes les 3 heures)
  cron.schedule('0 */3 * * *', async () => {
    logger.info('Exécution de la tâche planifiée: nettoyage du cache de modération');
    try {
      const result = await cleanModerationCache();
      if (result.success) {
        logger.info('Nettoyage du cache de modération terminé avec succès', result.stats);
      } else {
        logger.error('Erreur lors du nettoyage du cache de modération:', result.message);
      }
    } catch (error) {
      logger.error('Erreur inattendue lors du nettoyage du cache de modération:', error);
    }
  }, {
    scheduled: true,
    timezone: 'Europe/Paris'
  });

  // Exécution initiale des tâches au démarrage (avec délais pour éviter de surcharger le serveur)
  setTimeout(async () => {
    try {
      logger.info('Exécution initiale de la vérification des offres expirées');
      await OfferExpirationChecker.checkExpiredOffers();
    } catch (error) {
      logger.error('Erreur lors de la vérification initiale des offres expirées:', error);
    }
  }, 2000); // 2 secondes de délai

  setTimeout(async () => {
    try {
      logger.info('Exécution initiale du traitement de la file d\'attente d\'emails');
      const { processQueue } = require('../services/processEmailQueue');
      await processQueue();
    } catch (error) {
      logger.error('Erreur lors du traitement initial de la file d\'attente d\'emails:', error);
    }
  }, 5000); // 5 secondes de délai

  // Exécution initiale du nettoyage des tokens expirés
  setTimeout(async () => {
    try {
      logger.info('Exécution initiale du nettoyage des tokens expirés');
      const dbService = DatabaseService.getInstance();
      await dbService.cleanExpiredTokens();
      logger.info('Nettoyage initial des tokens expirés terminé avec succès');
    } catch (error) {
      logger.error('Erreur lors du nettoyage initial des tokens expirés:', error);
    }
  }, 8000); // 8 secondes de délai

  // Exécution initiale du nettoyage du cache de modération
  setTimeout(async () => {
    try {
      logger.info('Exécution initiale du nettoyage du cache de modération');
      const result = await cleanModerationCache();
      if (result.success) {
        logger.info('Nettoyage initial du cache de modération terminé avec succès', result.stats);
      } else {
        logger.error('Erreur lors du nettoyage initial du cache de modération:', result.message);
      }
    } catch (error) {
      logger.error('Erreur lors du nettoyage initial du cache de modération:', error);
    }
  }, 10000); // 10 secondes de délai

  // Nettoyage des images du bucket temporaire "temp_moderation" (tous les jours à 3h du matin, plus de 1 mois)
  cron.schedule('0 3 * * *', async () => {
    logger.info('Exécution de la tâche planifiée: nettoyage des images temporaires de modération');
    try {
      // Nettoyer les images plus anciennes que 12 heures
      const deletedCount  = await cleanupTemporaryModerationImages(24 * 30); // 30 jours
      logger.info(`Nettoyage des images temporaires du bucket temporaire "temp_moderation" terminé avec succès: ${deletedCount} images supprimées`);
    } catch (error) {
      logger.error('Erreur lors du nettoyage des images temporaires du bucket temporaire "temp_moderation":', error);
    }
  }, {
    scheduled: true,
    timezone: 'Europe/Paris'
  });

  // Nettoyage des consentements IA expirés (tous les jours à 4h30 du matin)
  cron.schedule('30 4 * * *', async () => {
    logger.info('Exécution de la tâche planifiée: nettoyage des consentements IA expirés');
    try {
      await cleanupExpiredConsents();
      logger.info('Nettoyage des consentements IA expirés terminé avec succès');
    } catch (error) {
      logger.error('Erreur lors du nettoyage des consentements IA expirés:', error);
    }
  }, {
    scheduled: true,
    timezone: 'Europe/Paris'
  });

  // Exécution initiale du nettoyage des images temporaires de modération
  setTimeout(async () => {
    try {
      logger.info('Exécution initiale du nettoyage des images temporaires de modération');
      const deletedCount = await cleanupTemporaryModerationImages(12);
      logger.info(`Nettoyage initial des images temporaires du bucket temporaire "temp_moderation" terminé avec succès: ${deletedCount} images supprimées`);
    } catch (error) {
      logger.error('Erreur lors du nettoyage initial des images temporaires du bucket temporaire "temp_moderation":', error);
    }
  }, 15000); // 15 secondes de délai

  // Exécution initiale du nettoyage des consentements IA expirés
  setTimeout(async () => {
    try {
      logger.info('Exécution initiale du nettoyage des consentements IA expirés');
      await cleanupExpiredConsents();
      logger.info('Nettoyage initial des consentements IA expirés terminé avec succès');
    } catch (error) {
      logger.error('Erreur lors du nettoyage initial des consentements IA expirés:', error);
    }
  }, 20000); // 20 secondes de délai

  logger.info('Tâches planifiées configurées avec succès');
};

export default setupCronJobs;
