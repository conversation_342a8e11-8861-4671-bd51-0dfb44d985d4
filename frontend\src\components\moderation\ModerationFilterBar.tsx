import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Paper, 
  TextField, 
  Select, 
  MenuItem, 
  FormControl, 
  InputLabel, 
  Button, 
  Chip, 
  Typography, 
  Divider,
  Collapse,
  useMediaQuery,
  useTheme,
  InputAdornment
} from '@mui/material';
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { fr } from 'date-fns/locale';
import { registerLocale, setDefaultLocale } from "react-datepicker";
import { 
  FilterX, 
  ChevronDown, 
  ChevronUp, 
  Calendar, 
  Tag, 
  Shield, 
  CheckCircle, 
  XCircle,
  MessageSquareWarning,
  Eye,
  Swords,
  HeartCrack,
  Skull,
  MailWarning,
  Phone,
  MapPin,
  Ban,
  ImageIcon,
  MessageSquare,
  FileText,
  MessageCircle,
  User,
  Briefcase,
  BookOpen,
  Bookmark,
  Star,
  Camera,
  Images
} from 'lucide-react';
import { styled } from '@mui/material/styles';

// Formate le nom d'une catégorie de modération pour l'affichage
const formatCategoryName = (category: string): string => {
    const categoryMap: Record<string, string> = {
      'harassment': 'Harcèlement',
      'hateSpeech': 'Discours haineux',
      'sexualContent': 'Contenu sexuel',
      'violence': 'Violence',
      'selfHarm': 'Auto-mutilation',
      'illegalActivity': 'Activité illégale',
      'spam': 'Spam',
      'phoneSpam': 'Numéro de téléphone',
      'addressSpam': 'Adresse postale',
      'unknownRisk': 'Risque inconnu'
    };
  
    return categoryMap[category] || category;
};
  
// Formate le nom d'un type de contenu pour l'affichage
const formatContentType = (type: string): string => {
const typeMap: Record<string, string> = {
    'mission': 'Mission',
    'comment': 'Commentaire',
    'profile': 'Profil',
    'titre_service': 'Titre service',
    'description_service': 'Description service',
    'gallery_name': 'Nom galerie',
    'gallery_description': 'Description galerie',
    'mission_title': 'Titre mission',
    'mission_description': 'Description mission',
    'review': 'Avis',
    'gallery': 'Galerie',
    'gallery_cover': 'Couverture galerie',
    'featured': 'À la une',
    'mission_assistant': 'Assistant mission',
    'avatar': 'Avatar',
    'profile_picture': 'Photo profil',
    'banner_picture': 'Bannière',
    'featured_photo': 'Photo à la une',
    'mission_image': 'Image mission',
    'gallery_photo': 'Photo galerie'
};

return typeMap[type] || type;
};

// Enregistrer la locale française pour DatePicker
registerLocale('fr', fr);
setDefaultLocale('fr');

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

const FilterContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2, 3),
  marginBottom: theme.spacing(3),
  borderRadius: '16px',
  boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  transition: 'box-shadow 0.3s ease',
  '&:hover': {
    boxShadow: '0 6px 16px rgba(0,0,0,0.08)',
  }
}));

const FilterForm = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexWrap: 'wrap',
  gap: theme.spacing(2),
  alignItems: 'flex-end',
  [theme.breakpoints.down('sm')]: {
    flexDirection: 'column',
    alignItems: 'stretch',
  }
}));

const FilterFormControl = styled(FormControl)(({ theme }) => ({
  minWidth: 180,
  flex: 1,
  [theme.breakpoints.down('sm')]: {
    minWidth: '100%',
  }
}));

const ChipsContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexWrap: 'wrap',
  gap: theme.spacing(1),
  marginTop: theme.spacing(2),
}));

const ToggleFilterButton = styled(Button)(({ theme }) => ({
  marginLeft: 'auto',
  color: COLORS.primary,
  backgroundColor: 'transparent',
  borderRadius: '50px',
  fontWeight: 500,
  fontSize: '0.875rem',
  padding: theme.spacing(0.5, 2),
  '&:hover': {
    backgroundColor: `${COLORS.primary}10`,
  },
  [theme.breakpoints.down('sm')]: {
    marginLeft: 0,
    marginTop: theme.spacing(1),
    width: '100%',
  }
}));

const ActionButton = styled(Button)(({ theme }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  fontWeight: 600,
  boxShadow: 'none',
  padding: theme.spacing(1, 2),
  minWidth: 120,
  [theme.breakpoints.down('sm')]: {
    flex: 1,
  }
}));

const FilterDivider = styled(Divider)(({ theme }) => ({
  margin: theme.spacing(2, 0),
}));

const CategoryIconBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginRight: theme.spacing(1),
}));

// Style pour le DatePicker
const StyledDatePickerContainer = styled('div')(({ theme }) => ({
  width: '100%',
  '& .react-datepicker-wrapper': {
    width: '100%',
  },
  '& .datepicker-input': {
    width: '100%',
    padding: '10px 14px',
    borderRadius: '8px',
    border: `1px solid ${theme.palette.divider}`,
    backgroundColor: theme.palette.background.paper,
    fontSize: '0.9rem',
    transition: 'all 0.2s ease-in-out',
    '&:focus': {
      borderColor: COLORS.primary,
      outline: 'none',
      boxShadow: `0 0 0 2px ${COLORS.primary}20`,
    },
  },
  '& .react-datepicker-popper': {
    zIndex: 999,
  },
  '& .react-datepicker': {
    fontFamily: theme.typography.fontFamily,
    fontSize: '0.9rem',
    border: `1px solid ${theme.palette.divider}`,
    borderRadius: '12px',
    boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
  },
  '& .react-datepicker__header': {
    backgroundColor: COLORS.background,
    borderBottom: `1px solid ${theme.palette.divider}`,
    borderTopLeftRadius: '12px',
    borderTopRightRadius: '12px',
    padding: '12px 0',
  },
  '& .react-datepicker__current-month': {
    fontSize: '1rem',
    fontWeight: 600,
    color: COLORS.primary,
    marginBottom: '8px',
  },
  '& .react-datepicker__day-name': {
    width: '32px',
    fontSize: '0.8rem',
    color: theme.palette.text.secondary,
  },
  '& .react-datepicker__day': {
    width: '32px',
    height: '32px',
    lineHeight: '32px',
    borderRadius: '50%',
    margin: '2px',
    color: theme.palette.text.primary,
    '&:hover': {
      backgroundColor: `${COLORS.primary}20`,
      color: COLORS.primary,
    },
  },
  '& .react-datepicker__day--selected': {
    backgroundColor: COLORS.primary,
    color: 'white',
    fontWeight: 600,
    '&:hover': {
      backgroundColor: COLORS.secondary,
    },
  },
  '& .react-datepicker__day--keyboard-selected': {
    backgroundColor: `${COLORS.primary}30`,
    color: COLORS.primary,
  },
  '& .react-datepicker__day--today': {
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  '& .react-datepicker__day--disabled': {
    color: theme.palette.text.disabled,
    '&:hover': {
      backgroundColor: 'transparent',
    },
  },
  '& .react-datepicker__navigation': {
    top: '12px',
  },
  '& .react-datepicker__navigation-icon::before': {
    borderColor: COLORS.primary,
  },
  '& .react-datepicker__triangle': {
    display: 'none',
  },
}));

// Composant personnalisé pour l'input du DatePicker
const CustomDatePickerInput = React.forwardRef<HTMLInputElement, React.InputHTMLAttributes<HTMLInputElement> & { value?: string; onClick?: () => void }>(({ value, onClick, size: htmlSize, color: htmlColor, ...props }, ref) => (
    <TextField
      fullWidth
      size="small"
      variant="outlined"
      value={value}
      onClick={onClick}
      InputProps={{
        endAdornment: (
          <InputAdornment position="end">
            <Calendar size={18} color={COLORS.primary} />
          </InputAdornment>
        ),
      }}
      inputRef={ref}
      {...props}
    />
  )
);

interface ModerationFilterBarProps {
  availableFilters?: {
    contentTypes?: string[];
    categories?: string[];
    methods?: string[];
  };
  appliedFilters?: {
    startDate: string | null;
    endDate: string | null;
    contentType: string | null;
    category: string | null;
    method: string | null;
    isSafe: boolean | null;
  };
  onFilterChange: (filters: any) => void;
}

// Fonctions pour obtenir les icônes par type de contenu
const getContentTypeIcon = (type: string) => {
  switch(type) {
    case 'mission':
      return <Briefcase size={18} color="#FF6B2C" />;
    case 'comment':
      return <MessageCircle size={18} color="#2196F3" />;
    case 'profile':
      return <User size={18} color="#9C27B0" />;
    case 'titre_service':
    case 'description_service':
      return <Bookmark size={18} color="#FFA726" />;
    case 'gallery_name':
    case 'gallery_description':
      return <Images size={18} color="#26A69A" />;
    case 'mission_title':
    case 'mission_description':
      return <FileText size={18} color="#5C6BC0" />;
    case 'review':
      return <Star size={18} color="#FFC107" />;
    case 'gallery':
    case 'gallery_cover':
    case 'featured':
    case 'mission_assistant':
    case 'avatar':
    case 'profile_picture':
    case 'banner_picture':
    case 'featured_photo':
    case 'mission_image':
    case 'gallery_photo':
      return <Camera size={18} color="#E91E63" />;
    default:
      return <BookOpen size={18} color="#607D8B" />;
  }
};

const ModerationFilterBar: React.FC<ModerationFilterBarProps> = ({
  availableFilters = { contentTypes: [], categories: [], methods: [] },
  appliedFilters = { startDate: null, endDate: null, contentType: null, category: null, method: null, isSafe: null },
  onFilterChange,
}) => {
  const [showFilters, setShowFilters] = useState(false);
  const [startDate, setStartDate] = useState<Date | null>(appliedFilters.startDate ? new Date(appliedFilters.startDate) : null);
  const [endDate, setEndDate] = useState<Date | null>(appliedFilters.endDate ? new Date(appliedFilters.endDate) : null);
  const [contentType, setContentType] = useState<string | null>(appliedFilters.contentType);
  const [category, setCategory] = useState<string | null>(appliedFilters.category);
  const [method, setMethod] = useState<string | null>(appliedFilters.method);
  const [isSafe, setIsSafe] = useState<string>(
    appliedFilters.isSafe === null ? '' : appliedFilters.isSafe ? 'true' : 'false'
  );
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Mise à jour de l'affichage des filtres actifs
  useEffect(() => {
    const filters = [];
    
    if (startDate) {
      filters.push('date_debut');
    }
    
    if (endDate) {
      filters.push('date_fin');
    }
    
    if (contentType) {
      filters.push('type_contenu');
    }
    
    if (category) {
      filters.push('categorie');
    }
    
    if (method) {
      filters.push('methode');
    }
    
    if (isSafe !== '') {
      filters.push('resultat');
    }
    
    setActiveFilters(filters);
    
    // Si des filtres sont actifs, afficher le panneau de filtres
    if (filters.length > 0 && !showFilters) {
      setShowFilters(true);
    }
  }, [startDate, endDate, contentType, category, method, isSafe, showFilters]);

  // Fonction pour appliquer les filtres (peut être appelée directement ou par d'autres handlers)
  const handleApplyFilters = () => {
    const filters = {
      startDate: startDate ? startDate.toISOString() : null,
      endDate: endDate ? endDate.toISOString() : null,
      contentType,
      category,
      method,
      isSafe: isSafe === '' ? null : isSafe === 'true',
    };
    
    onFilterChange(filters);
  };

  // Handlers pour chaque type de filtre avec application automatique
  const handleStartDateChange = (date: Date | null) => {
    setStartDate(date);
    setTimeout(() => {
      onFilterChange({
        startDate: date ? date.toISOString() : null,
        endDate: endDate ? endDate.toISOString() : null,
        contentType,
        category,
        method,
        isSafe: isSafe === '' ? null : isSafe === 'true',
      });
    }, 0);
  };

  const handleEndDateChange = (date: Date | null) => {
    setEndDate(date);
    setTimeout(() => {
      onFilterChange({
        startDate: startDate ? startDate.toISOString() : null,
        endDate: date ? date.toISOString() : null,
        contentType,
        category,
        method,
        isSafe: isSafe === '' ? null : isSafe === 'true',
      });
    }, 0);
  };

  const handleContentTypeChange = (value: string) => {
    const newValue = value === '' ? null : value;
    setContentType(newValue);
    setTimeout(() => {
      onFilterChange({
        startDate: startDate ? startDate.toISOString() : null,
        endDate: endDate ? endDate.toISOString() : null,
        contentType: newValue,
        category,
        method,
        isSafe: isSafe === '' ? null : isSafe === 'true',
      });
    }, 0);
  };

  const handleCategoryChange = (value: string) => {
    const newValue = value === '' ? null : value;
    setCategory(newValue);
    setTimeout(() => {
      onFilterChange({
        startDate: startDate ? startDate.toISOString() : null,
        endDate: endDate ? endDate.toISOString() : null,
        contentType,
        category: newValue,
        method,
        isSafe: isSafe === '' ? null : isSafe === 'true',
      });
    }, 0);
  };

  const handleMethodChange = (value: string) => {
    const newValue = value === '' ? null : value;
    setMethod(newValue);
    setTimeout(() => {
      onFilterChange({
        startDate: startDate ? startDate.toISOString() : null,
        endDate: endDate ? endDate.toISOString() : null,
        contentType,
        category,
        method: newValue,
        isSafe: isSafe === '' ? null : isSafe === 'true',
      });
    }, 0);
  };

  const handleSafeStatusChange = (value: string) => {
    setIsSafe(value);
    setTimeout(() => {
      onFilterChange({
        startDate: startDate ? startDate.toISOString() : null,
        endDate: endDate ? endDate.toISOString() : null,
        contentType,
        category,
        method,
        isSafe: value === '' ? null : value === 'true',
      });
    }, 0);
  };

  const handleResetFilters = () => {
    setStartDate(null);
    setEndDate(null);
    setContentType(null);
    setCategory(null);
    setMethod(null);
    setIsSafe('');
    
    onFilterChange({
      startDate: null,
      endDate: null,
      contentType: null,
      category: null,
      method: null,
      isSafe: null,
    });
  };
  
  const handleRemoveFilter = (filterName: string) => {
    switch (filterName) {
      case 'date_debut':
        setStartDate(null);
        break;
      case 'date_fin':
        setEndDate(null);
        break;
      case 'type_contenu':
        setContentType(null);
        break;
      case 'categorie':
        setCategory(null);
        break;
      case 'methode':
        setMethod(null);
        break;
      case 'resultat':
        setIsSafe('');
        break;
    }
    
    // Appliquer les filtres après suppression
    setTimeout(handleApplyFilters, 0);
  };
  
  // Mapper les méthodes à des libellés plus lisibles
  const methodLabels: Record<string, string> = {
    'textIA': 'Texte (IA)',
    'textNonIA': 'Texte (SANS IA)',
    'imageIA': 'Image (IA)',
    'other': 'Autre'
  };
  
  // Icônes pour les catégories
  const getCategoryIcon = (category: string) => {
    switch(category) {
      case 'harassment':
        return <MessageSquareWarning size={18} color={COLORS.error} />;
      case 'hateSpeech':
        return <Ban size={18} color="#FF7043" />;
      case 'sexualContent':
        return <Eye size={18} color="#AB47BC" />;
      case 'violence':
        return <Swords size={18} color="#5C6BC0" />;
      case 'selfHarm':
        return <HeartCrack size={18} color="#26A69A" />;
      case 'illegalActivity':
        return <Skull size={18} color="#FFA726" />;
      case 'spam':
        return <MailWarning size={18} color="#29B6F6" />;
      case 'phoneSpam':
        return <Phone size={18} color="#EC407A" />;
      case 'addressSpam':
        return <MapPin size={18} color="#66BB6A" />;
      default:
        return <Shield size={18} color={COLORS.primary} />;
    }
  };
  
  // Icônes pour les méthodes
  const getMethodIcon = (method: string) => {
    switch(method) {
      case 'textIA':
        return <MessageSquare size={18} color="#4285F4" />;
      case 'textNonIA':
        return <MessageSquare size={18} color="#34A853" />;
      case 'imageIA':
        return <ImageIcon size={18} color="#FBBC05" />;
      default:
        return <Shield size={18} color="#EA4335" />;
    }
  };

  // Générer les libellés des filtres actifs
  const getFilterLabel = (filterName: string): string => {
    switch (filterName) {
      case 'date_debut':
        return `Depuis: ${startDate?.toLocaleDateString('fr-FR')}`;
      case 'date_fin':
        return `Jusqu'à: ${endDate?.toLocaleDateString('fr-FR')}`;
      case 'type_contenu':
        return `Type: ${formatContentType(contentType || '')}`;
      case 'categorie':
        return `Catégorie: ${formatCategoryName(category || '')}`;
      case 'methode':
        return `Méthode: ${methodLabels[method || ''] || method}`;
      case 'resultat':
        return `Résultat: ${isSafe === 'true' ? 'Sûr' : 'Inapproprié'}`;
      default:
        return filterName;
    }
  };
  
  // Générer les icônes des filtres actifs
  const getFilterIcon = (filterName: string) => {
    switch (filterName) {
      case 'date_debut':
      case 'date_fin':
        return <Calendar size={16} />;
      case 'type_contenu':
        return contentType ? getContentTypeIcon(contentType) : <Tag size={16} />;
      case 'categorie':
        return category ? getCategoryIcon(category) : <Shield size={16} />;
      case 'methode':
        return method ? getMethodIcon(method) : <Shield size={16} />;
      case 'resultat':
        return isSafe === 'true' ? <CheckCircle size={16} color={COLORS.success} /> : <XCircle size={16} color={COLORS.error} />;
      default:
        return <Shield size={16} />;
    }
  };

  // Générer les couleurs des filtres actifs
  const getFilterColor = (filterName: string): string => {
    switch (filterName) {
      case 'date_debut':
      case 'date_fin':
        return '#6B7280';
      case 'type_contenu':
        return COLORS.primary;
      case 'categorie':
        return COLORS.tertiary;
      case 'methode':
        return COLORS.secondary;
      case 'resultat':
        return isSafe === 'true' ? COLORS.success : COLORS.error;
      default:
        return COLORS.neutral;
    }
  };

  return (
    <FilterContainer>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap' }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#2D3748', display: 'flex', alignItems: 'center' }}>
          <Shield size={20} color={COLORS.primary} style={{ marginRight: 8 }} />
          Filtrer les statistiques
        </Typography>
        
        <ToggleFilterButton
          onClick={() => setShowFilters(!showFilters)}
          startIcon={showFilters ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
        >
          {showFilters ? 'Masquer les filtres' : 'Afficher les filtres'}
        </ToggleFilterButton>
      </Box>

      {activeFilters.length > 0 && (
        <ChipsContainer>
          {activeFilters.map((filter) => (
            <Chip
              key={filter}
              label={getFilterLabel(filter)}
              onDelete={() => handleRemoveFilter(filter)}
              icon={
                <CategoryIconBox>
                  {getFilterIcon(filter)}
                </CategoryIconBox>
              }
              sx={{
                borderRadius: '50px',
                backgroundColor: `${getFilterColor(filter)}15`,
                color: getFilterColor(filter),
                fontWeight: 500,
                '& .MuiChip-deleteIcon': {
                  color: `${getFilterColor(filter)}80`,
                  '&:hover': {
                    color: getFilterColor(filter),
                  },
                },
                '& .MuiChip-icon': {
                  color: getFilterColor(filter),
                },
              }}
            />
          ))}
          
          {activeFilters.length > 1 && (
            <Chip
              label="Effacer tous les filtres"
              onDelete={handleResetFilters}
              deleteIcon={<FilterX size={16} />}
              sx={{
                borderRadius: '50px',
                backgroundColor: `${COLORS.error}15`,
                color: COLORS.error,
                fontWeight: 500,
                '& .MuiChip-deleteIcon': {
                  color: `${COLORS.error}90`,
                  '&:hover': {
                    color: COLORS.error,
                  },
                },
              }}
            />
          )}
        </ChipsContainer>
      )}

      <Collapse in={showFilters}>
        <FilterDivider />
        
        <FilterForm>
          <FilterFormControl>
            <InputLabel shrink sx={{ position: 'relative', transform: 'none', mb: 1 }}>
              Date de début
            </InputLabel>
            <StyledDatePickerContainer>
              <DatePicker
                selected={startDate}
                onChange={handleStartDateChange}
                dateFormat="dd/MM/yyyy"
                placeholderText="Sélectionner une date"
                locale="fr"
                isClearable
                customInput={<CustomDatePickerInput />}
              />
            </StyledDatePickerContainer>
          </FilterFormControl>

          <FilterFormControl>
            <InputLabel shrink sx={{ position: 'relative', transform: 'none', mb: 1 }}>
              Date de fin
            </InputLabel>
            <StyledDatePickerContainer>
              <DatePicker
                selected={endDate}
                onChange={handleEndDateChange}
                dateFormat="dd/MM/yyyy"
                placeholderText="Sélectionner une date"
                locale="fr"
                isClearable
                customInput={<CustomDatePickerInput />}
              />
            </StyledDatePickerContainer>
          </FilterFormControl>

          <FilterFormControl>
            <InputLabel id="content-type-label">Type de contenu</InputLabel>
            <Select
              labelId="content-type-label"
              id="content-type"
              value={contentType || ''}
              onChange={(e) => handleContentTypeChange(e.target.value)}
              label="Type de contenu"
              size="small"
              startAdornment={
                <InputAdornment position="start">
                  {contentType ? getContentTypeIcon(contentType) : <Tag size={18} color={COLORS.primary} />}
                </InputAdornment>
              }
            >
              <MenuItem value="">Tous les types</MenuItem>
              {availableFilters.contentTypes?.map((type) => (
                <MenuItem key={type} value={type} sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                    {getContentTypeIcon(type)}
                  </Box>
                  {formatContentType(type)}
                </MenuItem>
              ))}
            </Select>
          </FilterFormControl>

          <FilterFormControl>
            <InputLabel id="category-label">Catégorie</InputLabel>
            <Select
              labelId="category-label"
              id="category"
              value={category || ''}
              onChange={(e) => handleCategoryChange(e.target.value)}
              label="Catégorie"
              size="small"
              startAdornment={
                <InputAdornment position="start">
                  {category ? getCategoryIcon(category) : <Shield size={18} color={COLORS.tertiary} />}
                </InputAdornment>
              }
            >
              <MenuItem value="">Toutes les catégories</MenuItem>
              {availableFilters.categories?.map((cat) => (
                <MenuItem key={cat} value={cat} sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                    {getCategoryIcon(cat)}
                  </Box>
                  {formatCategoryName(cat)}
                </MenuItem>
              ))}
            </Select>
          </FilterFormControl>

          <FilterFormControl>
            <InputLabel id="method-label">Méthode de détection</InputLabel>
            <Select
              labelId="method-label"
              id="method"
              value={method || ''}
              onChange={(e) => handleMethodChange(e.target.value)}
              label="Méthode de détection"
              size="small"
              startAdornment={
                <InputAdornment position="start">
                  {method ? getMethodIcon(method) : <Shield size={18} color={COLORS.secondary} />}
                </InputAdornment>
              }
            >
              <MenuItem value="">Toutes les méthodes</MenuItem>
              {availableFilters.methods?.map((methodType) => (
                <MenuItem key={methodType} value={methodType} sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                    {getMethodIcon(methodType)}
                  </Box>
                  {methodLabels[methodType] || methodType}
                </MenuItem>
              ))}
            </Select>
          </FilterFormControl>

          <FilterFormControl>
            <InputLabel id="safe-status-label">Résultat</InputLabel>
            <Select
              labelId="safe-status-label"
              id="safe-status"
              value={isSafe}
              onChange={(e) => handleSafeStatusChange(e.target.value)}
              label="Résultat"
              size="small"
              startAdornment={
                <InputAdornment position="start">
                  {isSafe === 'true' ? <CheckCircle size={18} color={COLORS.success} /> : isSafe === 'false' ? <XCircle size={18} color={COLORS.error} /> : <Shield size={18} color={COLORS.primary} />}
                </InputAdornment>
              }
            >
              <MenuItem value="">Tous les résultats</MenuItem>
              <MenuItem value="true" sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                  <CheckCircle size={18} color={COLORS.success} />
                </Box>
                Contenu sûr
              </MenuItem>
              <MenuItem value="false" sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                  <XCircle size={18} color={COLORS.error} />
                </Box>
                Contenu bloqué
              </MenuItem>
            </Select>
          </FilterFormControl>
        </FilterForm>

        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'flex-end', 
          mt: 3,
          gap: 2,
          flexWrap: isMobile ? 'wrap' : 'nowrap'
        }}>
          <ActionButton
            variant="outlined"
            color="inherit"
            onClick={handleResetFilters}
            startIcon={<FilterX size={18} />}
            sx={{ 
              borderColor: '#E0E0E0',
              color: '#666',
              '&:hover': {
                backgroundColor: '#f5f5f5',
                borderColor: '#d0d0d0',
              }
            }}
          >
            Réinitialiser
          </ActionButton>
        </Box>
      </Collapse>
    </FilterContainer>
  );
};

export default ModerationFilterBar; 