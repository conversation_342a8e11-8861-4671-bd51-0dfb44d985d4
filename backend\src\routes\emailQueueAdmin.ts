import { Router, Request, Response } from 'express';
import { supabase } from '../config/supabase';
import { authMiddleware } from '../middleware/authMiddleware';
import rateLimit from 'express-rate-limit';
import { decryptDataAsync, encryptDataAsync } from '../utils/encryption';
import { sendEmailWithRetry } from '../services/emailService';
import logger from '../utils/logger';

const router = Router();

// Authentification admin obligatoire
router.use(authMiddleware.authenticateToken);
router.use(authMiddleware.checkRole(['jobpadm', 'jobmodo']));

// Rate limiter pour l'administration Email Queue
const emailQueueLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 40, // 40 requêtes par minute par IP
  message: { error: 'Trop de requêtes sur l\'admin Email Queue, veuillez réessayer dans 1 minute.' },
  standardHeaders: true,
  legacyHeaders: false
});

// Application du rate limiter à toutes les routes de ce router
router.use(emailQueueLimiter);

// GET /api/admin/email-queue?limit=50&offset=0
router.get('/', async (req: Request, res: Response) => {
  const limit = parseInt((req.query.limit as string) || '50', 10);
  const offset = parseInt((req.query.offset as string) || '0', 10);
  const to = (req.query.to as string) || '';
  const subject = (req.query.subject as string) || '';
  const status = (req.query.status as string) || '';
  const sort = (req.query.sort as string) || 'to_email';
  const order = (req.query.order as string) === 'desc' ? { ascending: false } : { ascending: true };

  let query = supabase
    .from('email_queue')
    .select('*', { count: 'exact' })
    .order(sort, order);

  // Pour la recherche par email, on ne peut pas faire de recherche directe sur les données chiffrées
  // On récupère d'abord tous les emails, puis on filtre côté serveur
  if (subject) query = query.ilike('subject', `%${subject}%`);
  if (status) query = query.ilike('status', `%${status}%`);

  query = query.range(offset, offset + limit - 1);

  const { data, error, count } = await query;

  if (error) {
    res.status(500).json({ success: false, message: error.message });
    return;
  }

  // Décrypter les emails avant de les renvoyer
  let decryptedEmails = await Promise.all(data?.map(async (email: any) => ({
    ...email,
    to_email: await decryptDataAsync(email.to_email)
  })) || []);

  // Filtrer par email si nécessaire (après décryptage)
  if (to) {
    decryptedEmails = decryptedEmails.filter((email: any) => 
      email.to_email.toLowerCase().includes(to.toLowerCase())
    );
  }

  res.json({ success: true, emails: decryptedEmails, total: count });
  return;
});

// PUT /api/admin/email-queue/:id
router.put('/:id', async (req: Request, res: Response) => {
  const id = req.params.id;
  const fields = req.body;

  try {
    // Vérifier d'abord l'état actuel de l'email
    const { data: currentEmail, error: fetchError } = await supabase
      .from('email_queue')
      .select('status')
      .eq('id', id)
      .single();

    if (fetchError || !currentEmail) {
      res.status(404).json({ success: false, message: 'Email non trouvé.' });
      return;
    }

    // Empêcher la modification des emails déjà envoyés (sauf pour les admins qui veulent changer le statut)
    if (currentEmail.status === 'sent' && fields.status !== 'pending') {
      res.status(400).json({
        success: false,
        message: 'Impossible de modifier un email déjà envoyé. Utilisez la fonction "Renvoyer" si nécessaire.'
      });
      return;
    }

    // On ne modifie que les champs autorisés
    const allowed = ['to_email', 'subject', 'html', 'status'];
    const update: any = {};

    for (const key of allowed) {
      if (fields[key] !== undefined) {
        if (key === 'to_email') {
          // Chiffrer l'email avant de le sauvegarder
          update[key] = await encryptDataAsync(fields[key]);
        } else {
          update[key] = fields[key];
        }
      }
    }

    if (Object.keys(update).length === 0) {
      res.status(400).json({ success: false, message: 'Aucun champ à modifier.' });
      return;
    }

    // Si on remet un email en pending, réinitialiser les compteurs
    if (update.status === 'pending') {
      update.retry_count = 0;
      update.last_retry = null;
      update.error_message = null;
      update.sent_at = null;
    }

    const { data, error } = await supabase
      .from('email_queue')
      .update(update)
      .eq('id', id)
      .select();

    if (error) {
      res.status(500).json({ success: false, message: error.message });
      return;
    }

    // Décrypter l'email dans la réponse
    const decryptedEmail = data?.[0] ? {
      ...data[0],
      to_email: await decryptDataAsync(data[0].to_email)
    } : null;

    logger.info('Email modifié avec succès', {
      id,
      modifiedBy: req.user?.id,
      changes: Object.keys(update)
    });

    res.json({ success: true, email: decryptedEmail });
    return;

  } catch (error) {
    logger.error('Erreur lors de la modification de l\'email:', error);
    res.status(500).json({ success: false, message: 'Erreur lors de la modification de l\'email.' });
    return;
  }
});

// POST /api/admin/email-queue/force-send/:id - Forcer l'envoi d'un email
router.post('/force-send/:id', async (req: Request, res: Response) => {
  const id = req.params.id;

  try {
    // Récupérer l'email
    const { data: email, error: fetchError } = await supabase
      .from('email_queue')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError || !email) {
      res.status(404).json({
        success: false,
        message: 'Email non trouvé'
      });
      return;
    }

    if (email.status === 'sent') {
      res.status(400).json({
        success: false,
        message: 'Cet email a déjà été envoyé'
      });
      return;
    }

    // Déchiffrer l'adresse email
    const decryptedEmail = await decryptDataAsync(email.to_email);

    // Bloquer les emails anonymisés pour le RGPD
    if (decryptedEmail.includes('@supprime.local')) {
      logger.warn('Email anonymisé bloqué (RGPD) - envoi forcé refusé', {
        id: email.id,
        email: decryptedEmail.substring(0, 20) + '...',
        subject: email.subject,
        forcedBy: req.user?.id
      });

      res.status(400).json({
        success: false,
        message: 'Impossible d\'envoyer un email anonymisé (RGPD)'
      });
      return;
    }

    // Préparer les options d'email
    const mailOptions: any = {
      to: decryptedEmail,
      subject: email.subject,
      html: email.html,
      from: `"Job Partiel" <<EMAIL>>`
    };

    // Ajouter les attachments s'ils existent
    if (email.attachments) {
      try {
        mailOptions.attachments = JSON.parse(email.attachments);
      } catch (parseError) {
        logger.error('Erreur lors du parsing des attachments:', parseError);
      }
    }

    // Envoyer l'email
    await sendEmailWithRetry(mailOptions);

    // Marquer l'email comme envoyé
    const { data: updatedEmail, error: updateError } = await supabase
      .from('email_queue')
      .update({
        status: 'sent',
        sent_at: new Date().toISOString(),
        retry_count: (email.retry_count || 0) + 1,
        last_retry: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      logger.error('Erreur lors de la mise à jour du statut:', updateError);
      res.status(500).json({
        success: false,
        message: 'Email envoyé mais erreur lors de la mise à jour du statut'
      });
      return;
    }

    logger.info('Email forcé envoyé avec succès', {
      id: email.id,
      to: decryptedEmail,
      subject: email.subject,
      forcedBy: req.user?.id
    });

    // Décrypter l'email dans la réponse
    const decryptedResponse = updatedEmail ? {
      ...updatedEmail,
      to_email: await decryptDataAsync(updatedEmail.to_email)
    } : null;

    res.json({
      success: true,
      message: 'Email envoyé avec succès',
      email: decryptedResponse
    });
    return;

  } catch (error) {
    logger.error('Erreur lors de l\'envoi forcé de l\'email:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'envoi de l\'email'
    });
    return;
  }
});

// POST /api/admin/email-queue/force-send-all - Forcer l'envoi de tous les emails en attente
router.post('/force-send-all', async (req: Request, res: Response) => {
  try {
    // Récupérer tous les emails en attente
    const { data: emails, error: fetchError } = await supabase
      .from('email_queue')
      .select('*')
      .eq('status', 'pending')
      .order('created_at', { ascending: true })
      .limit(50); // Limiter à 50 pour éviter la surcharge

    if (fetchError) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des emails'
      });
      return;
    }

    if (!emails || emails.length === 0) {
      res.json({
        success: true,
        message: 'Aucun email en attente',
        processed: 0,
        sent: 0,
        failed: 0
      });
      return;
    }

    let sent = 0;
    let failed = 0;

    // Traiter chaque email
    for (const email of emails) {
      try {
        // Déchiffrer l'adresse email
        const decryptedEmail = await decryptDataAsync(email.to_email);

        // Bloquer les emails anonymisés pour le RGPD
        if (decryptedEmail.includes('@supprime.local')) {
          logger.warn('Email anonymisé bloqué (RGPD) - envoi forcé refusé', {
            id: email.id,
            email: decryptedEmail.substring(0, 20) + '...',
            subject: email.subject,
            forcedBy: req.user?.id
          });

          continue;
        }

        // Préparer les options d'email
        const mailOptions: any = {
          to: decryptedEmail,
          subject: email.subject,
          html: email.html,
          from: `"Job Partiel" <<EMAIL>>`
        };

        // Ajouter les attachments s'ils existent
        if (email.attachments) {
          try {
            mailOptions.attachments = JSON.parse(email.attachments);
          } catch (parseError) {
            logger.error('Erreur lors du parsing des attachments:', parseError);
          }
        }

        // Envoyer l'email
        await sendEmailWithRetry(mailOptions);

        // Marquer l'email comme envoyé
        await supabase
          .from('email_queue')
          .update({
            status: 'sent',
            sent_at: new Date().toISOString(),
            retry_count: (email.retry_count || 0) + 1,
            last_retry: new Date().toISOString()
          })
          .eq('id', email.id);

        sent++;

        logger.info('Email forcé envoyé avec succès', {
          id: email.id,
          to: decryptedEmail,
          subject: email.subject,
          forcedBy: req.user?.id
        });

      } catch (error) {
        failed++;

        // Marquer l'email comme échoué
        await supabase
          .from('email_queue')
          .update({
            status: 'failed',
            retry_count: (email.retry_count || 0) + 1,
            last_retry: new Date().toISOString(),
            error_message: error instanceof Error ? error.message : 'Erreur inconnue'
          })
          .eq('id', email.id);

        logger.error('Erreur lors de l\'envoi forcé de l\'email:', {
          id: email.id,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    logger.info('Envoi forcé terminé', {
      processed: emails.length,
      sent,
      failed,
      forcedBy: req.user?.id
    });

    res.json({
      success: true,
      message: `Traitement terminé: ${sent} envoyés, ${failed} échoués`,
      processed: emails.length,
      sent,
      failed
    });
    return;

  } catch (error) {
    logger.error('Erreur lors de l\'envoi forcé de tous les emails:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du traitement des emails'
    });
    return;
  }
});

// POST /api/admin/email-queue/resend/:id - Renvoyer un email déjà envoyé
router.post('/resend/:id', async (req: Request, res: Response) => {
  const id = req.params.id;

  try {
    // Récupérer l'email
    const { data: email, error: fetchError } = await supabase
      .from('email_queue')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError || !email) {
      res.status(404).json({
        success: false,
        message: 'Email non trouvé'
      });
      return;
    }

    // Déchiffrer l'adresse email
    const decryptedEmail = await decryptDataAsync(email.to_email);

    // Bloquer les emails anonymisés pour le RGPD
    if (decryptedEmail.includes('@supprime.local')) {
      logger.warn('Email anonymisé bloqué (RGPD) - envoi forcé refusé', {
        id: email.id,
        email: decryptedEmail.substring(0, 20) + '...',
        subject: email.subject,
        forcedBy: req.user?.id
      });

      res.status(400).json({
        success: false,
        message: 'Impossible d\'envoyer un email anonymisé (RGPD)'
      });
      return;
    }

    // Préparer les options d'email
    const mailOptions: any = {
      to: decryptedEmail,
      subject: email.subject,
      html: email.html,
      from: `"Job Partiel" <<EMAIL>>`
    };

    // Ajouter les attachments s'ils existent
    if (email.attachments) {
      try {
        mailOptions.attachments = JSON.parse(email.attachments);
      } catch (parseError) {
        logger.error('Erreur lors du parsing des attachments:', parseError);
      }
    }

    // Envoyer l'email
    await sendEmailWithRetry(mailOptions);

    // Créer une nouvelle entrée dans la queue pour tracer le renvoi
    const { data: newEmail, error: insertError } = await supabase
      .from('email_queue')
      .insert({
        to_email: email.to_email, // Déjà chiffré
        subject: `[MAIL RENVOYÉ PAR ADMIN] ${email.subject}`,
        html: email.html,
        attachments: email.attachments,
        status: 'sent',
        sent_at: new Date().toISOString(),
        retry_count: 1
      })
      .select()
      .single();

    if (insertError) {
      logger.error('Erreur lors de la création de l\'entrée de renvoi:', insertError);
      // L'email a été envoyé mais on n'a pas pu tracer le renvoi
    }

    logger.info('Email renvoyé avec succès', {
      originalId: email.id,
      newId: newEmail?.id,
      to: decryptedEmail,
      subject: email.subject,
      resentBy: req.user?.id
    });

    res.json({
      success: true,
      message: 'Email renvoyé avec succès',
      newEmailId: newEmail?.id
    });
    return;

  } catch (error) {
    logger.error('Erreur lors du renvoi de l\'email:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du renvoi de l\'email'
    });
    return;
  }
});

export default router;