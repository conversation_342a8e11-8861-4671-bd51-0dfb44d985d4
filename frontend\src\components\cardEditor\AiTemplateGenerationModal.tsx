import React, { useState } from 'react';
import {
  CircularProgress,
  Divider,
  FormControl,
  Select,
  MenuItem,
  Tooltip,
  Chip
} from '@mui/material';
import { Close, AutoAwesome } from '@mui/icons-material';
import { AlertTriangle, <PERSON>rkles, Info, Zap } from 'lucide-react';
import { CardTemplateData, CardTemplateType } from '../../types/cardEditor';
import CardEditorCanvas from './CardEditorCanvas';
import cardEditorService from '../../services/cardEditorService';
import { notify } from '../../components/Notification';
import { useNavigate } from 'react-router-dom';
import AiConsentModal from '../ai/AiConsentModal';
import ModalPortal from '../ModalPortal';
import { motion, AnimatePresence } from 'framer-motion';
import { useAiCredits } from '../../hooks/useAiCredits';
import logger from '../../utils/logger';

interface AiTemplateGenerationModalProps {
  open: boolean;
  onClose: () => void;
  templateType: CardTemplateType;
  onTemplateSelect: (template: CardTemplateData) => void;
}

// Définition des animations pour les cartes de template
const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.1,
      duration: 0.3,
      ease: "easeOut"
    }
  }),
  hover: {
    y: -8,
    boxShadow: "0 10px 25px rgba(0,0,0,0.1)",
    transition: { duration: 0.2 }
  },
  tap: { scale: 0.98 }
};

// Ajouter un tableau de messages pour chaque phase de la génération
const generationPhaseMessages = [
  [
    "Initialisation du processus IA...",
    "Chargement des données nécessaires...",
    "Préparation du modèle IA...",
    "Analyse des paramètres de design..."
  ],
  [
    "Génération des éléments graphiques...",
    "Création de la structure du design...",
    "Optimisation de la mise en page...",
    "Ajustement des couleurs et contrastes..."
  ],
  [
    "Finalisation du template...",
    "Application des dernières touches...",
    "Vérification de la cohérence visuelle...",
    "Préparation à l'affichage..."
  ]
];

const AiTemplateGenerationModal: React.FC<AiTemplateGenerationModalProps> = ({
  open,
  onClose,
  templateType,
  onTemplateSelect
}) => {
  const navigate = useNavigate();
  const { credits, loading: creditsLoading, error: creditsError } = useAiCredits();

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [templates, setTemplates] = useState<CardTemplateData[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [aiConsentModalOpen, setAiConsentModalOpen] = useState<boolean>(false);
  const [templateCount, setTemplateCount] = useState<number>(1);
  const [confirmModalOpen, setConfirmModalOpen] = useState<boolean>(false);
  const [progress, setProgress] = useState<number>(0);
  const [currentTemplateIndex, setCurrentTemplateIndex] = useState<number>(0);

  // État local pour les messages changeants
  const [currentMessage, setCurrentMessage] = useState(generationPhaseMessages[0][0]);
  const [generationPhase, setGenerationPhase] = useState(0);

  // Ajouter des états supplémentaires pour le suivi de la génération
  const [templateProgress, setTemplateProgress] = useState<number[]>([]);
  const [isPolling, setIsPolling] = useState<boolean>(false);
  const [originalCount, setOriginalCount] = useState<number>(0);

  const [limitError, setLimitError] = useState<string | null>(null);

  // Protection contre les appels multiples
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [lastGenerationTime, setLastGenerationTime] = useState<number>(0);

  // Réinitialiser l'état de génération quand la modale se ferme
  React.useEffect(() => {
    if (!open) {
      setIsGenerating(false);
      setIsLoading(false);
      setError(null);
      setTemplates([]);
      setLimitError(null);
    }
  }, [open]);

  // Fonction pour ouvrir la modale de confirmation
  const handleOpenConfirmModal = () => {
    if (creditsError) {
      notify('Erreur lors de la récupération des crédits IA', 'error');
      return;
    }
    if (limitError) {
      notify(limitError, 'error');
      return;
    }
    setConfirmModalOpen(true);
  };

  // Fonction pour fermer la modale de confirmation
  const handleCloseConfirmModal = () => {
    setConfirmModalOpen(false);
  };

  // Fonction pour interroger l'API de statut
  const fetchGenerationStatus = async (): Promise<{
    status: string;
    progress: number[];
    templates?: CardTemplateData[];
  }> => {
    try {
      // Utiliser le service pour récupérer le statut
      const response = await cardEditorService.getGenerationStatus();
      return response;
    } catch (error) {
      logger.error('fetchGenerationStatus: Erreur lors de la vérification du statut:', error);
      return {
        status: 'error',
        progress: []
      };
    }
  };

  // Fonction modifiée pour la génération de templates avec polling réel
  const generateTemplates = async () => {
    // Protection contre les appels multiples
    const currentTime = Date.now();
    if (isGenerating || (currentTime - lastGenerationTime < 5000)) {
      logger.warn('generateTemplates: Tentative de génération multiple bloquée', {
        isGenerating,
        timeSinceLastGeneration: currentTime - lastGenerationTime
      });
      notify('Une génération est déjà en cours. Veuillez patienter.', 'warning');
      return;
    }

    logger.info('generateTemplates: Début du processus', { templateType, templateCount });
    setIsGenerating(true);
    setLastGenerationTime(currentTime);
    setIsLoading(true);
    setError(null);
    setTemplates([]);
    setProgress(0);
    setCurrentTemplateIndex(0);
    setTemplateProgress(Array(templateCount).fill(0));
    setOriginalCount(templateCount);
    setIsPolling(false);
    setGenerationPhase(0);
    setCurrentMessage(generationPhaseMessages[0][0]);
    setConfirmModalOpen(false);

    logger.info('generateTemplates: Initialisation des états.');

    let messageInterval: NodeJS.Timeout | null = null;
    let messageIndex = 0;

    // Fonction pour mettre à jour la phase et le message selon la progression globale calculée
    const updatePhaseAndMessage = (progress: number, currentIdx: number) => {
      let phase = 0;
      if (progress >= 80) phase = 2;
      else if (progress >= 20) phase = 1;
      setGenerationPhase(phase);
      setCurrentMessage(`Génération du template ${currentIdx + 1}/${templateCount} · ${generationPhaseMessages[phase][messageIndex % generationPhaseMessages[phase].length]}`);
    };

    // Démarrer le polling IMMÉDIATEMENT
    logger.info('generateTemplates: Démarrage du polling');
    let pollingInterval: NodeJS.Timeout | null = null;
    let timeoutId: NodeJS.Timeout | null = null;
    cardEditorService.generateAiTemplates(templateType, undefined, templateCount)
      .then(result => {
        // Si erreur de crédits, on arrête tout
        if (result.creditsRequired) {
          logger.warn('generateTemplates: Crédits insuffisants détectés après l\'appel initial', { required: result.creditsRequired });
          if (messageInterval) clearInterval(messageInterval);
          setIsLoading(false);
          setIsGenerating(false);
          setIsPolling(false);
          notify(`Vous n'avez pas assez de crédits IA. Cette action nécessite ${result.creditsRequired} crédits. Veuillez en acheter dans le menu Intelligence Artificielle.`, 'error');
          navigate('/dashboard/ai-credits');
          onClose();
          return;
        }
        // Si limite atteinte, on affiche le message d'erreur et on bloque
        if (result.limitReached) {
          setIsLoading(false);
          setIsGenerating(false);
          setIsPolling(false);
          setLimitError(result.limit !== undefined ?
            `Vous avez atteint la limite de ${result.limit} templates pour votre abonnement. Supprimez un template ou passez à une offre supérieure.` :
            `Vous avez atteint la limite de templates pour votre abonnement.`
          );
          return;
        }
        logger.info('generateTemplates: Appel de génération initial réussi');
        });

    // Démarrer le polling IMMÉDIATEMENT
    pollingInterval = setInterval(async () => {
      try {
        logger.info('generateTemplates: Début du cycle de polling');
        const statusResponse = await fetchGenerationStatus();
        // logger.info('generateTemplates: Réponse du polling', statusResponse);

        if (statusResponse.progress && statusResponse.progress.length > 0) {
          // logger.info('generateTemplates: Données de progression reçues', { progress: statusResponse.progress });
          // logger.info('generateTemplates: Progression détaillée reçue:', statusResponse.progress);
          setTemplateProgress(statusResponse.progress);
          // Progression globale = moyenne réelle de *tous* les templates
          const totalProgress = statusResponse.progress.reduce((sum, current) => sum + current, 0);
          const avgProgress = totalProgress / statusResponse.progress.length;

          // logger.info('generateTemplates: Progression moyenne calculée (tous templates)', { avgProgress, totalProgress, count: statusResponse.progress.length });

          // Déterminer le template en cours (le premier non terminé)
          const currentIdx = statusResponse.progress.findIndex(p => p < 100);
          let displayCurrentIdx = currentIdx !== -1 ? currentIdx : statusResponse.progress.length - 1;
          // logger.info('generateTemplates: Index du template en cours pour affichage', { currentIdxBackend: currentIdx, displayCurrentIdx });
          setCurrentTemplateIndex(displayCurrentIdx);

          // logger.info('generateTemplates: Mise à jour de la barre de progression globale', { newProgress: avgProgress });
          setProgress(avgProgress);

          updatePhaseAndMessage(avgProgress, displayCurrentIdx);
        }

        if (statusResponse.status === 'completed' || (statusResponse.progress && statusResponse.progress.every(p => p >= 100))) {
          logger.info('generateTemplates: Statut du backend: completed ou tous les templates >= 100%');
          setProgress(100);
          setGenerationPhase(2);
          setCurrentMessage("Templates prêts à être utilisés!");
          if (pollingInterval) clearInterval(pollingInterval);
          if (timeoutId) clearTimeout(timeoutId);
          if (messageInterval) clearInterval(messageInterval);
          setIsPolling(false); // Arrêter le polling
          // logger.info('generateTemplates: Polling arrêté');

          setTimeout(() => {
            if (statusResponse.templates && statusResponse.templates.length > 0) {
              logger.info('generateTemplates: Affichage des templates générés', { count: statusResponse.templates.length });
              setTemplates(statusResponse.templates);
              setIsLoading(false);
              setIsGenerating(false);
        } else {
              logger.warn('generateTemplates: La génération est terminée mais aucun template n\'a été reçu');
              setError("Aucun template généré");
              setIsLoading(false);
              setIsGenerating(false);
        }
          }, 2000);
        }
      } catch (error) {
        logger.error('generateTemplates: Erreur lors du cycle de polling', error);
        if (pollingInterval) clearInterval(pollingInterval);
        if (timeoutId) clearTimeout(timeoutId);
        if (messageInterval) clearInterval(messageInterval);
        setIsPolling(false); // Arrêter le polling
        logger.error('generateTemplates: Polling arrêté en raison d\'une erreur');
        setError('Erreur lors de la génération des templates.');
        setIsLoading(false);
        setIsGenerating(false);
      }
    }, 3000);

    // Timeout global
    const timeoutDuration = Math.max(templateCount * 30000, 60000);
    // logger.info('generateTemplates: Démarrage du timeout global', { duration: timeoutDuration });
    timeoutId = setTimeout(() => {
      logger.warn('generateTemplates: Timeout global atteint');
      if (pollingInterval) clearInterval(pollingInterval);
      if (messageInterval) clearInterval(messageInterval);
      setIsPolling(false); // Arrêter le polling
      logger.warn('generateTemplates: Polling arrêté en raison du timeout');
      setError('La génération a pris trop de temps. Veuillez réessayer.');
      setIsLoading(false);
      setIsGenerating(false);
    }, timeoutDuration);

    // Nettoyage
    return () => {
      // logger.info('generateTemplates: Fonction de nettoyage appelée');
      if (pollingInterval) clearInterval(pollingInterval);
      if (timeoutId) clearTimeout(timeoutId);
      if (messageInterval) clearInterval(messageInterval);
    };
  };

  // Fonction pour sélectionner un template
  const handleSelectTemplate = (template: CardTemplateData) => {
    // On suppose que chaque template généré possède un champ 'id' (issu de la BDD)
    const templateId = template.id;
    if (templateId) {
      // Rediriger vers la page d'édition du template existant
      navigate(`/dashboard/card-editor/${templateId}`);
      onClose();
    } else {
      // Si jamais il n'y a pas d'id (cas improbable), fallback sur l'ancien comportement
      onTemplateSelect(template);
      onClose();
    }
  };

  return (
    <>
      <AnimatePresence>
        {open && (
          <ModalPortal>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-40 overflow-y-auto py-4"
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                transition={{ type: "spring", duration: 0.5 }}
                className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl m-4 overflow-hidden max-h-[90vh] flex flex-col"
              >
                {/* Wrapper pour rendre tout le contenu défilant */}
                <div className="overflow-y-auto flex-grow flex flex-col">
                  {/* En-tête avec dégradé amélioré */}
                  <div className="flex items-center justify-between bg-gradient-to-r from-[#FFF8F3] via-[#FFE4BA20] to-white p-5 border-b border-[#FFE4BA] z-10">
                    <div className="flex items-center space-x-3">
                      <div className="bg-[#FF6B2C] bg-opacity-10 p-2 rounded-full">
                        <AutoAwesome sx={{ color: '#FF6B2C' }} />
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-800 flex items-center">
                          Génération IA de {templateType === 'business_card' || templateType === 'business_card_landscape' ? 'carte de visite' : 'flyer'}
                          <Tooltip title="Génération via intelligence artificielle">
                            <Info className="ml-2 h-4 w-4 text-gray-400" />
                          </Tooltip>
                        </h3>
                        <p className="text-sm text-gray-500">
                          Créez des designs uniques avec notre IA
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={onClose}
                      className="text-gray-500 hover:text-gray-700 transition-colors p-2 hover:bg-gray-100 rounded-full"
                    >
            <Close />
                    </button>
                  </div>

                  {/* Contenu principal */}
                  <div className="p-6 flex-grow">
          {(!isLoading && templates.length === 0 && !error) && (
                      <div className="text-center py-8">
                        <div className="max-w-md mx-auto mb-8 bg-[#FFF8F3] p-6 rounded-2xl border border-[#FFE4BA]">
                          <div className="flex justify-center mb-4">
                            <Zap className="h-10 w-10 text-[#FF6B2C]" />
                          </div>
                          <h4 className="text-xl font-medium mb-6 text-gray-800">
                            Générez vos templates professionnels
                          </h4>

                          <div className="mb-6">
                            <p className="text-sm font-medium text-gray-700 mb-2">Nombre de templates à générer:</p>
                            <FormControl fullWidth className="mb-4">
                              <Select
                                value={templateCount}
                                onChange={(e) => setTemplateCount(Number(e.target.value))}
                                className="bg-white"
                                sx={{
                                  '.MuiOutlinedInput-notchedOutline': {
                                    borderColor: 'rgba(255, 107, 44, 0.5)',
                                  },
                                  '&:hover .MuiOutlinedInput-notchedOutline': {
                                    borderColor: '#FF6B2C',
                                  },
                                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                    borderColor: '#FF6B2C',
                                  },
                                  borderRadius: '12px',
                                }}
                              >
                                <MenuItem value={1}>1 template</MenuItem>
                                <MenuItem value={2}>2 templates</MenuItem>
                                <MenuItem value={3}>3 templates</MenuItem>
                                <MenuItem value={4}>4 templates</MenuItem>
                              </Select>
                            </FormControl>
                          </div>

                          <div className="bg-white p-4 rounded-xl mb-6 flex flex-col space-y-3">
                            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-1 sm:space-y-0">
                              <p className="text-sm font-medium text-gray-700">Coût par template:</p>
                              <Chip
                                label="5 crédits"
                                size="small"
                                color="primary"
                                sx={{
                                  backgroundColor: '#FF6B2C20',
                                  color: '#FF6B2C',
                                  fontWeight: 500
                                }}
                              />
                            </div>
                            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-1 sm:space-y-0">
                              <p className="text-sm font-medium text-gray-700">Possibilité d'images:</p>
                              <Tooltip title="Chaque template peut inclure aléatoirement une image et/ou un fond d'image sans coût supplémentaire">
                                <Chip
                                  label="Incluses gratuitement"
                                  size="small"
                                  color="primary"
                                  sx={{
                                    backgroundColor: '#FF6B2C20',
                                    color: '#FF6B2C',
                                    fontWeight: 500
                                  }}
                                />
                              </Tooltip>
                            </div>
                            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-1 sm:space-y-0">
                              <p className="text-sm font-medium text-gray-700">Coût total:</p>
                              <Chip
                                label={`${templateCount * 5} crédits`}
                                size="small"
                                color="primary"
                                sx={{
                                  backgroundColor: '#FF6B2C',
                                  color: 'white',
                                  fontWeight: 600
                                }}
                              />
                            </div>

                            <Divider className="my-3" />

                            {creditsLoading ? (
                              <div className="flex items-center justify-center mt-1">
                                <CircularProgress size={16} sx={{ color: '#FF6B2C', mr: 1 }} />
                                <p className="text-sm text-gray-600">Chargement des crédits...</p>
                              </div>
                            ) : creditsError ? (
                              <p className="text-red-500 text-sm mt-1">
                                Erreur lors de la récupération des crédits IA
                              </p>
                            ) : (
                              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-1 sm:space-y-0">
                                <p className="text-sm font-medium text-gray-700">Vos crédits:</p>
                                <p className={`text-sm font-semibold ${credits < templateCount * 5 ? 'text-red-500' : 'text-green-600'}`}>
                                  {credits} {credits > 1 ? 'crédits' : 'crédit'}
                                </p>
                              </div>
                            )}
                          </div>

                          {limitError && (
                            <div className="mt-4 text-red-500 text-sm flex items-center justify-center">
                              <AlertTriangle className="h-4 w-4 mr-1" />
                              {limitError}
                            </div>
                          )}

                          <button
                            onClick={handleOpenConfirmModal}
                            disabled={creditsLoading || !!creditsError || credits < templateCount * 5 || !!limitError || isGenerating || (Date.now() - lastGenerationTime < 5000)}
                            className="w-full px-6 py-3 bg-gradient-to-r from-[#FF6B2C] to-[#FF7A35] text-white rounded-xl hover:from-[#FF7A35] hover:to-[#FF965E] transition-all shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:shadow-none flex items-center justify-center font-medium"
                          >
                            <Sparkles className="mr-2 h-5 w-5" />
                          {isGenerating ? 'Génération en cours...' : 'Lancer la génération IA'}
                          </button>

                          {credits < templateCount * 5 && (
                            <div className="mt-4 text-red-500 text-sm flex items-center">
                              <AlertTriangle className="h-4 w-4 mr-1" />
                              Crédits insuffisants pour cette génération
                            </div>
                          )}

                          {(isGenerating || (Date.now() - lastGenerationTime < 5000)) && (
                            <div className="mt-4 text-orange-500 text-sm flex items-center">
                              <Info className="h-4 w-4 mr-1" />
                              {isGenerating ? 'Génération en cours...' : 'Veuillez patienter quelques secondes avant de relancer une génération'}
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {isLoading && (
                      <div className="flex flex-col items-center justify-center py-16">
                        <div className="relative mb-6">
                          <div className="absolute inset-0 flex items-center justify-center">
                            <CircularProgress
                              variant="determinate"
                              value={100}
                              sx={{ color: 'rgba(255, 107, 44, 0.15)' }}
                              size={80}
                              thickness={4}
                            />
                          </div>
                          <CircularProgress
                            variant="determinate"
                            value={progress}
                            sx={{ color: '#FF6B2C' }}
                            size={80}
                            thickness={4}
                          />
                          <div className="absolute inset-0 flex items-center justify-center">
                            <span className="text-xl font-medium text-gray-800">{`${Math.round(progress)}%`}</span>
                          </div>
                        </div>

                        <div className="w-full max-w-md mb-6">
                          <h4 className="text-xl font-medium mb-3 text-center text-gray-800">Création de vos designs</h4>

                          {/* Message animé changeant */}
                          <motion.div
                            className="text-center mb-5"
                            initial={{ opacity: 0, y: 5 }}
                            animate={{ opacity: 1, y: 0 }}
                            key={currentMessage} // Réinitialiser l'animation à chaque changement
                            transition={{ duration: 0.5 }}
                          >
                            <p className="text-gray-600 font-medium">{currentMessage}</p>
                          </motion.div>

                          {/* Animation pour chaque template */}
                          <div className="space-y-3">
                            {templateProgress.map((progress, idx) => (
                              <motion.div
                                key={idx}
                                className="bg-white rounded-lg p-4 border relative overflow-hidden"
                                initial={{ opacity: 0.7, x: -10 }}
                                animate={{
                                  opacity: progress > 0 ? 1 : 0.6,
                                  x: 0,
                                  borderColor: progress >= 100
                                    ? '#4ADE80'
                                    : idx === currentTemplateIndex
                                      ? '#FF6B2C'
                                      : '#e5e7eb'
                                }}
                                transition={{ duration: 0.5 }}
                              >
                                <div className="flex items-center justify-between relative z-10">
                                  <div className="flex items-center">
                                    <span className="font-semibold text-gray-800">Template {idx+1}</span>
                                    {progress >= 100 && (
                                      <motion.span
                                        initial={{ scale: 0, opacity: 0 }}
                                        animate={{ scale: 1, opacity: 1 }}
                                        className="ml-2 px-2 py-0.5 bg-green-100 text-green-700 text-xs rounded-full font-medium flex items-center"
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 0 1 0 1.414l-8 8a1 1 0 0 1-1.414 0l-4-4a1 1 0 1 1 1.414-1.414L8 12.586l7.293-7.293a1 1 0 0 1 1.414 0z" clipRule="evenodd" />
                                        </svg>
                                        Terminé
                                      </motion.span>
                                    )}
                                  </div>

                                  {progress >= 100 ? (
                                    <motion.div
                                      initial={{ rotate: 0 }}
                                      animate={{ rotate: 360 }}
                                      transition={{ duration: 0.5 }}
                                    >
                                      <Sparkles className="h-5 w-5 text-green-500" />
                                    </motion.div>
                                  ) : idx === currentTemplateIndex ? (
                                    <motion.div
                                      animate={{
                                        scale: [1, 1.2, 1],
                                        opacity: [0.7, 1, 0.7]
                                      }}
                                      transition={{
                                        repeat: Infinity,
                                        duration: 2
                                      }}
                                    >
                                      <CircularProgress size={22} sx={{ color: '#FF6B2C' }} />
                                    </motion.div>
                                  ) : (
                                    <div className="h-5 w-5 rounded-full border-2 border-gray-300"></div>
                                  )}
                                </div>

                                {/* Fond animé pour le template en cours */}
                                {idx === currentTemplateIndex && progress < 100 && (
                                  <motion.div
                                    className="absolute inset-0 bg-gradient-to-r from-[#FFF8F3] to-transparent"
                                    animate={{
                                      opacity: [0.3, 0.5, 0.3],
                                      background: ['linear-gradient(to right, rgba(255, 248, 243, 0.3), transparent)',
                                                  'linear-gradient(to right, rgba(255, 228, 186, 0.3), transparent)',
                                                  'linear-gradient(to right, rgba(255, 248, 243, 0.3), transparent)']
                                    }}
                                    transition={{
                                      repeat: Infinity,
                                      duration: 3
                                    }}
                                  />
                                )}

                                {/* Barre de progression individuelle animée */}
                                {progress > 0 && progress < 100 ? (
                                  <motion.div
                                    className="h-1.5 bg-gradient-to-r from-[#FF6B2C] to-[#FF965E] absolute bottom-0 left-0 rounded-full"
                                    initial={{ width: '0%' }}
                                    animate={{
                                      width: `${progress}%`,
                                      opacity: [0.8, 1, 0.8]
                                    }}
                                    transition={{
                                      duration: 0.8,
                                      opacity: {
                                        repeat: Infinity,
                                        duration: 2
                                      }
                                    }}
                                  />
                                ) : progress >= 100 ? (
                                  <motion.div
                                    className="h-1.5 bg-green-500 absolute bottom-0 left-0 right-0 rounded-full"
                                    initial={{ width: '0%' }}
                                    animate={{ width: '100%' }}
                                    transition={{ duration: 0.5 }}
                                  />
                                ) : null}
                              </motion.div>
                            ))}
                          </div>
                        </div>

                        {/* Temps estimé restant */}
                        <div className="text-center text-gray-500 text-sm mt-2">
                          <p>Temps estimé restant: environ {Math.ceil((templateCount - (currentTemplateIndex)) * 15)} secondes</p>
                          <p className="mt-1 text-gray-400 text-xs">La génération peut prendre jusqu'à 25 secondes par template</p>
                        </div>
                      </div>
                    )}

                    {error && (
                      <div className="text-center py-12">
                        <div className="bg-red-50 p-6 rounded-2xl mb-6 max-w-md mx-auto">
                          <AlertTriangle className="h-10 w-10 text-red-500 mx-auto mb-4" />
                          <p className="text-red-600 mb-6 font-medium">{error}</p>
                          <button
                            onClick={handleOpenConfirmModal}
                            className="px-6 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors shadow-md"
                          >
                            Réessayer
                          </button>
                        </div>
                      </div>
                    )}

                    {templates.length > 0 && (
                      <>
                        <h4 className="text-xl font-medium mb-6 text-gray-800">Sélectionnez un template pour l'importer dans le canva</h4>
                        <div className="grid grid-cols-1 gap-6">
                          {templates.map((template, index) => (
                            <motion.div
                              key={index}
                              custom={index}
                              variants={cardVariants}
                              initial="hidden"
                              animate="visible"
                              whileHover="hover"
                              whileTap="tap"
                              className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 transition-all duration-300"
                            >
                              <div className="p-4">
                                <h5 className="font-semibold text-gray-800 mb-3 flex items-center justify-between">
                                  <span className="flex items-center">
                                    Proposition {index + 1} <Sparkles className="h-4 w-4 ml-2 text-[#FF6B2C]" />
                                  </span>
                                  {(template.background_image || template.elements.some(el => el.type === 'image')) && (
                                    <Tooltip title="Ce template inclut des images générées par IA">
                                      <Chip
                                        label="Images IA"
                                        size="small"
                                        color="primary"
                                        sx={{
                                          backgroundColor: '#FF6B2C20',
                                          color: '#FF6B2C',
                                          fontWeight: 500,
                                          fontSize: '0.7rem',
                                          height: '20px'
                                        }}
                                      />
                                    </Tooltip>
                                  )}
                                </h5>
                    <Divider sx={{ mb: 2 }} />
                                <div className="flex flex-col md:flex-row">
                                  <div className="bg-[#f7f7f7] rounded-lg p-3 mb-4 md:mb-0 md:mr-4 flex items-center justify-center h-[250px] w-full md:w-1/2 overflow-hidden">
                                <CardEditorCanvas
                                  templateData={template}
                                  onElementSelect={() => {}}
                                  onElementUpdate={() => {}}
                                  onElementAdd={() => {}}
                                  onElementDelete={() => {}}
                                  selectedElement={null}
                                  isEditable={false}
                                  zoom={0.7}
                                  showGrid={false}
                                  showGuides={false}
                                  snapEnabled={false}
                                  showProfessionalFooter={true}
                                  showWatermark={false}
                                />
                                </div>
                                <div className="flex flex-col justify-between w-full md:w-1/2">
                                  <div className="mb-4">
                                    <h6 className="font-medium text-gray-700 mb-2">Caractéristiques du design:</h6>
                                    <ul className="text-sm text-gray-600 space-y-1">
                                      <li className="flex items-center">
                                        <span className="h-2 w-2 bg-[#FF6B2C] rounded-full mr-2"></span>
                                        {template.elements.filter(el => el.type === 'text').length} éléments de texte
                                      </li>
                                      <li className="flex items-center">
                                        <span className="h-2 w-2 bg-[#FF6B2C] rounded-full mr-2"></span>
                                        {template.elements.filter(el => el.type === 'shape').length} formes
                                      </li>
                                      <li className="flex items-center">
                                        <span className="h-2 w-2 bg-[#FF6B2C] rounded-full mr-2"></span>
                                        {template.elements.filter(el => el.type === 'image').length} images
                                      </li>
                                      {template.elements.some(el => el.type === 'qrcode') && (
                                        <li className="flex items-center">
                                          <span className="h-2 w-2 bg-[#FF6B2C] rounded-full mr-2"></span>
                                          Inclut un QR code
                                        </li>
                                      )}
                                      {template.background_image && (
                                        <li className="flex items-center">
                                          <span className="h-2 w-2 bg-[#FF6B2C] rounded-full mr-2"></span>
                                          Fond d'image personnalisé
                                        </li>
                                      )}
                                    </ul>
                                  </div>
                                    <button
                                    onClick={() => handleSelectTemplate(template)}
                                      className="w-full px-4 py-3 bg-gradient-to-r from-[#FF6B2C] to-[#FF7A35] text-white rounded-lg hover:from-[#FF7A35] hover:to-[#FF965E] transition-all shadow-md hover:shadow-lg font-medium tracking-wide flex items-center justify-center"
                                    >
                                      <AutoAwesome sx={{ fontSize: 18, marginRight: 1 }} />
                                      Ouvrir ce modèle
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </motion.div>
                          ))}
                        </div>
                      </>
                    )}
                  </div>

                  {/* Pied de page avec actions */}
                  <div className="border-t border-gray-200 p-4 bg-white z-10 flex justify-between space-x-3">
                    <button
                      onClick={onClose}
                      className="px-4 py-2 border border-[#FF6B2C] text-[#FF6B2C] rounded-lg hover:bg-[#FFF8F3] transition-colors ml-auto"
                    >
                    Annuler
                    </button>
                      {!isLoading && templates.length > 0 && !isGenerating && (
                      <button
                        onClick={handleOpenConfirmModal}
                        disabled={isGenerating || (Date.now() - lastGenerationTime < 5000)}
                        className="px-4 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <Sparkles className="mr-2 h-4 w-4" />
                        Générer d'autres propositions
                      </button>
                    )}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </ModalPortal>
        )}
      </AnimatePresence>

      {/* Modale de consentement IA */}
      <AiConsentModal
        isOpen={aiConsentModalOpen}
        onClose={() => setAiConsentModalOpen(false)}
        onAccept={() => {
          setAiConsentModalOpen(false);
          generateTemplates();
        }}
      />

      {/* Modale de confirmation */}
      <AnimatePresence>
        {confirmModalOpen && (
          <ModalPortal>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                transition={{ type: "spring", duration: 0.5 }}
                className="bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-hidden transition-all duration-300 scale-100 m-4"
              >
                {/* En-tête de confirmation */}
                <div className="flex items-center justify-between bg-gradient-to-r from-[#FFF8F3] via-[#FFE4BA20] to-white p-4 border-b border-[#FFE4BA]">
                  <div className="flex items-center space-x-2">
                    <div className="bg-[#FF6B2C] bg-opacity-10 p-1.5 rounded-full">
                      <Sparkles className="text-[#FF6B2C] h-5 w-5" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-800">Confirmation</h3>
                  </div>
                  <button
                    onClick={handleCloseConfirmModal}
                    className="text-gray-500 hover:text-gray-700 transition-colors p-1.5 hover:bg-gray-100 rounded-full"
                  >
                    <Close />
                  </button>
                </div>

                <div className="p-5 overflow-y-auto max-h-[calc(90vh-80px)]">
                  <div className="bg-[#FFF8F3] p-4 rounded-xl mb-5">
                    <div className="flex items-start space-x-3">
                      <Info className="text-[#FF6B2C] h-5 w-5 mt-0.5 flex-shrink-0" />
                      <div>
                        {creditsLoading ? (
                          <div className="flex items-center">
                            <CircularProgress size={16} sx={{ color: '#FF6B2C', mr: 1 }} />
                            <p className="text-sm text-gray-600">Chargement des crédits...</p>
                          </div>
                        ) : creditsError ? (
                          <p className="text-red-500 text-sm">Erreur lors de la récupération des crédits IA</p>
                        ) : (
                          <>
                            <p className="text-gray-700">
                              Cette action utilisera <span className="font-semibold text-[#FF6B2C]">{templateCount * 5} crédits IA</span> pour générer {templateCount} template{templateCount > 1 ? 's' : ''}.
                            </p>
                            <p className="text-sm text-gray-600 mt-1">
                              Les images et fonds d'image sont inclus sans coût supplémentaire.
                            </p>
                            <div className="mt-3 p-2 bg-white rounded-lg">
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">Crédits actuels:</span>
                                <span className="font-medium">{credits}</span>
                              </div>
                              <div className="flex justify-between items-center mt-1">
                                <span className="text-sm text-gray-600">Coût:</span>
                                <span className="font-medium text-red-500">-{templateCount * 5}</span>
                              </div>
                              <Divider sx={{ my: 1 }} />
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium text-gray-700">Solde après génération:</span>
                                <span className={`font-semibold ${credits - templateCount * 5 >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                  {credits - templateCount * 5}
                                </span>
                              </div>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3">
                    <button
                      onClick={handleCloseConfirmModal}
                      className="w-full sm:w-auto px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors text-center hover:bg-gray-100 rounded-lg"
                    >
                      Annuler
                    </button>
                    <button
                      onClick={generateTemplates}
                      disabled={creditsLoading || !!creditsError || credits < templateCount * 5 || isGenerating || (Date.now() - lastGenerationTime < 5000)}
                      className="w-full sm:w-auto px-4 py-2 bg-gradient-to-r from-[#FF6B2C] to-[#FF7A35] text-white rounded-lg hover:from-[#FF7A35] hover:to-[#FF965E] transition-all shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:shadow-none flex items-center justify-center"
                    >
                      <Sparkles className="mr-2 h-5 w-5" />
                      {isGenerating ? 'Génération en cours...' : 'Générer maintenant'}
                    </button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </ModalPortal>
        )}
      </AnimatePresence>
    </>
  );
};

export default AiTemplateGenerationModal;
