import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { API_CONFIG } from '../config/api';
import { getCommonHeaders } from '../utils/headers';
import { notify } from '../components/Notification';
import logger from '@/utils/logger';
import { fetchCsrfToken } from '../services/csrf';

// Constantes
const JOBI_REWARD_REVIEW = 1; // Récompense en Jobi pour un avis

export interface Review {
  id: string;
  target_user_id: string;
  mission_id: string;
  note: number;
  commentaire: string;
  qualites: string[];
  defauts: string[];
  reponse?: string;
  reponse_date?: string;
  created_at: string;
  updated_at: string;
  modified_at?: string;
  author_id: string;
  is_modified: boolean;
  mission_titre?: string;
  mission_categorie?: string;
  mission_sous_categorie?: string;
  photos?: string[];
  author?: {
    email: string;
    profil?: {
      nom?: string;
      prenom?: string;
      photo_url?: string;
    }[];
  };
  rating: number;
  comment: string;
  client: {
    firstName: string;
    lastName: string;
    photo_url?: string;
  };
}

interface UseReviewsProps {
  userId: string;
}

interface ReviewsStats {
  total_reviews: number;
  rating: number;
  completion_rate: number;
}

export const useReviews = ({ userId }: UseReviewsProps) => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [stats, setStats] = useState<ReviewsStats>({
    total_reviews: 0,
    rating: 0,
    completion_rate: 0
  });
  const [qualitesStats, setQualitesStats] = useState<Array<{qualite: string, count: number}>>([]);
  const [defautsStats, setDefautsStats] = useState<Array<{defaut: string, count: number}>>([]);
  const reviewsPerPage = 5;
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);

  const fetchReviews = useCallback(async (page: number = 1) => {
    if (!userId) {
      logger.info('userId est requis pour fetchReviews');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await axios.get(
        `${API_CONFIG.baseURL}/api/reviews/user/${userId}?page=${page}&limit=${reviewsPerPage}`,
        { headers: await getCommonHeaders() }
      );

      if (response.data.success) {
        const formattedReviews = response.data.reviews.map((review: any) => ({
          ...review,
          rating: review.note,
          comment: review.commentaire,
          client: review.author?.profil?.[0] || null,
          created_at: review.created_at,
          modified_at: review.modified_at,
          is_modified: review.is_modified
        }));

        if (page === 1) {
          setReviews(formattedReviews);
        } else {
          setReviews(prev => [...prev, ...formattedReviews]);
        }

        setHasMore(response.data.pagination.has_more);
        setCurrentPage(page);
        setStats(response.data.stats);
      }
    } catch (err) {
      setError('Erreur lors du chargement des avis');
      logger.info('Erreur lors du chargement des avis:', err);
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    if (userId) {
      fetchReviews(1);
    }
  }, [userId, fetchReviews]);

  const loadMore = () => {
    if (!loading && hasMore) {
      fetchReviews(currentPage + 1);
    }
  };

  const addReview = async (data: {
    mission_id: string;
    note: number;
    commentaire: string;
    qualites: string[];
  }) => {
    try {
      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/reviews`,
        data,
        {
          headers: {
            ...(await getCommonHeaders()),
            'Content-Type': 'application/json',
            'X-CSRF-Token': await fetchCsrfToken()
          },
          withCredentials: true
        }
      );

      if (response.data.success) {
        notify(`Avis déposé avec succès ! Vous avez gagné ${JOBI_REWARD_REVIEW} jobi.`, 'success');
        await fetchReviews(1);
        return response.data.review; // Retourner l'objet de l'avis avec son ID
      }
      return null;
    } catch (err: any) {
      notify(err.response.data.message || 'Erreur lors de l\'envoi de l\'avis', 'error');
      logger.info('Erreur lors de l\'envoi de l\'avis:', err);
      return null;
    }
  };

  const getReview = useCallback(async (reviewId: string) => {
    try {
      setLoading(true);
      setError(null);

      const headers = await getCommonHeaders(true);
      const response = await axios.get(
        `${API_CONFIG.baseURL}/api/reviews/${reviewId}`,
        { 
          headers,
          withCredentials: true
        }
      );

      if (response.data.success) {
        const formattedReview = {
          ...response.data.review,
          rating: response.data.review.note,
          comment: response.data.review.commentaire,
          client: response.data.review.author?.profil?.[0] || null,
          created_at: response.data.review.created_at
        };
        setSelectedReview(formattedReview);
        return formattedReview;
      }
      notify('Erreur lors de la récupération de l\'avis', 'error');
      return null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la récupération de l\'avis';
      setError(errorMessage);
      notify(errorMessage, 'error');
      logger.info('Erreur lors de la récupération de l\'avis:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateReview = async (reviewId: string, data: {
    note: number;
    commentaire: string;
    qualites: string[];
    mission_id: string;
  }) => {
    try {
      const response = await axios.put(
        `${API_CONFIG.baseURL}/api/reviews/${reviewId}`,
        data,
        { 
          headers: {
            ...(await getCommonHeaders()),
            'Content-Type': 'application/json'
          },
          withCredentials: true
        }
      );

      if (response.data.success) {
        notify('Avis modifié avec succès', 'success');
        await fetchReviews(1);
        setSelectedReview(null);
        return true;
      }
      return false;
    } catch (err) {
      notify('Erreur lors de la modification de l\'avis', 'error');
      logger.info('Erreur lors de la modification de l\'avis:', err);
      return false;
    }
  };

  const deleteReview = async (reviewId: string) => {
    try {
      const response = await axios.delete(
        `${API_CONFIG.baseURL}/api/reviews/${reviewId}`,
        { 
          headers: await getCommonHeaders(),
          withCredentials: true
        }
      );

      if (response.data.success) {
        notify('Avis supprimé avec succès', 'success');
        await fetchReviews(1);
        return true;
      }
      return false;
    } catch (err) {
      notify('Erreur lors de la suppression de l\'avis', 'error');
      logger.info('Erreur lors de la suppression de l\'avis:', err);
      return false;
    }
  };

  const replyToReview = async (reviewId: string, reponse: string) => {
    try {
      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/reviews/${reviewId}/reply`,
        { reponse },
        { 
          headers: {
            ...(await getCommonHeaders()),
            'Content-Type': 'application/json',
            'X-CSRF-Token': await fetchCsrfToken()
          },
          withCredentials: true
        }
      );

      if (response.data.success) {
        notify('Réponse ajoutée avec succès', 'success');
        await fetchReviews(1);
        return true;
      }
      return false;
    } catch (err) {
      notify('Erreur lors de l\'ajout de la réponse', 'error');
      logger.info('Erreur lors de l\'ajout de la réponse:', err);
      return false;
    }
  };

  const updateReplyToReview = async (reviewId: string, reponse: string) => {
    try {
      const response = await axios.put(
        `${API_CONFIG.baseURL}/api/reviews/${reviewId}/reply`,
        { reponse },
        { 
          headers: {
            ...(await getCommonHeaders()),
            'Content-Type': 'application/json'
          },
          withCredentials: true
        }
      );

      if (response.data.success) {
        notify('Réponse modifiée avec succès', 'success');
        await fetchReviews(1);
        return true;
      }
      return false;
    } catch (err) {
      notify('Erreur lors de la modification de la réponse', 'error');
      logger.info('Erreur lors de la modification de la réponse:', err);
      return false;
    }
  };

  const fetchQualitesStats = async () => {
    try {
      const response = await axios.get(`${API_CONFIG.baseURL}/api/reviews/user/${userId}/qualites-stats`);
      if (response.data.success) {
        setQualitesStats(response.data.data);
      }
    } catch (error) {
      logger.info('Erreur lors de la récupération des stats des qualités:', error);
    }
  };

  const fetchDefautsStats = async () => {
    try {
      const defautsCount: Record<string, number> = {};
      
      reviews.forEach(review => {
        if (review.defauts && review.defauts.length > 0) {
          review.defauts.forEach(defaut => {
            const cleanDefaut = decodeURIComponent(defaut.replace(/&(#x27|apos|quot);/g, "'").replace(/&amp;/g, '&'));
            defautsCount[cleanDefaut] = (defautsCount[cleanDefaut] || 0) + 1;
          });
        }
      });
      
      const defautsStatsArray = Object.entries(defautsCount)
        .map(([defaut, count]) => ({ defaut, count }))
        .sort((a, b) => b.count - a.count);
      
      setDefautsStats(defautsStatsArray);
    } catch (error) {
      logger.info('Erreur lors du calcul des stats des défauts:', error);
    }
  };

  const checkReviewExists = async (missionId: string): Promise<boolean> => {
    try {
      const response = await axios.get(`${API_CONFIG.baseURL}/api/reviews/check/${missionId}`, {
        headers: await getCommonHeaders(),
        withCredentials: true
      });
      return response.data.exists;
    } catch (error) {
      logger.error('Erreur lors de la vérification de l\'existence d\'un avis:', error);
      return false;
    }
  };

  useEffect(() => {
    if (userId) {
      fetchQualitesStats();
    }
  }, [userId]);

  useEffect(() => {
    if (reviews.length > 0) {
      fetchDefautsStats();
    }
  }, [reviews]);

  return {
    reviews,
    loading,
    error,
    hasMore,
    loadMore,
    fetchReviews,
    getReview,
    addReview,
    updateReview,
    deleteReview,
    replyToReview,
    updateReplyToReview,
    selectedReview,
    stats,
    qualitesStats,
    defautsStats,
    checkReviewExists
  };
};

export default useReviews; 