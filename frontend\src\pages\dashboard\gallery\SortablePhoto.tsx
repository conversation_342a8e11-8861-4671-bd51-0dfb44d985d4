import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { motion } from 'framer-motion';
import { Edit2, Trash2, GripVertical, Maximize2 } from 'lucide-react';
import { GalleryImage } from './types';

interface SortablePhotoProps {
  photo: GalleryImage;
  onEdit: () => void;
  onDelete: () => void;
  currentIndex: number;
  onClick: () => void;
  isOwnProfil: boolean;
}

export const SortablePhoto: React.FC<SortablePhotoProps> = ({
  photo,
  onEdit,
  onDelete,
  currentIndex,
  onClick,
  isOwnProfil,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: photo.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <motion.div
      ref={setNodeRef}
      style={style}
      // initial={{ opacity: 0, y: 20 }}
      // animate={{ opacity: 1, y: 0 }}
      // transition={{ duration: 0.3, delay: currentIndex * 0.1 }}
      className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 group"
      onClick={onClick}
    >
      <div className="relative">
        <img
          src={photo.photo_url}
          alt={photo.caption || 'Photo de galerie'}
          className="w-full h-[250px] object-cover transition-transform duration-300 group-hover:scale-105 cursor-zoom-in"
        />
        <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 cursor-zoom-in" />
        <div className="absolute bottom-4 left-4 right-4 transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
          <p className="text-white text-sm line-clamp-2">
            {photo.caption || 'Aucune légende'}
          </p>
        </div>
        {isOwnProfil && (
          <div className="absolute top-4 right-4 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <button
              {...attributes}
              {...listeners}
              className="p-2 bg-white rounded-lg hover:bg-[#FF6B2C] hover:text-white transition-colors shadow-md cursor-move group"
              title="Déplacer la photo"
              onClick={(e) => e.stopPropagation()}
            >
              <GripVertical className="h-4 w-4 group-hover:scale-110 transition-transform" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onClick();
              }}
              className="p-2 bg-white rounded-lg hover:bg-[#FF6B2C] hover:text-white transition-colors shadow-md"
              title="Agrandir la photo"
            >
              <Maximize2 className="h-4 w-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onEdit();
              }}
              className="p-2 bg-white rounded-lg hover:bg-[#FF6B2C] hover:text-white transition-colors shadow-md"
              title="Modifier la légende"
            >
              <Edit2 className="h-4 w-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
              className="p-2 bg-white rounded-lg hover:bg-red-500 hover:text-white transition-colors shadow-md"
              title="Supprimer la photo"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
        )}
      </div>
    </motion.div>
  );
}; 