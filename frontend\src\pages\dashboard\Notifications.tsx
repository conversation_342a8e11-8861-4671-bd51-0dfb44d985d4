import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNotifications } from '../../hooks/useNotifications';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { ArchiveIcon, TrashIcon, MailOpenIcon, MailIcon, Search, Filter, CheckSquare, Trash2 } from 'lucide-react';
import { styled } from '@mui/material/styles';
import { Typography, Select, MenuItem, TextField, InputAdornment, Dialog, DialogTitle, DialogContent, DialogActions, Button } from '@mui/material';
import logger from '@/utils/logger';
import DOMPurify from 'dompurify';

const PageTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: '#2D3748', 
  marginBottom: '16px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: '#FF6B2C',
    borderRadius: '2px',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.5rem',
  },
  [theme.breakpoints.between('sm', 'md')]: {
    fontSize: '1.75rem',
  },
}));

const StyledSelect = styled(Select)({
  '& .MuiOutlinedInput-notchedOutline': {
    borderColor: '#E2E8F0',
  },
  '&:hover .MuiOutlinedInput-notchedOutline': {
    borderColor: '#FF6B2C',
  },
  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
    borderColor: '#FF6B2C',
  },
  '& .MuiSelect-select': {
    padding: '8px 14px',
  },
});

const StyledTextField = styled(TextField)({
  '& .MuiOutlinedInput-root': {
    '& fieldset': {
      borderColor: '#E2E8F0',
    },
    '&:hover fieldset': {
      borderColor: '#FF6B2C',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#FF6B2C',
    },
  },
});

// Hook personnalisé pour le debounce
const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

const NotificationsPage: React.FC = () => { 
  const {
    notifications,
    loading,
    error,
    deleteNotification,
    fetchNotifications,
    toggleReadStatus,
    toggleArchiveStatus,
    pagination,
    markAllAsRead,
    deleteAllNotifications,
    archiveAllNotifications
  } = useNotifications();

  const [showArchived, setShowArchived] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchInput, setSearchInput] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [showUnreadOnly, setShowUnreadOnly] = useState(false);
  const [allNotifications, setAllNotifications] = useState<any[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const observerTarget = useRef<HTMLDivElement>(null);

  // États pour les modales
  const [showMarkAllReadModal, setShowMarkAllReadModal] = useState(false);
  const [showDeleteAllModal, setShowDeleteAllModal] = useState(false);
  const [showArchiveAllModal, setShowArchiveAllModal] = useState(false);

  const debouncedSearchTerm = useDebounce(searchInput, 600); // 600 millisecondes de délai afin de ne pas surcharger le serveur lors de la recherche
  const navigate = useNavigate();

  // Gérer l'intersection observer pour le lazy loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoadingMore && currentPage < (pagination?.totalPages || 1)) {
          setCurrentPage((prevPage) => prevPage + 1);
        }
      },
      { threshold: 1.0 }
    );

    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => observer.disconnect();
  }, [hasMore, isLoadingMore, currentPage, pagination?.totalPages]);

  // Charger les notifications avec les filtres
  useEffect(() => {
    let isMounted = true;

    const loadNotifications = async () => {
      if (isLoadingMore) return;
      
      setIsLoadingMore(true);
      try {
        await fetchNotifications(showArchived, {
          page: currentPage,
          type: selectedType === 'all' ? undefined : selectedType,
          search: debouncedSearchTerm,
          unread: showUnreadOnly
        });
      } finally {
        if (isMounted) {
          setIsLoadingMore(false);
        }
      }
    };

    loadNotifications();

    return () => {
      isMounted = false;
    };
  }, [currentPage, showArchived, selectedType, debouncedSearchTerm, showUnreadOnly]);

  // Mettre à jour la liste des notifications
  useEffect(() => {
    if (!notifications) return;

    if (currentPage === 1) {
      setAllNotifications(notifications);
    } else {
      setAllNotifications(prev => {
        const uniqueNotifications = [...prev];
        notifications.forEach(notification => {
          if (!uniqueNotifications.some(n => n.id === notification.id)) {
            uniqueNotifications.push(notification);
          }
        });
        return uniqueNotifications;
      });
    }

    setHasMore(pagination?.totalPages ? currentPage < pagination.totalPages : false);
  }, [notifications, currentPage, pagination?.totalPages]);

  // Reset des filtres et de la pagination
  const resetFilters = useCallback(() => {
    setCurrentPage(1);
    setAllNotifications([]);
    setHasMore(true);
    setIsLoadingMore(false);
  }, []);

  const handleTypeChange = (event: any) => {
    setSelectedType(event.target.value);
    resetFilters();
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(event.target.value);
  };

  const handleDeleteNotification = async (notificationId: string) => {
    try {
      await deleteNotification(notificationId);
      // Mettre à jour la liste des notifications en filtrant la notification supprimée
      setAllNotifications(prev => prev.filter(n => n.id !== notificationId));
      // Rafraîchir les notifications si nécessaire
      await fetchNotifications(showArchived, {
        page: currentPage,
        type: selectedType === 'all' ? undefined : selectedType,
        search: debouncedSearchTerm
      });
    } catch (error) {
      logger.error('Erreur lors de la suppression de la notification:', error);
    }
  };

  const [showEcoBanner, setShowEcoBanner] = useState(true);

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-[200px] text-red-600">
        {error}
      </div>
    );
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'jobi':
        return '💰';
      case 'message':
        return '✉️';
      case 'mission':
        return '🎯';
      case 'mission_comment':
        return '💬';
      case 'profile':
        return '⭐';
      case 'bug_report':
        return '🛠️';
      case 'invoice':
        return '📝';
      case 'subscription':
        return '🔄';
      default:
        return 'ℹ️';
    }
  };

  return (
    <div className="space-y-6 px-2 md:px-0">
      {/* Bandeau d'information écologique */}
      {showEcoBanner && (
        <div className="md:flex bg-[#FFF8F3] border border-[#FF6B2C] rounded-lg p-4 flex items-center gap-3 text-sm text-[#2D3748] relative mb-8">
          <div className="flex-shrink-0 text-xl" role="img" aria-label="eco-friendly">🌱</div>
          <div className="flex-grow">
            <p className="font-medium">Mise à jour écologique</p>
            <p className="text-gray-600">
              Pour réduire notre empreinte carbone, le compteur de notifications est actualisé toutes les 60 secondes.
              Cette approche permet de diminuer la consommation de ressources serveur tout en maintenant une expérience utilisateur fluide.
            </p>
          </div>
          <button
            onClick={() => setShowEcoBanner(false)}
            className="absolute top-2 right-2 p-2 hover:bg-[#FFE4D6] rounded-full transition-colors duration-200"
            aria-label="Fermer le message"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="#FF6B2C"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18" />
              <line x1="6" y1="6" x2="18" y2="18" />
            </svg>
          </button>
        </div>
      )}

      <div className="flex items-center justify-between mb-4">
        <PageTitle variant="h1">
          {showArchived ? 'Notifications archivées' : 'Notifications'}
        </PageTitle>

        <div className="flex items-center gap-2">
          {!showArchived && notifications.length > 0 && (
            <>
              <button
                onClick={() => setShowMarkAllReadModal(true)}
                className="flex items-center gap-2 px-4 py-2 text-sm font-medium bg-[#FFF8F3] text-[#FF6B2C] hover:bg-[#FFE4BA] rounded-lg transition-all duration-200 transform hover:scale-105 shadow-sm hover:shadow-md"
                title="Marquer toutes les notifications comme lues"
              >
                <CheckSquare className="h-4 w-4" />
                <span className="hidden sm:inline">Tout marquer comme lu</span>
              </button>

              <button
                onClick={() => setShowDeleteAllModal(true)}
                className="flex items-center gap-2 px-4 py-2 text-sm font-medium bg-red-50 text-red-600 hover:bg-red-100 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-sm hover:shadow-md"
                title="Supprimer toutes les notifications"
              >
                <Trash2 className="h-4 w-4" />
                <span className="hidden sm:inline">Tout supprimer</span>
              </button>

              <button
                onClick={() => setShowArchiveAllModal(true)}
                className="flex items-center gap-2 px-4 py-2 text-sm font-medium bg-[#FFF8F3] text-[#FF6B2C] hover:bg-[#FFE4BA] rounded-lg transition-all duration-200 transform hover:scale-105 shadow-sm hover:shadow-md"
                title="Archiver toutes les notifications"
              >
                <ArchiveIcon className="h-4 w-4" />
                <span className="hidden sm:inline">Tout archiver</span>
              </button>
            </>
          )}

          <button
            onClick={() => {
              setShowArchived(!showArchived);
              resetFilters();
            }}
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium bg-[#FFF8F3] text-[#FF6B2C] hover:bg-[#FFE4BA] rounded-lg transition-all duration-200 transform hover:scale-105 shadow-sm hover:shadow-md"
          >
            <ArchiveIcon className="h-4 w-4" />
            <span className="hidden sm:inline">
              {showArchived ? 'Voir les notifications actives' : 'Voir les archives'}
            </span>
          </button>
        </div>
      </div>

      {/* Filtres */}
      <div className="flex flex-col md:flex-row gap-4 mb-6 bg-white p-4 rounded-lg shadow-sm">
        <StyledTextField
          size="small"
          placeholder="Rechercher..."
          value={searchInput}
          onChange={handleSearch}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search className="h-5 w-5 text-gray-400" />
              </InputAdornment>
            ),
          }}
          className="flex-1"
        />

        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="relative">
              <input
                type="checkbox"
                id="unreadFilter"
                checked={showUnreadOnly}
                onChange={(e) => {
                  setShowUnreadOnly(e.target.checked);
                  resetFilters();
                }}
                className="absolute opacity-0 w-4 h-4 cursor-pointer"
              />
              <div className={`w-4 h-4 border-2 rounded flex items-center justify-center transition-colors duration-200
                ${showUnreadOnly ? 'bg-[#FF6B2C] border-[#FF6B2C]' : 'bg-white border-[#FF6B2C]'}`}>
                {showUnreadOnly && (
                  <svg className="w-3 h-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
            </div>
            <label htmlFor="unreadFilter" className="text-sm text-gray-600">
              Non lus uniquement
            </label>
          </div>

          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <StyledSelect
              size="small"
              value={selectedType}
              onChange={handleTypeChange}
              className="min-w-[150px]"
            >
              <MenuItem value="all">Tous les types</MenuItem>
              <MenuItem value="bug_report">Rapports</MenuItem>
              <MenuItem value="jobi">Jobi</MenuItem>
              <MenuItem value="message">Message</MenuItem>
              <MenuItem value="mission_comment">Commentaires</MenuItem>
              <MenuItem value="mission">Mission</MenuItem>
              <MenuItem value="subscription">Abonnement</MenuItem>
              <MenuItem value="system">Système</MenuItem>
            </StyledSelect>
          </div>
        </div>
      </div>

      {allNotifications.length === 0 && !loading ? (
        <div className="text-center py-12 text-gray-500 bg-white rounded-lg shadow-sm">
          {showArchived
            ? 'Aucune notification archivée'
            : 'Aucune nouvelle notification'}
        </div>
      ) : (
        <div className="space-y-4">
          {allNotifications.map((notification) => (
            <motion.div
              key={notification.id}
              className={`p-4 rounded-lg shadow-sm hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1 ${
                notification.is_read ? 'bg-white' : 'bg-[#FFF8F3]'
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <span className="text-2xl transform hover:scale-110 transition-transform duration-200" role="img" aria-label={notification.type}>
                    {getNotificationIcon(notification.type)}
                  </span>
                  <div>
                    <h3 className=" font-medium text-gray-900">{notification.title}</h3>
                    <p className="text-sm text-gray-600 mt-1" dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(notification.content) }}></p>
                    {notification.link && (
                      <Link
                        to={notification.link}
                        className="text-[#FF6B2C] hover:text-[#FF7A35] hover:underline text-sm mt-2 inline-block transition-colors duration-200"
                        onClick={async (e) => { // Permet de marquer la notification comme lue et de rediriger vers le lien
                          if (!notification.is_read) {
                            e.preventDefault();
                            await toggleReadStatus(notification.id, true);
                            navigate(DOMPurify.sanitize(notification.link));
                          }
                        }}
                      >
                        Consulter le lien
                      </Link>
                    )}
                    <p className="text-gray-400 text-sm mt-2">
                      {format(new Date(notification.created_at), 'PPP à HH:mm', {
                        locale: fr
                      })}
                    </p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={async () => {
                      await toggleReadStatus(notification.id, !notification.is_read);
                      // Mettre à jour l'état local pour refléter le changement
                      setAllNotifications(prev => 
                        prev.map(notif => 
                          notif.id === notification.id 
                            ? { ...notif, is_read: !notification.is_read }
                            : notif
                        )
                      );
                    }}
                    className="p-2 hover:bg-[#FFE4BA] rounded-lg transition-all duration-200 transform hover:scale-110"
                    title={notification.is_read ? 'Marquer comme non lu' : 'Marquer comme lu'}
                  >
                    {notification.is_read ? (
                      <MailOpenIcon className="h-5 w-5 text-gray-400" />
                    ) : (
                      <MailIcon className="h-5 w-5 text-[#FF6B2C]" />
                    )}
                  </button>
                  <button
                    onClick={async () => {
                      await toggleArchiveStatus(notification.id, !notification.is_archived);
                      await fetchNotifications(showArchived, {
                        page: currentPage,
                        type: selectedType === 'all' ? undefined : selectedType,
                        search: debouncedSearchTerm
                      });
                    }}
                    className="p-2 hover:bg-[#FFE4BA] rounded-lg transition-all duration-200 transform hover:scale-110"
                    title={showArchived ? 'Désarchiver' : 'Archiver'}
                  >
                    <ArchiveIcon className={`h-5 w-5 ${showArchived ? 'text-[#FF6B2C]' : 'text-gray-400'}`} />
                  </button>
                  <button
                    onClick={() => handleDeleteNotification(notification.id)}
                    className="p-2 hover:bg-red-100 rounded-lg transition-all duration-200 transform hover:scale-110"
                    title="Supprimer"
                  >
                    <TrashIcon className="h-5 w-5 text-gray-400 hover:text-red-600" />
                  </button>
                </div>
              </div>
            </motion.div>
          ))}

          {/* Loading indicator et observer target */}
          <div ref={observerTarget} className="py-4">
            {isLoadingMore && (
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-[#FF6B2C]"></div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Modal de confirmation pour marquer toutes les notifications comme lues */}
      <Dialog
        open={showMarkAllReadModal}
        onClose={() => setShowMarkAllReadModal(false)}
        aria-labelledby="mark-all-read-dialog-title"
      >
        <DialogTitle id="mark-all-read-dialog-title">
          Marquer toutes les notifications comme lues ?
        </DialogTitle>
        <DialogContent>
          Êtes-vous sûr de vouloir marquer toutes les notifications comme lues ?
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setShowMarkAllReadModal(false)}
            sx={{
              color: 'gray',
              '&:hover': { backgroundColor: '#f3f4f6' }
            }}
          >
            Annuler
          </Button>
          <Button
            onClick={() => {
              markAllAsRead();
              setShowMarkAllReadModal(false);
            }}
            sx={{
              color: '#FF6B2C',
              '&:hover': { backgroundColor: '#FFF8F3' }
            }}
            autoFocus
          >
            Confirmer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Modal de confirmation pour supprimer toutes les notifications */}
      <Dialog
        open={showDeleteAllModal}
        onClose={() => setShowDeleteAllModal(false)}
        aria-labelledby="delete-all-dialog-title"
      >
        <DialogTitle id="delete-all-dialog-title">
          Supprimer toutes les notifications ?
        </DialogTitle>
        <DialogContent>
          Êtes-vous sûr de vouloir supprimer toutes les notifications ? Cette action est irréversible.
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setShowDeleteAllModal(false)}
            sx={{
              color: 'gray',
              '&:hover': { backgroundColor: '#f3f4f6' }
            }}
          >
            Annuler
          </Button>
          <Button
            onClick={() => {
              deleteAllNotifications(showArchived);
              setShowDeleteAllModal(false);
            }}
            sx={{
              color: 'red',
              '&:hover': { backgroundColor: '#fee2e2' }
            }}
            autoFocus
          >
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Modal de confirmation pour archiver toutes les notifications */}
      <Dialog
        open={showArchiveAllModal}
        onClose={() => setShowArchiveAllModal(false)}
      >
        <DialogTitle>Archiver toutes les notifications</DialogTitle>
        <DialogContent>
          Êtes-vous sûr de vouloir archiver toutes les notifications ? Cette action ne peut pas être annulée.
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowArchiveAllModal(false)} color="inherit">
            Annuler
          </Button>
          <Button
            onClick={async () => {
              setShowArchiveAllModal(false);
              await archiveAllNotifications();
            }}
            color="primary"
            variant="contained"
            sx={{ bgcolor: '#FF6B2C', '&:hover': { bgcolor: '#FF7A35' } }}
          >
            Archiver tout
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default NotificationsPage; 