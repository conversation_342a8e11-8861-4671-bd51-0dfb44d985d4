import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { logUserActivity, getIpFromRequest } from '../utils/activityLogger';
import { decryptProfilDataAsync, decryptUserDataAsync } from '../utils/encryption';
import {
  SupportTicket,
  CreateTicketDTO,
  UpdateTicketDTO,
  TicketFilters,
} from '../types/supportTickets';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { uploadTicketAttachment } from '../services/storage';
import { deleteTicketAttachments } from './supportTicketAttachments';
import { formatTicketStatus } from '../utils/formatters';
import { sendNewSupportTicketEmail, sendSupportTicketStatusChangeEmail, sendSupportTicketAssignedEmail } from '../services/emailService';

const CACHE_PREFIX = 'support_ticket:';
const STATS_CACHE_KEY = 'support_ticket_stats';
const CACHE_DURATION = 60 * 5; // 5 minutes

// Configuration des SLA par priorité (en heures)
const SLA_CONFIG = {
  urgente: 4,
  elevee: 8,
  normale: 24,
  faible: 48
};

// Fonction utilitaire pour générer un nom de fichier unique
const generateUniqueFileName = (originalName: string): string => {
  const fileExt = path.extname(originalName);
  const sanitizedName = path.basename(originalName, fileExt)
    .replace(/[^a-zA-Z0-9]/g, '_')
    .substring(0, 50);
  const uniqueId = crypto.randomBytes(16).toString('hex');
  return `${sanitizedName}_${uniqueId}${fileExt}`;
};

// Fonction pour vérifier et fermer les tickets inactifs depuis 30 jours
export const checkAndCloseInactiveTickets = async (): Promise<void> => {
  try {
    // Calculer la date limite (30 jours dans le passé)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const thirtyDaysAgoISOString = thirtyDaysAgo.toISOString();

    // Récupérer les tickets qui n'ont pas été mis à jour depuis 30 jours et qui ne sont pas déjà fermés
    const { data: inactiveTickets, error: fetchError } = await supabase
      .from('support_tickets')
      .select('*, user:users!support_tickets_user_id_fkey(id, email)')
      .not('status', 'eq', 'ferme')
      .lt('last_response_at', thirtyDaysAgoISOString);

    if (fetchError) {
      logger.error('Erreur lors de la récupération des tickets inactifs', { error: fetchError });
      return;
    }

    if (!inactiveTickets || inactiveTickets.length === 0) {
      logger.info('Aucun ticket inactif à fermer');
      return;
    }

    logger.info(`Fermeture automatique de ${inactiveTickets.length} ticket(s) inactif(s)`);

    // Traiter chaque ticket inactif
    for (const ticket of inactiveTickets) {
      // Mettre à jour le statut du ticket
      const { error: updateError } = await supabase
        .from('support_tickets')
        .update({
          status: 'ferme',
          updated_at: new Date().toISOString()
        })
        .eq('id', ticket.id);

      if (updateError) {
        logger.error(`Erreur lors de la fermeture automatique du ticket ${ticket.id}`, { error: updateError });
        continue;
      }

      // Utiliser handleTicketStateChange pour la notification par email
      // en créant une copie du ticket avec le nouveau statut
      const updatedTicket = {
        ...ticket,
        status: 'ferme'
      };

      try {
        await handleTicketStateChange(ticket, updatedTicket, true, 'system'); // true pour indiquer que c'est automatique, 'system' comme ID d'assignateur
        logger.info(`Notification envoyée pour la fermeture automatique du ticket ${ticket.id}`);
      } catch (emailError) {
        logger.error(`Erreur lors de l'envoi de la notification pour le ticket ${ticket.id}`, { error: emailError });
      }

      // Ajouter un commentaire système indiquant la fermeture automatique
      try {
        // Récupérer un administrateur pour le commentaire système
        const { data: adminUser, error: adminError } = await supabase
          .from('users')
          .select('id')
          .eq('role', 'jobpadm')
          .limit(1)
          .single();

        if (adminError || !adminUser) {
          logger.error(`Erreur lors de la récupération d'un administrateur pour le commentaire système`, { error: adminError });
          continue;
        }

        // Insérer le commentaire avec l'ID de l'administrateur
        const { error: commentError } = await supabase
          .from('support_ticket_comments')
          .insert({
            ticket_id: ticket.id,
            user_id: adminUser.id, // Utiliser l'ID d'un admin au lieu de null
            message: "Ce ticket a été fermé automatiquement après 30 jours d'inactivité. Si votre problème persiste, veuillez ouvrir un nouveau ticket.",
            is_internal: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (commentError) {
          logger.error(`Erreur lors de l'ajout du commentaire de fermeture pour le ticket ${ticket.id}`, { error: commentError });
        } else {
          logger.info(`Commentaire système ajouté pour le ticket ${ticket.id}`);
        }
      } catch (commentError) {
        logger.error(`Erreur lors de l'ajout du commentaire de fermeture pour le ticket ${ticket.id}`, { error: commentError });
      }
    }

    logger.info(`${inactiveTickets.length} tickets ont été fermés automatiquement après 30 jours d'inactivité`);
  } catch (error) {
    logger.error('Erreur lors de la vérification des tickets inactifs', { error });
  }
};

// Fonction pour assainir le HTML
const sanitizeHtml = (html: string): string => {
  // Version simple qui supprime les balises HTML
  return html.replace(/<[^>]*>?/gm, '');
};

// Fonction pour calculer la date d'échéance SLA
const calculateSLADueDate = (priority: string): string => {
  const hours = SLA_CONFIG[priority as keyof typeof SLA_CONFIG] || SLA_CONFIG.normale;
  const dueDate = new Date();
  dueDate.setHours(dueDate.getHours() + hours);
  return dueDate.toISOString();
};

// Remplace le service de cache des tickets avec des fonctions locales utilisant Redis
const ticketCacheService = {
  getTicket: async (ticketId: string): Promise<SupportTicket | null> => {
    const cached = await redis.get(`${CACHE_PREFIX}${ticketId}`);
    if (cached) {
      return JSON.parse(cached);
    }
    return null;
  },
  setTicket: async (ticketId: string, ticket: SupportTicket): Promise<void> => {
    await redis.set(`${CACHE_PREFIX}${ticketId}`, JSON.stringify(ticket), 'EX', CACHE_DURATION);
  },
  invalidateTicket: async (ticketId: string): Promise<void> => {
    await redis.del(`${CACHE_PREFIX}${ticketId}`);
  }
};

// Fonction de log sécurité
const logSecurityEvent = (event: { action: string; ip: string; status: string; message: string }): void => {
  logger.log({
    level: 'info',
    message: `Security event: ${event.action} from ${event.ip} - ${event.status}: ${event.message}`,
    meta: event
  });
};

export const createTicket = async (req: Request, res: Response) => {
  try {
    // Exécuter la vérification des tickets inactifs
    checkAndCloseInactiveTickets().catch(error => {
      logger.error('Erreur lors de la vérification des tickets inactifs', { error });
    });

    const userId = req.user?.userId;

    if (!userId) {
      res.status(401).json({ error: 'Non authentifié' });
      return;
    }

    const ticketData: CreateTicketDTO = req.body;

    // Validation de base
    if (!ticketData.title || !ticketData.description) {
      res.status(400).json({ error: 'Titre et description requis' });
      return;
    }

    // Sanitization
    const sanitizedTitle = sanitizeHtml(ticketData.title);
    const sanitizedDescription = sanitizeHtml(ticketData.description);

    // Calcul de la date d'échéance SLA
    const slaDueAt = calculateSLADueDate(ticketData.priority);

    // Date actuelle pour l'horodatage
    const now = new Date().toISOString();

    // Création du ticket
    const { data: ticket, error } = await supabase
      .from('support_tickets')
      .insert({
        title: sanitizedTitle,
        description: sanitizedDescription,
        user_id: userId,
        status: 'nouveau',
        priority: ticketData.priority,
        category: ticketData.category,
        sla_due_at: slaDueAt,
        last_response_at: now, // Initialiser last_response_at
        repondu: false // Initialiser repondu à false
      })
      .select('*, user:users!support_tickets_user_id_fkey(id, email)')
      .single();

    if (error) {
      logger.error('Erreur lors de la création du ticket:', error);
      res.status(500).json({ error: 'Erreur lors de la création du ticket' });
      return;
    }

    // Gérer les pièces jointes si présentes
    const files = req.files?.files;
    if (files) {
      const fileArray = Array.isArray(files) ? files : [files];
      const attachments = [];

      for (const file of fileArray) {
        try {
          // Vérification du type MIME
          const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp', 'application/pdf', 'text/plain'];
          if (!allowedMimeTypes.includes(file.mimetype)) {
            logger.error('Type de fichier non autorisé :', { mimetype: file.mimetype });
            continue;
          }

          // Vérification de la taille
          const maxSize = 5 * 1024 * 1024; // 5MB
          if (file.size > maxSize) {
            logger.error('Fichier trop volumineux', { size: file.size });
            continue;
          }

          // Lire le fichier temporaire
          const fileBuffer = await new Promise<Buffer>((resolve, reject) => {
            require('fs').readFile(file.tempFilePath, (err: any, data: Buffer) => {
              if (err) reject(err);
              else resolve(data);
            });
          });

          // Upload de la pièce jointe
          const fileUrl = await uploadTicketAttachment(userId, fileBuffer, file.mimetype, ticket.id, file.name);

          // Créer l'entrée dans la table support_ticket_attachments
          const { data: attachment, error: attachmentError } = await supabase
            .from('support_ticket_attachments')
            .insert({
              ticket_id: ticket.id,
              user_id: userId,
              file_url: fileUrl,
              file_name: file.name,
              file_type: file.mimetype,
              file_size: file.size,
              created_at: now,
              updated_at: now
            })
            .select()
            .single();

          if (attachmentError) {
            logger.error('Erreur lors de l\'enregistrement de la pièce jointe:', attachmentError);
            continue;
          }

          attachments.push(attachment);
        } catch (error) {
          logger.error('Erreur lors du traitement d\'une pièce jointe:', error);
          continue;
        }
      }

      // Ajouter les pièces jointes à la réponse
      ticket.attachments = attachments;
    }

    // Notification par email
    if (ticket.user && ticket.user.email) {
      // Déchiffrer les données utilisateur
      const decryptedUser = await decryptUserDataAsync(ticket.user);

      await sendNewSupportTicketEmail(decryptedUser.email, {
        ticketId: ticket.id,
        title: ticket.title,
        description: ticket.description,
        priority: ticket.priority,
        category: ticket.category,
        userEmail: decryptedUser.email
      });
    }

    // Notification du staff
    await notifyStaff(ticket);

    // Journaliser l'action de création du ticket de support
    await logUserActivity(
      userId,
      'support_ticket_create',
      ticket.id,
      'support_ticket',
      {
        title: ticket.title,
        category: ticket.category,
        priority: ticket.priority,
        has_attachments: ticket.attachments && ticket.attachments.length > 0
      },
      getIpFromRequest(req)
    );

    res.status(201).json(ticket);
  } catch (error) {
    logger.error('Erreur lors de la création du ticket:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

export const updateTicket = async (req: Request, res: Response) => {
  try {
    const { ticketId } = req.params;
    const userId = req.user?.userId;
    const isStaff = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';

    if (!userId) {
      res.status(401).json({ error: 'Non authentifié' });
      return;
    }

    // Récupérer le ticket existant
    const { data: existingTicket, error: fetchError } = await supabase
      .from('support_tickets')
      .select('*, user:users!support_tickets_user_id_fkey(id, email), assignee:users!support_tickets_assigned_to_fkey(id, email)')
      .eq('id', ticketId)
      .single();

    if (fetchError) {
      logger.error('Erreur lors de la récupération du ticket:', fetchError);
      res.status(500).json({ error: 'Erreur lors de la récupération du ticket' });
      return;
    }

    if (!existingTicket) {
      res.status(404).json({ error: 'Ticket non trouvé' });
      return;
    }

    // Vérifier les permissions
    if (!isStaff && existingTicket.user_id !== userId) {
      res.status(403).json({ error: 'Non autorisé' });
      return;
    }

    // Si l'utilisateur n'est pas membre du staff, vérifier si le ticket a été créé il y a moins de 30 minutes
    if (!isStaff) {
      const ticketCreatedAt = new Date(existingTicket.created_at);
      const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);

      if (ticketCreatedAt < thirtyMinutesAgo) {
        res.status(403).json({
          error: 'Délai dépassé',
          message: 'Vous ne pouvez modifier votre ticket que dans les 30 minutes suivant sa création'
        });
        return;
      }
    }

    const updateData: Partial<UpdateTicketDTO> = req.body;
    const tags = req.body.tags;

    // Sanitization
    if (updateData.title) {
      updateData.title = sanitizeHtml(updateData.title);
    }
    if (updateData.description) {
      updateData.description = sanitizeHtml(updateData.description);
    }

    // Seul le staff peut modifier certains champs
    if (!isStaff) {
      delete updateData.status;
      delete updateData.priority;
      delete updateData.assigned_to;
    }

    // Si la priorité change, recalculer la date SLA
    if (updateData.priority && updateData.priority !== existingTicket.priority) {
      updateData.sla_due_at = calculateSLADueDate(updateData.priority);
    }

    // Supprimer les tags de updateData car ils sont gérés séparément
    delete updateData.tags;

    // Cas spécial: gestion de l'annulation d'assignation
    // Si assigned_to est explicitement défini à null ou undefined dans le body,
    // on le définit à null pour la base de données
    if (req.body.hasOwnProperty('assigned_to') && (req.body.assigned_to === null || req.body.assigned_to === undefined)) {
      updateData.assigned_to = null;
    }

    // Mise à jour du ticket
    const { data: updatedTicket, error } = await supabase
      .from('support_tickets')
      .update(updateData)
      .eq('id', ticketId)
      .select('*, user:users!support_tickets_user_id_fkey(id, email), assignee:users!support_tickets_assigned_to_fkey(id, email)')
      .single();

    if (error) {
      logger.error('Erreur lors de la mise à jour du ticket:', error);
      res.status(500).json({ error: 'Erreur lors de la mise à jour du ticket' });
      return;
    }

    // Gérer les tags si fournis et si l'utilisateur est un admin/modo
    if (isStaff && Array.isArray(tags)) {
      try {
        // 1. Récupérer les tags actuels du ticket
        const { data: currentTagRelations } = await supabase
          .from('support_ticket_tag_relations')
          .select('tag_id')
          .eq('ticket_id', ticketId);

        const currentTagIds = currentTagRelations?.map(relation => relation.tag_id) || [];
        const newTagIds = tags.map(tag => tag.id);

        // 2. Déterminer les tags à ajouter et à supprimer
        const tagsToAdd = newTagIds.filter(id => !currentTagIds.includes(id));
        const tagsToRemove = currentTagIds.filter(id => !newTagIds.includes(id));

        // 3. Supprimer les relations obsolètes
        if (tagsToRemove.length > 0) {
          await supabase
            .from('support_ticket_tag_relations')
            .delete()
            .eq('ticket_id', ticketId)
            .in('tag_id', tagsToRemove);
        }

        // 4. Ajouter les nouvelles relations
        if (tagsToAdd.length > 0) {
          const newRelations = tagsToAdd.map(tagId => ({
            ticket_id: ticketId,
            tag_id: tagId
          }));

          await supabase
            .from('support_ticket_tag_relations')
            .insert(newRelations);
        }
      } catch (tagError) {
        logger.error('Erreur lors de la mise à jour des tags du ticket:', tagError);
        // On continue malgré l'erreur sur les tags
      }
    }

    // Gérer les notifications
    await handleTicketStateChange(existingTicket, updatedTicket, false, userId);

    // Invalider le cache
    await ticketCacheService.invalidateTicket(ticketId);

    // Journaliser l'action de mise à jour du ticket
    await logUserActivity(
      userId,
      'support_ticket_update',
      ticketId,
      'support_ticket',
      {
        title: updatedTicket.title,
        fields_updated: Object.keys(updateData),
        status_changed: updateData.status && updateData.status !== existingTicket.status,
        priority_changed: updateData.priority && updateData.priority !== existingTicket.priority,
        assignment_changed: updateData.assigned_to !== undefined && updateData.assigned_to !== existingTicket.assigned_to
      },
      getIpFromRequest(req)
    );

    // Récupérer le ticket mis à jour avec ses tags
    const { data: finalTicket } = await supabase
      .from('support_tickets')
      .select(`
        *,
        user:users!support_tickets_user_id_fkey(id, email),
        assignee:users!support_tickets_assigned_to_fkey(id, email),
        tags:support_ticket_tag_relations(
          tag:support_ticket_tags(*)
        )
      `)
      .eq('id', ticketId)
      .single();

    // Transformer le format des tags pour correspondre à l'attente du frontend
    if (finalTicket && finalTicket.tags) {
      finalTicket.tags = finalTicket.tags.map((relation: any) => relation.tag);
    }

    res.json(finalTicket || updatedTicket);
  } catch (error) {
    logger.error('Erreur lors de la mise à jour du ticket:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

export const getTicket = async (req: Request, res: Response) => {
  try {
    const { ticketId } = req.params;
    const userId = req.user?.userId;
    const isStaff = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';

    // Essayer d'abord d'obtenir le ticket depuis le cache
    const cachedTicket = await ticketCacheService.getTicket(ticketId);
    if (cachedTicket) {
      // Vérifier les permissions
      if (!isStaff && cachedTicket.user_id !== userId) {
        res.status(403).json({ error: 'Non autorisé' });
        return;
      }
      res.json(cachedTicket);
      return;
    }

    // Sinon le récupérer de la base de données
    const { data: ticket, error } = await supabase
      .from('support_tickets')
      .select('*, user:users!support_tickets_user_id_fkey(id, email), assignee:users!support_tickets_assigned_to_fkey(id, email), support_ticket_comments(*), support_ticket_attachments(*)')
      .eq('id', ticketId)
      .single();

    if (error) {
      logger.error('Erreur lors de la récupération du ticket:', error);
      res.status(500).json({ error: 'Erreur lors de la récupération du ticket' });
      return;
    }

    if (!ticket) {
      res.status(404).json({ error: 'Ticket non trouvé' });
      return;
    }

    // Vérifier les permissions
    if (!isStaff && ticket.user_id !== userId) {
      res.status(403).json({ error: 'Non autorisé' });
      return;
    }

    // Mettre en cache
    await ticketCacheService.setTicket(ticketId, ticket);

    res.json(ticket);
  } catch (error) {
    logger.error('Erreur lors de la récupération du ticket:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

export const getTickets = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const isStaff = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';
    const filters: TicketFilters = req.query;

    // Préparer la requête
    let query = supabase
      .from('support_tickets')
      .select('*, user:users!support_tickets_user_id_fkey(id, email), assignee:users!support_tickets_assigned_to_fkey(id, email)');

    // Appliquer les filtres
    if (filters.status) {
      query = query.eq('status', filters.status);
    }
    if (filters.priority) {
      query = query.eq('priority', filters.priority);
    }
    if (filters.category) {
      query = query.eq('category', filters.category);
    }
    if (filters.search) {
      query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
    }
    // Ajouter le filtre status_not_in si présent
    if (filters.status_not_in) {
      let statusesToExclude: string[] = [];

      // Si c'est déjà un tableau, on l'utilise tel quel
      if (Array.isArray(filters.status_not_in)) {
        statusesToExclude = filters.status_not_in;
      }
      // Si c'est une chaîne, on la divise en tableau
      else if (typeof filters.status_not_in === 'string') {
        const statusString = filters.status_not_in as string;
        if (statusString.trim() !== '') {
          statusesToExclude = statusString.split(',').map((s: string) => s.trim());
        }
      }

      // Appliquer le filtre seulement s'il y a des statuts à exclure
      if (statusesToExclude.length > 0) {
        // Pour plus de robustesse, appliquons chaque statut à exclure un par un
        statusesToExclude.forEach(status => {
          query = query.neq('status', status);
        });
        logger.info(`Filtre status_not_in appliqué individuellement pour: ${statusesToExclude.join(', ')}`);
      }
    }
    // Ajouter le filtre repondu si présent
    if (filters.repondu !== undefined) {
      // Convertir la chaîne 'true'/'false' en booléen si nécessaire
      const reponduValue = typeof filters.repondu === 'string'
        ? (filters.repondu as string).toLowerCase() === 'true'
        : Boolean(filters.repondu);

      query = query.eq('repondu', reponduValue);
      logger.info(`Filtre repondu appliqué: ${reponduValue}`);
    }

    // Ajouter le filtre assigned_to si présent
    if (filters.assigned_to) {
      query = query.eq('assigned_to', filters.assigned_to);
      logger.info(`Filtre assigned_to appliqué: ${filters.assigned_to}`);
    }

    // Si l'utilisateur n'est pas du staff, limiter aux tickets de l'utilisateur
    if (!isStaff) {
      query = query.eq('user_id', userId);
    }

    // Pagination
    const page = Number(filters.page) || 1;
    const limit = Number(filters.limit) || 20;
    const start = (page - 1) * limit;
    const end = start + limit - 1;

    query = query.range(start, end);

    // Exécuter la requête
    const { data: tickets, error, count } = await query.order('created_at', { ascending: false });

    if (error) {
      logger.error('Erreur lors de la récupération des tickets:', error);
      res.status(500).json({ error: 'Erreur lors de la récupération des tickets' });
      return;
    }

    res.json({
      tickets,
      pagination: {
        page,
        limit,
        total: count || 0,
        pages: count ? Math.ceil(count / limit) : 0
      }
    });
  } catch (error) {
    logger.error('Erreur lors de la récupération des tickets:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

export const getTicketStats = async (req: Request, res: Response) => {
  try {
    // Récupérer la période demandée (7, 30, 90 jours) depuis la query
    const timeRange = req.query.timeRange ? String(req.query.timeRange) : '7';
    const cacheKey = `${STATS_CACHE_KEY}:${timeRange}`;

    // Vérifier si les stats sont en cache
    const cachedStats = await redis.get(cacheKey);
    if (cachedStats) {
      res.json(JSON.parse(cachedStats));
      return;
    }

    // Calculer la date de début en fonction de la période
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(timeRange));
    const startDateISO = startDate.toISOString();

    // Définir la date de fin (aujourd'hui)
    const endDate = new Date();
    const endDateISO = endDate.toISOString();

    // Requêtes parallèles pour obtenir toutes les statistiques
    const [
      // Statistiques globales
      totalQuery,
      resolvedTicketsQuery,
      openTicketsQuery,
      byStatusQuery,
      byPriorityQuery,
      byCategoryQuery,

      // Statistiques temporelles
      byDateQuery,

      // Statistiques d'assignation
      byAgentQuery,

      // Statistiques de performance
      resolutionTimeQuery,
      firstResponseTimeQuery,

      // Statistiques supplémentaires
      tagsQuery
    ] = await Promise.all([
      // Total des tickets pour la période (TOUS les statuts)
      supabase.from('support_tickets')
        .select('id', { count: 'exact' })
        .in('status', ['nouveau', 'en_attente', 'en_cours', 'resolu', 'ferme', 'reouvert']),

      // Tickets résolus (uniquement "resolu")
      supabase.from('support_tickets')
        .select('id', { count: 'exact' })
        .eq('status', 'resolu'),

      // Tickets ouverts (statuts: nouveau, en_attente, en_cours, reouvert)
      supabase.from('support_tickets')
        .select('id', { count: 'exact' })
        .in('status', ['nouveau', 'en_attente', 'en_cours', 'reouvert']),

      // Distribution par statut
      supabase.from('support_tickets')
        .select('status'),

      // Distribution par priorité
      supabase.from('support_tickets')
        .select('priority'),

      // Distribution par catégorie
      supabase.from('support_tickets')
        .select('category'),

      // Distribution par date
      supabase.from('support_tickets')
        .select('created_at')
        .gte('created_at', startDateISO),

      // Distribution par agent assigné
      supabase.from('support_tickets')
        .select('assigned_to'),

      // Temps de résolution moyen
      supabase.from('support_tickets')
        .select('resolved_at, created_at, category')
        .not('resolved_at', 'is', null),

      // Temps de première réponse
      supabase.from('support_tickets')
        .select('last_response_at, created_at')
        .not('last_response_at', 'is', null),

      // Distribution des tags
      supabase.from('support_tickets')
        .select('id')
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO)
        .then(result => {
          if (result.error) {
            throw result.error;
          }
          // Extraire les IDs de tickets en un tableau
          const ticketIds = result.data.map(ticket => ticket.id);
          // Retourner une nouvelle requête qui utilise le tableau d'IDs
          return supabase.from('support_ticket_tag_relations')
            .select('tag_id, support_ticket_tags(name)')
            .in('ticket_id', ticketIds);
        })
    ]);

    // Vérifier les erreurs
    const errors = [
      totalQuery.error, resolvedTicketsQuery.error, openTicketsQuery.error,
      byStatusQuery.error, byPriorityQuery.error, byCategoryQuery.error,
      byDateQuery.error, byAgentQuery.error, resolutionTimeQuery.error,
      firstResponseTimeQuery.error, tagsQuery.error
    ].filter(error => error !== null);

    if (errors.length > 0) {
      logger.error('Erreur lors de la récupération des statistiques:', errors);
      res.status(500).json({ error: 'Erreur lors de la récupération des statistiques' });
      return;
    }

    // Transformer les résultats bruts en statistiques
    const byStatus: Record<string, number> = {};
    const byPriority: Record<string, number> = {};
    const byCategory: Record<string, number> = {};
    const byDate: Record<string, number> = {};
    const byAgent: Record<string, number> = {};
    const resolutionByCategory: Record<string, number> = {};
    const tagsDistribution: Record<string, number> = {};
    const peakHours: Record<string, number> = {};

    // Initialiser les statuts avec 0
    ['nouveau', 'en_attente', 'en_cours', 'resolu', 'ferme', 'reouvert'].forEach(status => {
      byStatus[status] = 0;
    });

    // Initialiser les priorités avec 0
    ['faible', 'normale', 'elevee', 'urgente'].forEach(priority => {
      byPriority[priority] = 0;
    });

    // Initialiser les catégories avec 0
    ['technique', 'facturation', 'compte', 'mission', 'autre'].forEach(category => {
      byCategory[category] = 0;
      resolutionByCategory[category] = 0;
    });

    // Initialiser les heures de pointe
    for (let i = 0; i < 24; i++) {
      peakHours[i] = 0;
    }

    // Calculer les dates pour la période
    for (let i = 0; i < parseInt(timeRange); i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      byDate[date.toISOString().split('T')[0]] = 0;
    }

    // Compter les statuts
    byStatusQuery.data?.forEach(item => {
      byStatus[item.status] = (byStatus[item.status] || 0) + 1;
    });

    // Compter les priorités
    byPriorityQuery.data?.forEach(item => {
      byPriority[item.priority] = (byPriority[item.priority] || 0) + 1;
    });

    // Compter les catégories
    byCategoryQuery.data?.forEach(item => {
      byCategory[item.category] = (byCategory[item.category] || 0) + 1;
    });

    // Compter par date (seulement pour les tickets créés durant la période)
    byDateQuery.data?.forEach(item => {
      const date = new Date(item.created_at).toISOString().split('T')[0];
      // Vérifier si la date existe dans notre dictionnaire (elle devrait toujours exister)
      if (byDate.hasOwnProperty(date)) {
        byDate[date] += 1;
      }

      // Heures de pointe
      const hour = new Date(item.created_at).getHours();
      peakHours[hour] = (peakHours[hour] || 0) + 1;
    });

    // Log pour vérifier la distribution par date
    // const sortedDates = Object.entries(byDate)
    //   .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime())
    //   .map(([date, count]) => ({ date, count }));
    // console.log('Distribution par date (triée chronologiquement):', sortedDates);

    // Compter par agent
    byAgentQuery.data?.forEach(item => {
      // Utiliser directement l'ID d'assignation ou 'Non assigné' par défaut
      const agentName = item.assigned_to ? `Agent #${item.assigned_to.substring(0, 8)}` : 'Non assigné';
      byAgent[agentName] = (byAgent[agentName] || 0) + 1;
    });

    // Calculer le temps de résolution moyen par catégorie
    const resolutionTimes: number[] = [];
    let totalResolutionTime = 0;

    resolutionTimeQuery.data?.forEach(item => {
      if (item.resolved_at && item.created_at) {
        const created = new Date(item.created_at).getTime();
        const resolved = new Date(item.resolved_at).getTime();
        const timeDiff = (resolved - created) / (1000 * 60 * 60); // en heures

        resolutionTimes.push(timeDiff);
        totalResolutionTime += timeDiff;

        // Ajouter au temps par catégorie
        if (item.category) {
          resolutionByCategory[item.category] =
            (resolutionByCategory[item.category] || 0) + timeDiff;
        }
      }
    });

    // Calculer le temps moyen de première réponse
    const firstResponseTimes: number[] = [];
    let totalFirstResponseTime = 0;

    firstResponseTimeQuery.data?.forEach(item => {
      if (item.last_response_at && item.created_at) {
        const created = new Date(item.created_at).getTime();
        const firstResponse = new Date(item.last_response_at).getTime();
        const timeDiff = (firstResponse - created) / (1000 * 60 * 60); // en heures

        // On ne prend en compte que les délais positifs et raisonnables (< 720h soit 30 jours)
        if (timeDiff > 0 && timeDiff < 720) {
          firstResponseTimes.push(timeDiff);
          totalFirstResponseTime += timeDiff;
        }
      }
    });

    // Compter les tags
    tagsQuery.data?.forEach(item => {
      if (item.support_ticket_tags && typeof item.support_ticket_tags === 'object' && 'name' in item.support_ticket_tags) {
        const tagName = item.support_ticket_tags.name as string;
        tagsDistribution[tagName] = (tagsDistribution[tagName] || 0) + 1;
      }
    });

    // Calculer les moyennes
    const averageResolutionTime = resolutionTimes.length > 0
      ? totalResolutionTime / resolutionTimes.length
      : 0;

    const averageFirstResponseTime = firstResponseTimes.length > 0
      ? totalFirstResponseTime / firstResponseTimes.length
      : 0;

    // Temps de réponse moyen (utilisé pour l'affichage dans le dashboard)
    // On privilégie le temps de première réponse s'il est disponible
    const averageResponseTime = averageFirstResponseTime > 0
      ? averageFirstResponseTime
      : averageResolutionTime;

    // Calculer la note moyenne de satisfaction (simulée car non implémentée)
    const averageSatisfaction = 4.5; // Entre 0 et 5

    // Calculer le taux de conformité SLA (simulé car non implémenté)
    const slaComplianceRate = 85; // En pourcentage

    // Calculer le nombre de tickets résolus et non résolus
    const resolvedTickets = resolvedTicketsQuery.count || 0;
    const openTickets = openTicketsQuery.count || 0;

    // Formater les temps de résolution par catégorie (moyenne par catégorie)
    Object.keys(resolutionByCategory).forEach(category => {
      const categoryCount = byCategory[category] || 1;
      resolutionByCategory[category] = resolutionByCategory[category] / categoryCount;
    });

    const stats = {
      total: totalQuery.count || 0,
      by_status: byStatus,
      by_priority: byPriority,
      by_category: byCategory,
      by_date: byDate,
      open_tickets: openTickets,
      resolved_tickets: resolvedTickets,
      average_resolution_time: Math.round(averageResolutionTime * 10) / 10,
      average_first_response_time: Math.round(averageFirstResponseTime * 10) / 10,
      average_response_time: Math.round(averageResponseTime * 10) / 10,
      average_satisfaction: averageSatisfaction,
      sla_compliance_rate: slaComplianceRate,
      tickets_by_agent: byAgent,
      resolution_by_category: resolutionByCategory,
      peak_hours: peakHours,
      tags_distribution: tagsDistribution
    };

    // Mettre en cache
    await redis.set(cacheKey, JSON.stringify(stats), 'EX', CACHE_DURATION);

    res.json(stats);
  } catch (error) {
    logger.error('Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

/**
 * Récupérer les statistiques des tickets avec une plage de dates personnalisée
 */
export const getTicketStatsCustom = async (req: Request, res: Response) => {
  try {
    // Récupérer les dates de début et de fin depuis la query
    const { startDate: startDateStr, endDate: endDateStr } = req.query;

    if (!startDateStr || !endDateStr) {
      res.status(400).json({ error: 'Les paramètres startDate et endDate sont requis' });
      return;
    }

    // Convertir les dates en objets Date
    const startDate = new Date(startDateStr as string);
    const endDate = new Date(endDateStr as string);

    // Vérifier que les dates sont valides
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      res.status(400).json({ error: 'Les dates fournies sont invalides' });
      return;
    }

    // Vérifier que la date de début est antérieure à la date de fin
    if (startDate > endDate) {
      res.status(400).json({ error: 'La date de début doit être antérieure à la date de fin' });
      return;
    }

    // Créer une clé de cache unique pour cette plage de dates
    const cacheKey = `${STATS_CACHE_KEY}:custom:${startDateStr}:${endDateStr}`;

    // Vérifier si les stats sont en cache
    const cachedStats = await redis.get(cacheKey);
    if (cachedStats) {
      res.json(JSON.parse(cachedStats));
      return;
    }

    // Convertir les dates en format ISO pour les requêtes
    const startDateISO = startDate.toISOString();
    const endDateISO = new Date(endDate.getTime() + 24 * 60 * 60 * 1000 - 1).toISOString(); // Fin de la journée

    // Requêtes parallèles pour obtenir toutes les statistiques avec la plage de dates
    const [
      // Statistiques globales
      totalQuery,
      resolvedTicketsQuery,
      openTicketsQuery,
      byStatusQuery,
      byPriorityQuery,
      byCategoryQuery,

      // Statistiques temporelles
      byDateQuery,

      // Statistiques d'assignation
      byAgentQuery,

      // Statistiques de performance
      resolutionTimeQuery,
      firstResponseTimeQuery,

      // Statistiques supplémentaires
      tagsQuery
    ] = await Promise.all([
      // Total des tickets pour la période (TOUS les statuts)
      supabase.from('support_tickets')
        .select('id', { count: 'exact' })
        .in('status', ['nouveau', 'en_attente', 'en_cours', 'resolu', 'ferme', 'reouvert'])
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO),

      // Tickets résolus (uniquement "resolu")
      supabase.from('support_tickets')
        .select('id', { count: 'exact' })
        .eq('status', 'resolu')
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO),

      // Tickets ouverts (statuts: nouveau, en_attente, en_cours, reouvert)
      supabase.from('support_tickets')
        .select('id', { count: 'exact' })
        .in('status', ['nouveau', 'en_attente', 'en_cours', 'reouvert'])
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO),

      // Distribution par statut
      supabase.from('support_tickets')
        .select('status')
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO),

      // Distribution par priorité
      supabase.from('support_tickets')
        .select('priority')
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO),

      // Distribution par catégorie
      supabase.from('support_tickets')
        .select('category')
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO),

      // Distribution par date
      supabase.from('support_tickets')
        .select('created_at')
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO),

      // Distribution par agent assigné
      supabase.from('support_tickets')
        .select('assigned_to')
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO),

      // Temps de résolution moyen
      supabase.from('support_tickets')
        .select('resolved_at, created_at, category')
        .not('resolved_at', 'is', null)
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO),

      // Temps de première réponse
      supabase.from('support_tickets')
        .select('last_response_at, created_at')
        .not('last_response_at', 'is', null)
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO),

      // Distribution des tags (pour les tickets de la période)
      supabase.from('support_tickets')
        .select('id')
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO)
        .then(result => {
          if (result.error) {
            throw result.error;
          }
          // Extraire les IDs de tickets en un tableau
          const ticketIds = result.data.map(ticket => ticket.id);
          // Retourner une nouvelle requête qui utilise le tableau d'IDs
          return supabase.from('support_ticket_tag_relations')
            .select('tag_id, support_ticket_tags(name)')
            .in('ticket_id', ticketIds);
        })
    ]);

    // Vérifier les erreurs
    const errors = [
      totalQuery.error, resolvedTicketsQuery.error, openTicketsQuery.error,
      byStatusQuery.error, byPriorityQuery.error, byCategoryQuery.error,
      byDateQuery.error, byAgentQuery.error, resolutionTimeQuery.error,
      firstResponseTimeQuery.error, tagsQuery.error
    ].filter(error => error !== null);

    if (errors.length > 0) {
      logger.error('Erreur lors de la récupération des statistiques personnalisées:', errors);
      res.status(500).json({ error: 'Erreur lors de la récupération des statistiques personnalisées' });
      return;
    }

    // Transformer les résultats bruts en statistiques
    const byStatus: Record<string, number> = {};
    const byPriority: Record<string, number> = {};
    const byCategory: Record<string, number> = {};
    const byDate: Record<string, number> = {};
    const byAgent: Record<string, number> = {};
    const resolutionByCategory: Record<string, number> = {};
    const tagsDistribution: Record<string, number> = {};
    const peakHours: Record<string, number> = {};

    // Initialiser les statuts avec 0
    ['nouveau', 'en_attente', 'en_cours', 'resolu', 'ferme', 'reouvert'].forEach(status => {
      byStatus[status] = 0;
    });

    // Initialiser les priorités avec 0
    ['faible', 'normale', 'elevee', 'urgente'].forEach(priority => {
      byPriority[priority] = 0;
    });

    // Initialiser les catégories avec 0
    ['technique', 'facturation', 'compte', 'mission', 'autre'].forEach(category => {
      byCategory[category] = 0;
      resolutionByCategory[category] = 0;
    });

    // Initialiser les heures de pointe
    for (let i = 0; i < 24; i++) {
      peakHours[i] = 0;
    }

    // Initialiser le dictionnaire de dates pour la période spécifiée
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      const dateStr = currentDate.toISOString().split('T')[0];
      byDate[dateStr] = 0;
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Compter les statuts
    byStatusQuery.data?.forEach(item => {
      byStatus[item.status] = (byStatus[item.status] || 0) + 1;
    });

    // Compter les priorités
    byPriorityQuery.data?.forEach(item => {
      byPriority[item.priority] = (byPriority[item.priority] || 0) + 1;
    });

    // Compter les catégories
    byCategoryQuery.data?.forEach(item => {
      byCategory[item.category] = (byCategory[item.category] || 0) + 1;
    });

    // Compter par date
    byDateQuery.data?.forEach(item => {
      const date = new Date(item.created_at).toISOString().split('T')[0];
      // Vérifier si la date existe dans notre dictionnaire (elle devrait toujours exister)
      if (byDate.hasOwnProperty(date)) {
        byDate[date] += 1;
      }

      // Heures de pointe
      const hour = new Date(item.created_at).getHours();
      peakHours[hour] = (peakHours[hour] || 0) + 1;
    });

    // Compter par agent
    byAgentQuery.data?.forEach(item => {
      // Utiliser directement l'ID d'assignation ou 'Non assigné' par défaut
      const agentName = item.assigned_to ? `Agent #${item.assigned_to.substring(0, 8)}` : 'Non assigné';
      byAgent[agentName] = (byAgent[agentName] || 0) + 1;
    });

    // Calculer le temps de résolution moyen par catégorie
    const resolutionTimes: number[] = [];
    let totalResolutionTime = 0;

    resolutionTimeQuery.data?.forEach(item => {
      if (item.resolved_at && item.created_at) {
        const created = new Date(item.created_at).getTime();
        const resolved = new Date(item.resolved_at).getTime();
        const timeDiff = (resolved - created) / (1000 * 60 * 60); // en heures

        resolutionTimes.push(timeDiff);
        totalResolutionTime += timeDiff;

        // Ajouter au temps par catégorie
        if (item.category) {
          resolutionByCategory[item.category] =
            (resolutionByCategory[item.category] || 0) + timeDiff;
        }
      }
    });

    // Calculer le temps moyen de première réponse
    const firstResponseTimes: number[] = [];
    let totalFirstResponseTime = 0;

    firstResponseTimeQuery.data?.forEach(item => {
      if (item.last_response_at && item.created_at) {
        const created = new Date(item.created_at).getTime();
        const firstResponse = new Date(item.last_response_at).getTime();
        const timeDiff = (firstResponse - created) / (1000 * 60 * 60); // en heures

        // On ne prend en compte que les délais positifs et raisonnables (< 720h soit 30 jours)
        if (timeDiff > 0 && timeDiff < 720) {
          firstResponseTimes.push(timeDiff);
          totalFirstResponseTime += timeDiff;
        }
      }
    });

    // Compter les tags
    tagsQuery.data?.forEach(item => {
      if (item.support_ticket_tags && typeof item.support_ticket_tags === 'object' && 'name' in item.support_ticket_tags) {
        const tagName = item.support_ticket_tags.name as string;
        tagsDistribution[tagName] = (tagsDistribution[tagName] || 0) + 1;
      }
    });

    // Calculer les moyennes
    const averageResolutionTime = resolutionTimes.length > 0
      ? totalResolutionTime / resolutionTimes.length
      : 0;

    const averageFirstResponseTime = firstResponseTimes.length > 0
      ? totalFirstResponseTime / firstResponseTimes.length
      : 0;

    // Temps de réponse moyen (utilisé pour l'affichage dans le dashboard)
    // On privilégie le temps de première réponse s'il est disponible
    const averageResponseTime = averageFirstResponseTime > 0
      ? averageFirstResponseTime
      : averageResolutionTime;

    // Calculer la note moyenne de satisfaction (simulée car non implémentée)
    const averageSatisfaction = 4.5; // Entre 0 et 5

    // Calculer le taux de conformité SLA (simulé car non implémenté)
    const slaComplianceRate = 85; // En pourcentage

    // Calculer le nombre de tickets résolus et non résolus
    const resolvedTickets = resolvedTicketsQuery.count || 0;
    const openTickets = openTicketsQuery.count || 0;

    // Formater les temps de résolution par catégorie (moyenne par catégorie)
    Object.keys(resolutionByCategory).forEach(category => {
      const categoryCount = byCategory[category] || 1;
      resolutionByCategory[category] = resolutionByCategory[category] / categoryCount;
    });

    const stats = {
      total: totalQuery.count || 0,
      by_status: byStatus,
      by_priority: byPriority,
      by_category: byCategory,
      by_date: byDate,
      open_tickets: openTickets,
      resolved_tickets: resolvedTickets,
      average_resolution_time: Math.round(averageResolutionTime * 10) / 10,
      average_first_response_time: Math.round(averageFirstResponseTime * 10) / 10,
      average_response_time: Math.round(averageResponseTime * 10) / 10,
      average_satisfaction: averageSatisfaction,
      sla_compliance_rate: slaComplianceRate,
      tickets_by_agent: byAgent,
      resolution_by_category: resolutionByCategory,
      peak_hours: peakHours,
      tags_distribution: tagsDistribution
    };

    // Mettre en cache avec une durée plus courte pour les requêtes personnalisées
    await redis.set(cacheKey, JSON.stringify(stats), 'EX', CACHE_DURATION);

    res.json(stats);
  } catch (error) {
    logger.error('Erreur lors de la récupération des statistiques personnalisées:', error);
    res.status(500).json({ error: 'Erreur lors de la récupération des statistiques personnalisées' });
  }
};

// Fonctions utilitaires
const notifyStaff = async (ticket: SupportTicket) => {
  try {
    const { data: staffUsers } = await supabase
      .from('users')
      .select('email')
      .in('userType', ['jobpadm', 'jobmodo']);

    if (staffUsers?.length) {
      // Déchiffrer les emails du staff
      const decryptedStaffUsers = await Promise.all(staffUsers.map(user => decryptUserDataAsync(user)));
      const staffEmails = decryptedStaffUsers.map(user => user.email).join(',');

      // Déchiffrer l'email de l'utilisateur du ticket si disponible
      const userEmail = ticket.user?.email ? (await decryptUserDataAsync(ticket.user)).email : '';

      await sendNewSupportTicketEmail(staffEmails, {
        ticketId: ticket.id,
        title: ticket.title,
        description: ticket.description || 'Pas de description',
        priority: ticket.priority,
        category: ticket.category,
        userEmail: userEmail
      });
    }
  } catch (error) {
    logger.error('Erreur lors de la notification du staff:', error);
  }
};

const handleTicketStateChange = async (oldTicket: SupportTicket, newTicket: SupportTicket, isAutomatic: boolean = false, updatedById: string) => {
  try {
    // Si le statut a changé et qu'on a l'email de l'utilisateur
    if (oldTicket.status !== newTicket.status && newTicket.user && newTicket.user.email) {
      // Déchiffrer les données utilisateur
      const decryptedUser = await decryptUserDataAsync(newTicket.user);

      // Est-ce une réouverture suite à un commentaire? (passe de fermé/résolu à en_cours)
      const isReopenedByComment = (oldTicket.status === 'ferme' || oldTicket.status === 'resolu') && newTicket.status === 'en_cours';
      // Envoyer l'email de changement de statut
      await sendSupportTicketStatusChangeEmail(decryptedUser.email, {
        ticketId: newTicket.id,
        title: newTicket.title,
        oldStatus: oldTicket.status,
        newStatus: newTicket.status,
        userEmail: decryptedUser.email,
        isAutomatic,
        isReopenedByComment,
      });

      // Supprimer les pièces jointes si le statut est fermé ou résolu (même en cas de changement entre les deux)
      if (newTicket.status === 'ferme' || newTicket.status === 'resolu') {
        try {
          // Appel à la fonction qui supprime les pièces jointes
          await deleteTicketAttachments(newTicket.id);
          logger.info(`Pièces jointes du ticket ${newTicket.id} supprimées suite au statut ${newTicket.status}`);
        } catch (error) {
          logger.error(`Erreur lors de la suppression des pièces jointes du ticket ${newTicket.id}:`, error);
        }
      }

      // Créer une notification interne
      try {
        // Déterminer les textes de la notification selon le contexte
        let notificationTitle, notificationContent;

        if (isAutomatic) {
          notificationTitle = 'Fermeture automatique de votre ticket';
          notificationContent = `Votre ticket "${newTicket.title}" a été fermé automatiquement après 30 jours d'inactivité.`;
        } else if (isReopenedByComment) {
          notificationTitle = 'Ticket réouvert suite à votre commentaire';
          notificationContent = `Votre ticket "${newTicket.title}" a été réouvert automatiquement suite à votre commentaire. L'équipe de support a été notifiée.`;
        } else {
          // Formater les statuts pour l'affichage
          const formattedOldStatus = formatTicketStatus(oldTicket.status);
          const formattedNewStatus = formatTicketStatus(newTicket.status);

          notificationTitle = `Statut du ticket mis à jour: ${formattedNewStatus}`;
          notificationContent = `Le statut de votre ticket "${newTicket.title}" a été modifié de "${formattedOldStatus}" à "${formattedNewStatus}".`;
        }

        const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
        const ticketUrl = `${baseUrl}/dashboard/support/ticket/${newTicket.id}`;

        // Insérer la notification
        const { error: notificationError } = await supabase
          .from('user_notifications')
          .insert({
            user_id: newTicket.user_id,
            type: 'system',
            title: notificationTitle,
            content: notificationContent,
            link: ticketUrl,
            is_read: false,
            is_archived: false
          });

        if (notificationError) {
          logger.error('Erreur lors de la création de la notification pour le changement de statut:', notificationError);
        } else {
          // Invalider le cache des notifications pour l'utilisateur
          const cacheKey = `notifications_count:${newTicket.user_id}`;
          await redis.del(cacheKey);

          // Invalider le cache des notifications listées
          const notificationsCacheKeys = await redis.keys(`notifications:${newTicket.user_id}:*`);
          if (notificationsCacheKeys.length > 0) {
            await Promise.all(notificationsCacheKeys.map(key => redis.del(key)));
          }

          logger.info(`Notification interne créée pour l'utilisateur ${newTicket.user_id} - changement de statut du ticket ${newTicket.id}`);
        }
      } catch (notifError) {
        logger.error('Erreur lors de la création de la notification interne:', notifError);
      }
    }

    // Notification d'assignation
    if (oldTicket.assigned_to !== newTicket.assigned_to && newTicket.assignee && newTicket.assignee.email) {
      // Déchiffrer les données de l'assigné
      const decryptedAssignee = await decryptUserDataAsync(newTicket.assignee);

      // Récupérer les informations de l'utilisateur qui a fait l'assignation
      const assignerId = updatedById; // Utiliser l'ID de l'utilisateur qui a fait la mise à jour

      try {
        // Récupérer les informations de l'assignateur
        let assignerInfo = { email: "Support JobPartiel" };
        let formattedName = "Support JobPartiel";

        if (assignerId) {
          const { data: assigner, error: assignerError } = await supabase
            .from('users')
            .select('id, email, user_profil!inner(nom, prenom)')
            .eq('id', assignerId)
            .single();

          if (!assignerError && assigner) {
            // Déchiffrer les données utilisateur
            const decryptedAssigner = await decryptUserDataAsync(assigner);
            assignerInfo = { email: decryptedAssigner.email };

            // Déchiffrer les données du profil
            if (decryptedAssigner.user_profil && decryptedAssigner.user_profil[0]) {
              const decryptedProfile = await decryptProfilDataAsync(decryptedAssigner.user_profil[0]);

              // Formater le nom au format demandé (Prénom I.)
              if (decryptedProfile.prenom && decryptedProfile.nom) {
                const lastInitial = decryptedProfile.nom.charAt(0).toUpperCase();
                formattedName = `${decryptedProfile.prenom} ${lastInitial}.`;
              }
            }
          }
        }

        // Envoyer la notification d'assignation avec les informations de l'assignateur
        await sendSupportTicketAssignedEmail(decryptedAssignee.email, {
          ticketId: newTicket.id,
          title: newTicket.title,
          assigneeEmail: decryptedAssignee.email,
          assignerEmail: assignerInfo.email,
          assignerName: formattedName
        });
      } catch (assignerError) {
        logger.error('Erreur lors de la récupération des informations de l\'assignateur:', assignerError);

        // Envoyer la notification même si on n'a pas réussi à récupérer les infos de l'assignateur
        await sendSupportTicketAssignedEmail(decryptedAssignee.email, {
          ticketId: newTicket.id,
          title: newTicket.title,
          assigneeEmail: decryptedAssignee.email,
          assignerEmail: "Support JobPartiel",
          assignerName: "Support JobPartiel"
        });
      }
    }
  } catch (error) {
    logger.error('Erreur lors de l\'envoi des notifications:', error);
  }
};

export const deleteTicket = async (req: Request, res: Response) => {
  try {
    const { ticketId } = req.params;
    const userId = req.user?.userId;
    const isStaff = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';

    if (!userId) {
      res.status(401).json({ error: 'Non authentifié' });
      return;
    }

    // Vérifier l'existence du ticket
    const { data: ticket, error: fetchError } = await supabase
      .from('support_tickets')
      .select('*')
      .eq('id', ticketId)
      .single();

    if (fetchError) {
      logger.error('Erreur lors de la récupération du ticket:', fetchError);
      res.status(500).json({ error: 'Erreur lors de la récupération du ticket' });
      return;
    }

    if (!ticket) {
      res.status(404).json({ error: 'Ticket non trouvé' });
      return;
    }

    // Vérifier les permissions (le staff peut supprimer n'importe quel ticket)
    // Les utilisateurs peuvent supprimer leurs propres tickets dans les 30 minutes
    if (!isStaff) {
      // Vérifier si l'utilisateur est propriétaire du ticket
      if (ticket.user_id !== userId) {
        res.status(403).json({ error: 'Non autorisé à supprimer ce ticket' });
        return;
      }

      // Vérifier si le ticket a été créé il y a moins de 30 minutes
      const ticketCreatedAt = new Date(ticket.created_at);
      const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);

      if (ticketCreatedAt < thirtyMinutesAgo) {
        res.status(403).json({
          error: 'Délai dépassé',
          message: 'Vous ne pouvez supprimer votre ticket que dans les 30 minutes suivant sa création'
        });
        return;
      }
    }

    // Journaliser l'action de suppression pour audit
    const actionMessage = isStaff
      ? `Support JobPartiel a supprimé le ticket ${ticketId}`
      : `Utilisateur ${userId} a supprimé son propre ticket ${ticketId}`;

    logSecurityEvent({
      action: 'delete_ticket',
      ip: req.ip || 'unknown',
      status: 'success',
      message: actionMessage
    });

    // Supprimer les pièces jointes de Supabase Storage et de la base de données
    try {
      await deleteTicketAttachments(ticketId);
      logger.info(`Pièces jointes du ticket ${ticketId} supprimées avec succès`);
    } catch (attachmentError) {
      logger.error('Erreur lors de la suppression des pièces jointes:', attachmentError);
      // Continuer malgré l'erreur pour supprimer le ticket
    }

    // Supprimer les commentaires
    await supabase.from('support_ticket_comments').delete().eq('ticket_id', ticketId);

    // Supprimer les relations de tags s'il y en a
    try {
      await supabase.from('support_ticket_tag_relations').delete().eq('ticket_id', ticketId);
    } catch (tagsError) {
      logger.error('Erreur lors de la suppression des relations de tags:', tagsError);
      // Continuer malgré l'erreur
    }

    // Supprimer le ticket
    const { error } = await supabase.from('support_tickets').delete().eq('id', ticketId);

    if (error) {
      logger.error('Erreur lors de la suppression du ticket:', error);
      res.status(500).json({ error: 'Erreur lors de la suppression du ticket' });
      return;
    }

    // Invalider le cache
    await ticketCacheService.invalidateTicket(ticketId);
    await redis.del(STATS_CACHE_KEY);

    // Journaliser l'action avec logUserActivity
    await logUserActivity(
      userId,
      'support_ticket_delete',
      ticketId,
      'support_ticket',
      {
        title: ticket.title,
        isStaff: isStaff
      },
      getIpFromRequest(req)
    );

    res.json({ message: 'Ticket supprimé avec succès' });
  } catch (error) {
    logger.error('Erreur lors de la suppression du ticket:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Exporter la fonction handleTicketStateChange
export { handleTicketStateChange };