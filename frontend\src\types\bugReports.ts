export type ReportType = 'bug' | 'improvement';
export type ReportCategory = 'interface' | 'fonctionnalite' | 'paiement' | 'securite' | 'autre';
export type ReportPriority = 'faible' | 'moyenne' | 'elevee' | 'critique';
export type ReportStatus = 'nouveau' | 'en_cours' | 'resolu' | 'rejete' | 'ferme' | 'reouvert' | 'attente_moderation';
export type VoteType = 'pour' | 'contre';

export interface BrowserInfo {
  name: string;
  version: string;
  mobile: boolean;
  os: string;
}

export interface BugReport {
  id: string;
  user_id: string;
  user?: {
    id: string;
    email: string;
  };
  title: string;
  description: string;
  report_type: ReportType;
  category: ReportCategory;
  priority: ReportPriority;
  status: ReportStatus;
  is_private: boolean;
  admin_comment?: string;
  reproduction_steps?: string;
  browser_info?: BrowserInfo;
  os_info?: string;
  assigned_to?: string;
  assigned?: {
    id: string;
    email: string;
  };
  resolved_at?: string;
  created_at: string;
  updated_at: string;
  votes?: BugReportVote[];
  history?: BugReportHistory[];
  comments?: BugReportComment[];
}

export interface BugReportHistory {
  id: string;
  bug_report_id: string;
  updated_by?: string;
  updater?: {
    id: string;
    email: string;
  };
  old_status?: ReportStatus;
  new_status: ReportStatus;
  comment?: string;
  created_at: string;
}

export interface BugReportVote {
  id: string;
  bug_report_id: string;
  user_id: string;
  vote_type: VoteType;
  comment?: string;
  created_at: string;
}

export interface BugReportComment {
  id: string;
  bug_report_id: string;
  user_id: string;
  user?: {
    id: string;
    email: string;
  };
  parent_comment_id?: string;
  parent?: {
    id: string;
    message: string;
    user_id: string;
    user?: {
      id: string;
      email: string;
    }
  };
  message: string;
  created_at: string;
  updated_at: string;
  is_admin: boolean;
}

export interface BugReportCommentRequest {
  message: string;
  parent_comment_id?: string;
}

export interface BugReportVoteCount {
  pour_count: number;
  contre_count: number;
}

export interface BugReportCreateRequest {
  title: string;
  description: string;
  report_type: ReportType;
  category: ReportCategory;
  priority: ReportPriority;
  is_private?: boolean;
  reproduction_steps?: string;
  browser_info?: BrowserInfo;
  os_info?: string;
}

export interface BugReportUpdateRequest {
  title?: string;
  description?: string;
  report_type?: ReportType;
  category?: ReportCategory;
  priority?: ReportPriority;
  status?: ReportStatus;
  is_private?: boolean;
  admin_comment?: string;
  reproduction_steps?: string;
  browser_info?: BrowserInfo;
  os_info?: string;
  assigned_to?: string;
}

export interface BugReportVoteRequest {
  vote_type: VoteType;
  comment?: string;
}

export interface BugReportResponse {
  data: BugReport[];
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

export interface BugReportFilters {
  type?: ReportType;
  category?: ReportCategory;
  status?: ReportStatus;
  priority?: ReportPriority;
  search?: string;
  page?: number;
  limit?: number;
  order?: string;
  direction?: 'asc' | 'desc';
  user_id?: string;
  show_pending?: boolean;
}

export interface BugReportStats {
  status: Array<{
    status: ReportStatus;
    count: number;
  }>;
  type: Array<{
    report_type: ReportType;
    count: number;
  }>;
  category: Array<{
    category: ReportCategory;
    count: number;
  }>;
  priority: Array<{
    priority: ReportPriority;
    count: number;
  }>;
} 