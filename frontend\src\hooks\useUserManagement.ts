import { useState, useCallback } from 'react';
import { getCommonHeaders } from '../utils/headers';
import { fetchCsrfToken } from '../services/csrf';
import { API_CONFIG } from '../config/api';
import {
  User,
  UserDetailData,
  UserStats,
  UserFilters,
  Pagination,
  JobiManagementRequest,
  AiCreditsManagementRequest,
  SubscriptionManagementRequest,
  UserActionRequest,
  ActionResponse
} from '../types/userManagement';

interface UseUserManagementReturn {
  // États
  users: User[];
  selectedUser: UserDetailData | null;
  userStats: UserStats | null;
  loading: boolean;
  error: string | null;
  pagination: Pagination;
  
  // Actions
  fetchUsers: (filters?: Partial<UserFilters>, page?: number, limit?: number) => Promise<void>;
  fetchUserDetails: (userId: string) => Promise<void>;
  fetchUserStats: (userId: string, period?: string) => Promise<void>;
  manageJobi: (userId: string, data: JobiManagementRequest) => Promise<ActionResponse>;
  manageAiCredits: (userId: string, data: AiCreditsManagementRequest) => Promise<ActionResponse>;
  manageSubscription: (userId: string, data: SubscriptionManagementRequest) => Promise<ActionResponse>;
  performUserAction: (userId: string, data: UserActionRequest) => Promise<ActionResponse>;
  manageUserPhotos: (userId: string, data: any) => Promise<ActionResponse>;
  manageUserBadges: (userId: string, data: any) => Promise<ActionResponse>;
  manageUserMessages: (userId: string, data: any) => Promise<ActionResponse>;
  getUserMissions: (userId: string) => Promise<any>;
  generateUserReport: (userId: string, reportType?: string, timeRange?: string) => Promise<any>;
  clearError: () => void;
  clearSelectedUser: () => void;
}

export const useUserManagement = (): UseUserManagementReturn => {
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<UserDetailData | null>(null);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<Pagination>({
    page: 0,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const clearSelectedUser = useCallback(() => {
    setSelectedUser(null);
    setUserStats(null);
  }, []);

  const fetchUsers = useCallback(async (
    filters: Partial<UserFilters> = {},
    page: number = 0,
    limit: number = 20
  ) => {
    try {
      setLoading(true);
      setError(null);

      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const params = new URLSearchParams({
        page: (page + 1).toString(),
        limit: limit.toString(),
        search: filters.search || '',
        role: filters.role || '',
        status: filters.status || '',
        userType: filters.userType || '',
        sortBy: filters.sortBy || 'created_at',
        sortOrder: filters.sortOrder || 'desc'
      });

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users?${params}`,
        {
          method: 'GET',
          headers,
          credentials: 'include'
        }
      );

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setUsers(data.data.users);
        setPagination({
          page,
          limit,
          total: data.data.pagination.total,
          totalPages: data.data.pagination.totalPages
        });
      } else {
        throw new Error(data.message || 'Erreur lors de la récupération des utilisateurs');
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des utilisateurs:', error);
      setError(error instanceof Error ? error.message : 'Erreur inconnue');
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchUserDetails = useCallback(async (userId: string) => {
    try {
      setLoading(true);
      setError(null);

      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${userId}`,
        {
          method: 'GET',
          headers,
          credentials: 'include'
        }
      );

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setSelectedUser(data.data);
      } else {
        throw new Error(data.message || 'Erreur lors de la récupération des détails utilisateur');
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des détails utilisateur:', error);
      setError(error instanceof Error ? error.message : 'Erreur inconnue');
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchUserStats = useCallback(async (userId: string, period: string = '30') => {
    try {
      setLoading(true);
      setError(null);

      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${userId}/stats?period=${period}`,
        {
          method: 'GET',
          headers,
          credentials: 'include'
        }
      );

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setUserStats(data.data);
      } else {
        throw new Error(data.message || 'Erreur lors de la récupération des statistiques');
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      setError(error instanceof Error ? error.message : 'Erreur inconnue');
    } finally {
      setLoading(false);
    }
  }, []);

  const manageJobi = useCallback(async (
    userId: string, 
    data: JobiManagementRequest
  ): Promise<ActionResponse> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${userId}/jobi`,
        {
          method: 'POST',
          headers,
          credentials: 'include',
          body: JSON.stringify(data)
        }
      );

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || 'Erreur lors de la gestion des Jobi');
      }

      return result;
    } catch (error) {
      console.error('Erreur lors de la gestion des Jobi:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Erreur inconnue',
        toastType: 'error'
      };
    }
  }, []);

  const manageAiCredits = useCallback(async (
    userId: string, 
    data: AiCreditsManagementRequest
  ): Promise<ActionResponse> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${userId}/ai-credits`,
        {
          method: 'POST',
          headers,
          credentials: 'include',
          body: JSON.stringify(data)
        }
      );

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || 'Erreur lors de la gestion des crédits IA');
      }

      return result;
    } catch (error) {
      console.error('Erreur lors de la gestion des crédits IA:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Erreur inconnue',
        toastType: 'error'
      };
    }
  }, []);

  const manageSubscription = useCallback(async (
    userId: string, 
    data: SubscriptionManagementRequest
  ): Promise<ActionResponse> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${userId}/subscription`,
        {
          method: 'POST',
          headers,
          credentials: 'include',
          body: JSON.stringify(data)
        }
      );

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || 'Erreur lors de la gestion de l\'abonnement');
      }

      return result;
    } catch (error) {
      console.error('Erreur lors de la gestion de l\'abonnement:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Erreur inconnue',
        toastType: 'error'
      };
    }
  }, []);

  const performUserAction = useCallback(async (
    userId: string, 
    data: UserActionRequest
  ): Promise<ActionResponse> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${userId}/actions`,
        {
          method: 'POST',
          headers,
          credentials: 'include',
          body: JSON.stringify(data)
        }
      );

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || 'Erreur lors de l\'action utilisateur');
      }

      return result;
    } catch (error) {
      console.error('Erreur lors de l\'action utilisateur:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Erreur inconnue',
        toastType: 'error'
      };
    }
  }, []);

  const manageUserPhotos = useCallback(async (
    userId: string,
    data: any
  ): Promise<ActionResponse> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${userId}/photos`,
        {
          method: 'POST',
          headers,
          credentials: 'include',
          body: JSON.stringify(data)
        }
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Erreur lors de la gestion des photos');
      }

      return result;
    } catch (error) {
      console.error('Erreur lors de la gestion des photos:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Erreur inconnue',
        toastType: 'error'
      };
    }
  }, []);

  const manageUserBadges = useCallback(async (
    userId: string,
    data: any
  ): Promise<ActionResponse> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${userId}/badges`,
        {
          method: 'POST',
          headers,
          credentials: 'include',
          body: JSON.stringify(data)
        }
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Erreur lors de la gestion des badges');
      }

      return result;
    } catch (error) {
      console.error('Erreur lors de la gestion des badges:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Erreur inconnue',
        toastType: 'error'
      };
    }
  }, []);

  const manageUserMessages = useCallback(async (
    userId: string,
    data: any
  ): Promise<ActionResponse> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${userId}/messages`,
        {
          method: 'POST',
          headers,
          credentials: 'include',
          body: JSON.stringify(data)
        }
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Erreur lors de la gestion des messages');
      }

      return result;
    } catch (error) {
      console.error('Erreur lors de la gestion des messages:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Erreur inconnue',
        toastType: 'error'
      };
    }
  }, []);

  const generateUserReport = useCallback(async (
    userId: string,
    reportType: string = 'complete',
    timeRange: string = '90'
  ) => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${userId}/report?reportType=${reportType}&timeRange=${timeRange}`,
        {
          method: 'GET',
          headers,
          credentials: 'include'
        }
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Erreur lors de la génération du rapport');
      }

      return result;
    } catch (error) {
      console.error('Erreur lors de la génération du rapport:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Erreur inconnue',
        toastType: 'error'
      };
    }
  }, []);

  const getUserMissions = useCallback(async (userId: string) => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${userId}/missions`,
        {
          method: 'GET',
          headers,
          credentials: 'include'
        }
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Erreur lors de la récupération des missions');
      }

      return result;
    } catch (error) {
      console.error('Erreur lors de la récupération des missions:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Erreur inconnue',
        toastType: 'error'
      };
    }
  }, []);

  return {
    // États
    users,
    selectedUser,
    userStats,
    loading,
    error,
    pagination,

    // Actions
    fetchUsers,
    fetchUserDetails,
    fetchUserStats,
    manageJobi,
    manageAiCredits,
    manageSubscription,
    performUserAction,
    manageUserPhotos,
    manageUserBadges,
    manageUserMessages,
    getUserMissions,
    generateUserReport,
    clearError,
    clearSelectedUser
  };
};

export default useUserManagement;
