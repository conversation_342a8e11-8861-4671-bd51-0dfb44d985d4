import { Request, Response, NextFunction } from 'express';
import { z, ZodError, ZodSchema } from 'zod';
import logger from './logger';

// Schémas de validation
const emailSchema: ZodSchema<string> = z.string()
  .email('Email invalide')
  .refine((email) => !email.includes('@supprime.local'), {
    message: 'Les adresses email anonymisées ne sont pas autorisées (RGPD)'
  });
const passwordSchema: ZodSchema<string> = z.string()
  .min(8, 'Le mot de passe doit contenir au moins 8 caractères')
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/, 
    'Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial');
const usernameSchema: ZodSchema<string> = z.string()
  .min(3, 'Le nom d\'utilisateur doit contenir au moins 3 caractères')
  .max(30, 'Le nom d\'utilisateur ne doit pas dépasser 30 caractères')
  .regex(/^[a-zA-Z0-9_-]+$/, 'Le nom d\'utilisateur ne doit contenir que des lettres, chiffres, tirets et underscores');

// Schémas composés pour différentes routes
const loginSchema: ZodSchema<{ email: string; password: string }> = z.object({
  email: emailSchema,
  password: passwordSchema,
});

const registerSchema: ZodSchema<{ username: string; email: string; password: string }> = z.object({
  username: usernameSchema,
  email: emailSchema,
  password: passwordSchema,
});

const updateProfilSchema: ZodSchema<{ username?: string; email?: string; currentPassword?: string; newPassword?: string }> = z.object({
  username: usernameSchema.optional(),
  email: emailSchema.optional(),
  currentPassword: passwordSchema.optional(),
  newPassword: passwordSchema.optional(),
}).refine((data) => {
  // Si newPassword est présent, currentPassword doit l'être aussi
  if (data.newPassword && !data.currentPassword) {
    return false;
  }
  return true;
}, {
  message: "Le mot de passe actuel est requis pour changer de mot de passe",
  path: ["currentPassword"],
});

const resetPasswordSchema: ZodSchema<{ token: string; password: string }> = z.object({
  token: z.string().min(32, 'Token invalide'),
  password: passwordSchema
});

const validateInput = <T>(schema: ZodSchema<T>, data: unknown): z.SafeParseReturnType<unknown, T> => {
  try {
    return schema.safeParse(data);
  } catch (error) {
    if (error instanceof ZodError) {
      return { success: false, error };
    }
    throw error;
  }
};

export const validateSchema = (schema: ZodSchema) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const result = validateInput(schema, req.body);
      if (!result.success) {
        res.status(400).json({
          message: 'Validation failed',
          errors: result.error.errors.map(err => ({
            path: err.path.join('.'),
            message: err.message
          }))
        });
        return;
      }
      req.body = result.data;
      next();
    } catch (error) {
      logger.error('Validation error:', error);
      res.status(500).json({
        message: 'Internal server error during validation'
      });
      return;
    }
  };
};

// Middleware de validation
export const validateLogin = validateSchema(loginSchema);
export const validateRegister = validateSchema(registerSchema);
export const validateProfilUpdate = validateSchema(updateProfilSchema);
export const validateResetPassword = validateSchema(resetPasswordSchema);

// Validation des paramètres d'URL
export const validateUrlParams = (schema: ZodSchema) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedParams = await schema.parseAsync(req.params);
      req.params = validatedParams;
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        res.status(400).json({ 
          message: 'Paramètres d\'URL invalides',
          errors: error.errors.map(err => ({
            path: err.path.join('.'),
            message: err.message
          }))
        });
      }
      next(error);
    }
  };
};

// Validation des query parameters
export const validateQueryParams = (schema: ZodSchema) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedQuery = await schema.parseAsync(req.query);
      req.query = validatedQuery;
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        res.status(400).json({ 
          message: 'Paramètres de requête invalides',
          errors: error.errors.map(err => ({
            path: err.path.join('.'),
            message: err.message
          }))
        });
      }
      next(error);
    }
  };
};

// Utilitaire pour gérer les fonctions async dans les routes Express
export function asyncHandler(fn: any) {
  return function (req: any, res: any, next: any) {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}
