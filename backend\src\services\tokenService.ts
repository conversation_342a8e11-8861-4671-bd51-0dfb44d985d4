import { SignOptions } from 'jsonwebtoken';
import jwt from 'jsonwebtoken';
import { redis } from '../config/redis';
import logger from '../utils/logger';
import { supabase } from '../config/supabase';
import config from '../config'; // Import de la configuration
import crypto from 'crypto';

// Types
export type TokenType = 'access' | 'refresh';
export type BlacklistReason = 'logout' | 'password_change' | 'suspension' | 'security_breach' | 'token_expired';

interface TokenPayload {
  userId: string;
  email: string;
  userType?: string;
  role?: string;
  jti: string;
}

interface BlacklistEntry {
  token: string;
  reason: BlacklistReason;
  timestamp: number;
  userId: string;
}

interface TokenValidationResult {
  isValid: boolean;
  error?: string;
  decoded?: TokenPayload;
}

class TokenService {
  private static instance: TokenService;
  private readonly BLACKLIST_PREFIX = 'token_blacklist:';
  private readonly USER_ROLE_PREFIX = 'user_role:';
  private readonly USER_ROLE_TTL = 86400; // 24 heures en secondes
  private readonly TOKEN_EXPIRY = {
    access: '1h',    // 1 heure
    refresh: '7d'     // 7 jours
  };

  private constructor() {}

  public static getInstance(): TokenService {
    if (!TokenService.instance) {
      TokenService.instance = new TokenService();
    }
    return TokenService.instance;
  }

  // Génération de tokens
  public async generateTokens(userId: string, email: string, userType: string = 'jobbeur'): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      // Vérifier si le rôle de l'utilisateur est déjà en cache
      const roleCacheKey = `${this.USER_ROLE_PREFIX}${userId}`;
      let roleFromCache = await redis.get(roleCacheKey);
      let fromCache = true;
      let role: string;

      // Si le rôle n'est pas en cache, le récupérer depuis la base de données
      if (!roleFromCache) {
        fromCache = false;
        const { data, error } = await supabase
          .from('users')
          .select('role')
          .eq('id', userId)
          .limit(1);

        if (error) {
          logger.error('Erreur lors de la récupération du rôle utilisateur:', error);
          role = 'jobutil'; // Valeur par défaut en cas d'erreur
        } else {
          role = data?.[0]?.role || 'jobutil';

          // Stocker le rôle en cache pour les futures requêtes
          await redis.set(roleCacheKey, role, 'EX', this.USER_ROLE_TTL);
        }
      } else {
        role = roleFromCache;
      }

      logger.info(`Génération de tokens pour l'utilisateur ${userId} avec le rôle ${role} (${fromCache ? 'depuis cache' : 'depuis DB'})`);

      const accessToken = await this.generateToken(userId, email, userType, role, 'access');
      const refreshToken = await this.generateToken(userId, email, userType, role, 'refresh');

      return { accessToken, refreshToken };
    } catch (error) {
      logger.error('Erreur lors de la génération des tokens:', error);
      throw new Error('Erreur lors de la génération des tokens');
    }
  }

  // Vérification de token avec gestion de la blacklist
  public async verifyToken(token: string, type: TokenType): Promise<TokenValidationResult> {
    try {
      // Vérifier d'abord si le token est dans la blacklist
      const isBlacklisted = await this.isTokenBlacklisted(token);
      if (isBlacklisted) {
        return {
          isValid: false,
          error: 'Token révoqué'
        };
      }

      // Vérifier la validité du token
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as TokenPayload;

      return {
        isValid: true,
        decoded
      };
    } catch (error) {
      let userId: string | undefined;

      // Essayer de décoder le token même s'il est expiré
      try {
        const decoded = jwt.decode(token) as TokenPayload;
        userId = decoded?.userId;
      } catch {
        // Ignorer l'erreur si le token ne peut pas être décodé
      }

      if (error instanceof jwt.TokenExpiredError) {
        await this.blacklistToken(token, 'token_expired', userId);
      }

      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Token invalide'
      };
    }
  }

  // Gestion de la blacklist
  public async blacklistToken(token: string, reason: BlacklistReason, userId?: string): Promise<void> {
    try {
      const entry: BlacklistEntry = {
        token,
        reason,
        timestamp: Date.now(),
        userId: userId || 'unknown'
      };

      const key = `${this.BLACKLIST_PREFIX}${token}`;
      await redis.set(key, JSON.stringify(entry), 'EX', 86400); // 24h

      logger.info('Token blacklisté:', {
        reason,
        userId,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Erreur lors du blacklist du token:', error);
      throw new Error('Erreur lors du blacklist du token');
    }
  }

  public async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const key = `${this.BLACKLIST_PREFIX}${token}`;
      const result = await redis.get(key);
      return !!result;
    } catch (error) {
      logger.error('Erreur lors de la vérification de la blacklist:', error);
      return true; // Par sécurité, considérer le token comme blacklisté en cas d'erreur
    }
  }

  public async getBlacklistEntry(token: string): Promise<BlacklistEntry | null> {
    try {
      const key = `${this.BLACKLIST_PREFIX}${token}`;
      const entry = await redis.get(key);
      return entry ? JSON.parse(entry) : null;
    } catch (error) {
      logger.error('Erreur lors de la récupération de l\'entrée de la blacklist:', error);
      return null;
    }
  }

  // Invalidation de tous les tokens d'un utilisateur
  public async invalidateAllUserTokens(userId: string, reason: BlacklistReason): Promise<void> {
    try {
      // Rechercher tous les tokens de l'utilisateur
      const pattern = `${this.BLACKLIST_PREFIX}*`;
      const keys = await redis.keys(pattern);

      for (const key of keys) {
        const entry = await this.getBlacklistEntry(key.replace(this.BLACKLIST_PREFIX, ''));
        if (entry && entry.userId === userId) {
          await this.blacklistToken(entry.token, reason, userId);
        }
      }

      logger.info('Tous les tokens de l\'utilisateur ont été invalidés:', {
        userId,
        reason,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Erreur lors de l\'invalidation des tokens:', error);
      throw new Error('Erreur lors de l\'invalidation des tokens');
    }
  }

  // Nettoyage de la blacklist
  public async cleanupBlacklist(): Promise<void> {
    try {
      const pattern = `${this.BLACKLIST_PREFIX}*`;
      const keys = await redis.keys(pattern);

      for (const key of keys) {
        const entry = await this.getBlacklistEntry(key.replace(this.BLACKLIST_PREFIX, ''));
        if (entry && Date.now() - entry.timestamp > 86400000) { // 24h
          await redis.del(key);
        }
      }

      logger.info('Nettoyage de la blacklist effectué');
    } catch (error) {
      logger.error('Erreur lors du nettoyage de la blacklist:', error);
    }
  }

  // Méthodes privées
  public async generateToken(userId: string, email: string, userType: string, role: string, type: TokenType): Promise<string> {
    const jti = this.generateTokenId();

    // Ajouter des informations supplémentaires pour renforcer la sécurité
    const payload: TokenPayload = {
      userId,
      email,
      userType,
      role,
      jti
    };

    // Récupérer l'IP et l'agent utilisateur si disponibles (à implémenter dans un middleware)
    // Ces informations peuvent être stockées dans Redis et récupérées ici

    return jwt.sign(
      payload,
      process.env.JWT_SECRET || '',
      {
        expiresIn: type === 'access' && config.jwt.socketExpiresIn ? config.jwt.socketExpiresIn : this.TOKEN_EXPIRY[type],
        issuer: process.env.JWT_ISSUER || 'jobpartiel-auth',
        algorithm: 'HS256',
        audience: process.env.JWT_AUDIENCE || 'jobpartiel-users',
        subject: userId,
        notBefore: 0, // Le token est valide immédiatement
      } as SignOptions
    );
  }

  private generateTokenId(): string {
    // Utiliser crypto pour générer un ID de token plus sécurisé
    const timestamp = Date.now();
    const random = crypto.randomBytes(16).toString('hex');
    return `${timestamp}-${random}`;
  }

  // Méthode pour invalider le cache de rôle utilisateur
  // À appeler lorsque le rôle d'un utilisateur change
  public async invalidateUserRoleCache(userId: string): Promise<void> {
    try {
      const roleCacheKey = `${this.USER_ROLE_PREFIX}${userId}`;
      await redis.del(roleCacheKey);
      logger.info(`Cache de rôle invalidé pour l'utilisateur ${userId}`);
    } catch (error) {
      logger.error('Erreur lors de l\'invalidation du cache de rôle:', error);
    }
  }
}

export const tokenService = TokenService.getInstance();
