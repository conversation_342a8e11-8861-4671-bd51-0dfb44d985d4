import { useState, useEffect } from 'react';
import { api } from '../../services/api';
import { logger } from '../../utils/logger';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Pagination,
  Box,
  Typography,
  Chip,
  // IconButton,
  // Tooltip
} from '@mui/material';
import { Search, RefreshCw, Download } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { notify } from '../../components/Notification';

interface Subscriber {
  id: string;
  email: string;
  is_verified: boolean;
  subscribed_at: string;
  verified_at: string | null;
  created_at: string;
  updated_at: string;
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

const NewsletterSubscribersPage = () => {
  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [verified, setVerified] = useState<string>('');
  const [page, setPage] = useState(1);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit: 20,
    pages: 0
  });

  const fetchSubscribers = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/newsletter/subscribers', {
        params: {
          page,
          limit: pagination.limit,
          search: search || undefined,
          verified: verified || undefined
        }
      });

      if (response.data.success) {
        setSubscribers(response.data.subscribers);
        setPagination(response.data.pagination);
      } else {
        notify('Erreur lors de la récupération des abonnés', 'error');
      }
    } catch (error) {
      logger.error('Erreur lors de la récupération des abonnés:', error);
      notify('Erreur lors de la récupération des abonnés', 'error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSubscribers();
  }, [page, verified]);

  const handleSearch = () => {
    setPage(1);
    fetchSubscribers();
  };

  const handleReset = () => {
    setSearch('');
    setVerified('');
    setPage(1);
    fetchSubscribers();
  };

  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  const exportToCSV = () => {
    try {
      // Créer les en-têtes CSV
      const headers = ['Email', 'Statut', 'Date d\'inscription', 'Date de vérification'];

      // Convertir les données en lignes CSV
      const rows = subscribers.map(sub => [
        sub.email,
        sub.is_verified ? 'Vérifié' : 'Non vérifié',
        format(new Date(sub.subscribed_at), 'dd/MM/yyyy HH:mm'),
        sub.verified_at ? format(new Date(sub.verified_at), 'dd/MM/yyyy HH:mm') : 'Non vérifié'
      ]);

      // Combiner les en-têtes et les lignes
      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.join(','))
      ].join('\n');

      // Créer un blob et un lien de téléchargement
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `abonnes-newsletter-${format(new Date(), 'yyyy-MM-dd')}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      notify('Export CSV réussi', 'success');
    } catch (error) {
      logger.error('Erreur lors de l\'export CSV:', error);
      notify('Erreur lors de l\'export CSV', 'error');
    }
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <Typography variant="h4" component="h1" gutterBottom>
          Abonnés à la Newsletter
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<Download />}
          onClick={exportToCSV}
          disabled={subscribers.length === 0}
        >
          Exporter CSV
        </Button>
      </div>

      <Paper className="p-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4 items-end">
          <TextField
            label="Rechercher par email"
            variant="outlined"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="flex-grow"
            size="small"
          />
          <FormControl variant="outlined" className="min-w-[200px]" size="small">
            <InputLabel>Statut</InputLabel>
            <Select
              value={verified}
              onChange={(e) => setVerified(e.target.value as string)}
              label="Statut"
            >
              <MenuItem value="">Tous</MenuItem>
              <MenuItem value="true">Vérifiés</MenuItem>
              <MenuItem value="false">Non vérifiés</MenuItem>
            </Select>
          </FormControl>
          <div className="flex gap-2">
            <Button
              variant="contained"
              color="primary"
              onClick={handleSearch}
              startIcon={<Search />}
            >
              Rechercher
            </Button>
            <Button
              variant="outlined"
              onClick={handleReset}
              startIcon={<RefreshCw />}
            >
              Réinitialiser
            </Button>
          </div>
        </div>
      </Paper>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Email</TableCell>
              <TableCell>Statut</TableCell>
              <TableCell>Date d'inscription</TableCell>
              <TableCell>Date de vérification</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={4} align="center" className="py-8">
                  <div className="flex justify-center items-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#FF7A35]"></div>
                    <span className="ml-2">Chargement...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : subscribers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} align="center" className="py-8">
                  Aucun abonné trouvé
                </TableCell>
              </TableRow>
            ) : (
              subscribers.map((subscriber) => (
                <TableRow key={subscriber.id}>
                  <TableCell>{subscriber.email}</TableCell>
                  <TableCell>
                    <Chip
                      label={subscriber.is_verified ? "Vérifié" : "Non vérifié"}
                      color={subscriber.is_verified ? "success" : "warning"}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {format(new Date(subscriber.subscribed_at), 'dd MMMM yyyy à HH:mm', { locale: fr })}
                  </TableCell>
                  <TableCell>
                    {subscriber.verified_at
                      ? format(new Date(subscriber.verified_at), 'dd MMMM yyyy à HH:mm', { locale: fr })
                      : "Non vérifié"}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {pagination.pages > 1 && (
        <Box display="flex" justifyContent="center" mt={4}>
          <Pagination
            count={pagination.pages}
            page={page}
            onChange={handlePageChange}
            color="primary"
          />
        </Box>
      )}

      <Box mt={2} display="flex" justifyContent="flex-end">
        <Typography variant="body2" color="textSecondary">
          Total: {pagination.total} abonné{pagination.total !== 1 ? 's' : ''}
        </Typography>
      </Box>
    </div>
  );
};

export default NewsletterSubscribersPage;
