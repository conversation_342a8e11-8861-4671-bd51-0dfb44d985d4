import { Request, Response } from 'express';
import vpsService from '../services/vpsService';
import logger from '../utils/logger';

export const vpsController = {
  getVPSInfo: async (req: Request, res: Response) => {
    try {
      const info = await vpsService.getVPSInfo();
      res.json(info);
    } catch (error) {
      logger.error('Error in getVPSInfo controller:', error);
      res.status(500).json({ error: 'Erreur lors de la récupération des informations VPS' });
    }
  },

  getVPSMetrics: async (req: Request, res: Response) => {
    try {
      const { dateFrom, dateTo } = req.query;
      
      if (!dateFrom || !dateTo) {
        res.status(400).json({ error: 'Les dates sont requises' });
      }

      const metrics = await vpsService.getVPSMetrics(
        new Date(dateFrom as string),
        new Date(dateTo as string)
      );
      res.json(metrics);
    } catch (error) {
      logger.error('Error in getVPSMetrics controller:', error);
      res.status(500).json({ error: 'Erreur lors de la récupération des métriques VPS' });
    }
  },

  getVPSHistory: async (req: Request, res: Response) => {
    try {
      const history = await vpsService.getVPSHistory();
      res.json(history);
    } catch (error) {
      logger.error('Error in getVPSHistory controller:', error);
      res.status(500).json({ error: 'Erreur lors de la récupération de l\'historique VPS' });
    }
  },

  restartVPS: async (req: Request, res: Response) => {
    try {
      const result = await vpsService.restartVPS();
      res.json(result);
    } catch (error) {
      logger.error('Error in restartVPS controller:', error);
      res.status(500).json({ error: 'Erreur lors du redémarrage du VPS' });
    }
  },
}; 