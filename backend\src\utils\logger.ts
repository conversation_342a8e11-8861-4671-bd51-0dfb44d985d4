import winston from 'winston';

// Configuration des niveaux de log personnalisés pour la sécurité
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  security: 3,
  debug: 4,
};

// Configuration des couleurs pour la console
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  security: 'magenta',
  debug: 'blue',
};

// Ajout des couleurs à winston
winston.addColors(colors);

// Création du format personnalisé
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Fonction pour déterminer la sévérité en fonction du niveau
function getSeverity(level: string): 'low' | 'medium' | 'high' {
  switch (level) {
    case 'error':
      return 'high';
    case 'warn':
      return 'medium';
    default:
      return 'low';
  }
}

// Création du logger avec les niveaux personnalisés
const logger = winston.createLogger({
  levels,
  level: process.env.LOG_LEVEL || 'info',
  format: format,
  transports: [
    // Transport console par défaut
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    })
  ],
});

// Ajout d'un transport fichier en production
if (process.env.NODE_ENV === 'production') {
  logger.add(new winston.transports.File({
    filename: 'logs/error.log',
    level: 'error',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    )
  }));
  
  logger.add(new winston.transports.File({
    filename: 'logs/combined.log',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    )
  }));
}

// Interface pour les événements de sécurité
export interface SecurityEvent {
  action: string;
  ip: string;
  status: string;
  message: string;
  userId?: string;
}

// Fonction pour logger les événements de sécurité
export const logSecurityEvent = (event: SecurityEvent) => {
  logger.log('security', {
    timestamp: new Date().toISOString(),
    ...event,
  });
};

// Fonction pour logger dans Supabase (à utiliser après l'initialisation de Supabase)
export const logToSupabase = async (level: string, message: string, meta: any = {}) => {
  try {
    // Import dynamique pour éviter la dépendance circulaire
    const { supabase } = await import('../config/supabase');
    
    const { userId, ...otherMeta } = meta;
    if (getSeverity(level) !== 'low') {
      await supabase.from('security_logs').insert({
        type: level.toUpperCase(),
        severity: getSeverity(level),
        user_id: userId || null,
        details: {
          message,
          ...otherMeta
        },
        created_at: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Erreur lors de l\'enregistrement du log dans Supabase:', error);
  }
};

export default logger;
