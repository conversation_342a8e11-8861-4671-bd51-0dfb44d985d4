import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { logUserActivity, getIpFromRequest } from '../utils/activityLogger';
import { getUserSubscriptionLimits } from '../routes/configSubscriptions';
import { decryptProfilDataAsync, decryptUserDataAsync } from '../utils/encryption';

export class FavoritesController {
  // Durée de vie du cache en secondes (5 minutes)
  private CACHE_TTL = 300;

  // Récupérer tous les favoris de l'utilisateur
  async getFavorites(req: Request, res: Response) {
    try {
      if (!req.user?.userId) {
        res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
        return;
      }

      // Récupérer la limite de favoris selon l'abonnement
      const limits = await getUserSubscriptionLimits(req.user.userId);
      const favoriteLimit = limits.isPremium ? 60 : 3;

      const page = parseInt(req.query.page as string) || 1;
      const limit = 10;
      const offset = (page - 1) * limit;

      // Clé de cache pour les favoris de l'utilisateur
      const cacheKey = `favorites:user:${req.user.userId}:page:${page}`;

      // Vérifier si les données sont en cache
      const cachedData = await redis.get(cacheKey);
      if (cachedData) {
        logger.info(`Cache des favoris récupérés depuis le cache pour l'utilisateur ${req.user.userId}`);
        // Ajout de la limite dans la réponse même en cache
        const response = JSON.parse(cachedData);
        response.favoriteLimit = favoriteLimit;
        res.json(response);
        return;
      } else {
        logger.info(`Cache des favoris récupérés depuis la base de données pour l'utilisateur ${req.user.userId}`);
      }

      // Récupérer le nombre total de favoris
      const { count } = await supabase
        .from('user_favorites')
        .select('id', { count: 'exact' })
        .eq('user_id', req.user.userId);

      if (count && count > favoriteLimit) {
        res.status(400).json({
          success: false,
          message: `Vous avez atteint la limite de ${favoriteLimit} favoris, supprimez des favoris pour en ajouter d'autres.`
        });
        return;
      }

      const { data: favorites, error } = await supabase
        .from('user_favorites')
        .select(`
          id,
          favorite_user_id,
          created_at,
          users:favorite_user_id (
            id,
            user_type,
            last_login,
            profil_verifier,
            identite_verifier,
            entreprise_verifier,
            assurance_verifier,
            is_online,
            profil:user_profil!user_id (
              nom,
              prenom,
              photo_url,
              bio,
              ville,
              slug,
              telephone,
              pays,
              code_postal,
              intervention_zone,
              telephone_prive,
              type_de_profil
            ),
            reviews:user_reviews!fk_target_user (
              note,
              commentaire,
              created_at
            ),
            services:user_services (
              id
            )
          )
        `)
        .eq('user_id', req.user.userId)
        .range(offset, offset + limit - 1)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const response = {
        success: true,
        data: await Promise.all(favorites.map(async (fav: any) => {
          // Déchiffrer les données utilisateur et profil
          const decryptedUser = fav.users ? await decryptUserDataAsync(fav.users) : null;

          return {
            ...fav,
            users: decryptedUser ? {
              ...decryptedUser,
              profil: decryptedUser.profil ? await Promise.all(decryptedUser.profil.map(async (p: any) => {
                const decryptedProfil = await decryptProfilDataAsync(p);
                return {
                  ...decryptedProfil,
                  nom: decryptedProfil.nom?.[0]?.toUpperCase() + '.' || null,
                  telephone: decryptedProfil.telephone_prive === false ? decryptedProfil.numero : 'Numéro Masqué',
                  intervention_zone: decryptedProfil.intervention_zone ? {
                    center: decryptedProfil.intervention_zone.center,
                    radius: decryptedProfil.intervention_zone.radius,
                    france_entiere: decryptedProfil.intervention_zone.france_entiere
                  } : null,
                };
              })) : null,
            } : null,
          };
        })),
        favoriteLimit,
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      };

      // Mettre en cache les données
      await redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(response));
      logger.info(`Cache mis à jour pour les favoris de l'utilisateur ${req.user.userId}`);

      res.json(response);
      return;
    } catch (error) {
      logger.error('Erreur lors de la récupération des favoris:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des favoris'
      });
      return;
    }
  }

  // Ajouter un favori et notifier l'utilisateur ajouté
  async addFavorite(req: Request, res: Response) {
    try {
      if (!req.user?.userId) {
        res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
        return;
      }

      const { userId } = req.params;

      // Récupérer la limite de favoris selon l'abonnement
      const limits = await getUserSubscriptionLimits(req.user.userId);
      const favoriteLimit = limits.isPremium ? 60 : 3;

      // Vérifier le nombre de favoris
      const { count } = await supabase
        .from('user_favorites')
        .select('id', { count: 'exact' })
        .eq('user_id', req.user.userId);

      if (count && count > favoriteLimit) {
        res.status(400).json({
          success: false,
          message: `Vous avez atteint la limite de ${favoriteLimit} favoris, supprimez des favoris pour en ajouter d'autres ou passer Premium pour en ajouter davantage.`,
        });
        return;
      }

      // Vérifier si le favori existe déjà
      const { data: existingFavorite } = await supabase
        .from('user_favorites')
        .select('id')
        .eq('user_id', req.user.userId)
        .eq('favorite_user_id', userId)
        .single();

      if (existingFavorite) {
        res.status(400).json({
          success: false,
          message: 'Ce profil est déjà dans vos favoris'
        });
        return;
      }

      // Ajouter le favori
      const { error } = await supabase
        .from('user_favorites')
        .insert({
          user_id: req.user.userId,
          favorite_user_id: userId
        });

      if (error) throw error;

      // Journaliser l'action de l'utilisateur
      logUserActivity(
        req.user.userId,
        'favorite_add',
        userId,
        'user',
        { action: 'Ajout d\'un utilisateur aux favoris' },
        getIpFromRequest(req)
      );

      // Invalider le cache des favoris de l'utilisateur
      const cachePattern = `favorites:user:${req.user.userId}:page:*`;
      const keys = await redis.keys(cachePattern);
      if (keys.length > 0) {
        await redis.del(keys);
        logger.info(`Cache invalidé pour les favoris de l'utilisateur ${req.user.userId}`);
      }

      // Récupérer les informations de l'utilisateur qui a ajouté le favori
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select(`
          id,
          profil:user_profil!user_id (
            nom,
            prenom,
            slug
          )
        `)
        .eq('id', req.user.userId)
        .single();

      if (userError) {
        logger.error('Erreur lors de la récupération des informations utilisateur:', userError);
      }

    //   logger.info('Données utilisateur récupérées:', {
    //     userId: req.user.userId,
    //     hasUserData: !!userData,
    //     hasProfilData: userData && !!userData.profil,
    //     profilIsArray: userData && userData.profil && Array.isArray(userData.profil),
    //     profilLength: userData && userData.profil && Array.isArray(userData.profil) ? userData.profil.length : 0
    //   });

      // Créer une notification pour l'utilisateur qui a été ajouté aux favoris
      if (userData && userData.profil && Array.isArray(userData.profil) && userData.profil.length > 0) {
        // Déchiffrer les données du profil
        const decryptedProfil = await decryptProfilDataAsync(userData.profil[0]);

        const nom = decryptedProfil.nom || '';
        const prenom = decryptedProfil.prenom || '';
        const slug = decryptedProfil.slug || '';
        // Formater le nom avec l'initiale suivie d'un point
        const nomFormate = nom && nom.length > 0 ? `${nom.charAt(0)}.` : '';
        const nomComplet = prenom ? `${prenom} ${nomFormate}`.trim() : nomFormate;
        
        // logger.info('Tentative de création de notification avec les données:', {
        //   destinataireId: userId,
        //   expediteurId: req.user.userId,
        //   nom,
        //   prenom,
        //   nomFormate,
        //   nomComplet
        // });
        
        try {
          const { data: notifData, error: notifError } = await supabase.from('user_notifications').insert({
            user_id: userId,
            type: 'profile',
            title: 'Nouveau favori',
            content: `${nomComplet || 'Un utilisateur'} vous a ajouté à ses favoris.`,
            is_read: false,
            is_archived: false,
            link: `/dashboard/profil/${slug || req.user.userId}`
          }).select();
          
          if (notifError) {
            logger.error('Erreur lors de la création de la notification:', notifError);
            
            // Tentative alternative d'insertion directe sans validation de type
            logger.info('Tentative alternative d\'insertion directe...');
            try {
              // Utiliser une requête SQL directe au lieu de RPC
              const { error: directError } = await supabase.from('user_notifications').insert({
                user_id: userId,
                type: 'profile',
                title: 'Nouveau favori',
                content: `${nomComplet || 'Un utilisateur'} vous a ajouté à ses favoris.`,
                is_read: false,
                is_archived: false,
                link: `/dashboard/profil/${slug || req.user.userId}`
              });
              
              if (directError) {
                logger.error('Échec de l\'insertion directe:', directError);
              } else {
                logger.info('Insertion directe réussie');
              }
            } catch (directError) {
              logger.error('Exception lors de l\'insertion directe:', directError);
            }
          } else {
            logger.info('Notification créée avec succès:', { notificationId: notifData?.[0]?.id });
          }
        
          // Invalider le cache des notifications pour l'utilisateur ajouté aux favoris
          const notifCachePattern = `notifications:${userId}:*`;
          const notifKeys = await redis.keys(notifCachePattern);
          if (notifKeys.length > 0) {
            await redis.del(notifKeys);
            logger.info(`Cache invalidé pour les notifications de l'utilisateur ${userId}`);
          }
          
          // Invalider le cache du compteur de notifications
          await redis.del(`notifications_count:${userId}`);
          logger.info(`Cache du compteur de notifications invalidé pour l'utilisateur ${userId}`);
        } catch (notificationError) {
          logger.error('Exception lors de la création de la notification:', notificationError);
        }
      } else {
        logger.warn('Impossible de créer une notification: données de profil utilisateur invalides ou manquantes');
      }

      logger.info('Profil ajouté aux favoris :', userId, 'pour l\'utilisateur :', req.user.userId);

      res.json({
        success: true,
        message: 'Profil ajouté aux favoris'
      });
      return;
    } catch (error) {
      logger.error('Erreur lors de l\'ajout du favori:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'ajout aux favoris'
      });
      return;
    }
  }

  // Supprimer un favori
  async removeFavorite(req: Request, res: Response) {
    try {
      if (!req.user?.userId) {
        res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
        return;
      }

      const { userId } = req.params;

      // Supprimer le favori
      const { error } = await supabase
        .from('user_favorites')
        .delete()
        .eq('user_id', req.user.userId)
        .eq('favorite_user_id', userId);

      if (error) throw error;

      // Journaliser l'action de l'utilisateur
      logUserActivity(
        req.user.userId,
        'favorite_remove',
        userId,
        'user',
        { action: 'Suppression d\'un utilisateur des favoris' },
        getIpFromRequest(req)
      );

      // Invalider le cache des favoris de l'utilisateur
      const cachePattern = `favorites:user:${req.user.userId}:page:*`;
      const keys = await redis.keys(cachePattern);
      if (keys.length > 0) {
        await redis.del(keys);
        logger.info(`Cache invalidé pour les favoris de l'utilisateur ${req.user.userId}`);
      }

      logger.info('Profil retiré des favoris :', userId, 'pour l\'utilisateur :', req.user.userId);

      res.json({
        success: true,
        message: 'Profil retiré des favoris'
      });
      return;
    } catch (error) {
      logger.error('Erreur lors de la suppression du favori:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la suppression du favori'
      });
      return;
    }
  }
}

export default new FavoritesController();