import api from './api';
import { API_CONFIG } from '../config/api';
import { logger } from '../utils/logger';

/**
 * Interface pour les statistiques de profil utilisateur
 */
export interface ProfileStats {
  // Infos de base
  user_id: string;
  registration_date: string;
  is_currently_premium: boolean;
  
  // Stats d'avis
  avg_rating: number;
  reviews_count: number;
  response_rate: number;
  consecutive_five_star_reviews: number;
  
  // Stats de missions
  missions_count: number;
  
  // Stats par défaut
  avg_response_time: number;
  connections_count: number;
  completed_trainings_count: number;
  on_time_completion_count: number;
  perfect_rating_count: number;
}

/**
 * Service pour gérer les statistiques de profil utilisateur
 */
export const profileService = {
  /**
   * Récupérer les statistiques du profil utilisateur
   * @param userId ID de l'utilisateur
   */
  getProfileStats: async (userId: string): Promise<ProfileStats> => {
    try {
      const response = await api.get(`${API_CONFIG.baseURL}/api/user-badges/profile-stats/${userId}`);
      return response.data.data;
    } catch (error) {
      logger.error('Erreur lors de la récupération des statistiques du profil:', error);
      // Retourner des statistiques par défaut en cas d'erreur
      return {
        user_id: userId,
        registration_date: new Date().toISOString(),
        is_currently_premium: false,
        avg_rating: 0,
        reviews_count: 0,
        response_rate: 0,
        consecutive_five_star_reviews: 0,
        missions_count: 0,
        avg_response_time: 0,
        connections_count: 0,
        completed_trainings_count: 0,
        on_time_completion_count: 0,
        perfect_rating_count: 0
      };
    }
  }
};

export default profileService;
