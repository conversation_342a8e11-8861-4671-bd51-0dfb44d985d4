/**
 * Utilitaires pour la gestion des connexions Socket
 */

export class SocketConnectionManager {
  private static instance: SocketConnectionManager;
  private activeConnections: Set<string> = new Set();
  private lastTokenFetch: number = 0;
  private readonly MIN_TOKEN_DELAY = 2000; // 2 secondes minimum entre les requêtes

  static getInstance(): SocketConnectionManager {
    if (!SocketConnectionManager.instance) {
      SocketConnectionManager.instance = new SocketConnectionManager();
    }
    return SocketConnectionManager.instance;
  }

  /**
   * Vérifie si on peut faire une nouvelle requête de token
   */
  canFetchToken(): boolean {
    const now = Date.now();
    return (now - this.lastTokenFetch) >= this.MIN_TOKEN_DELAY;
  }

  /**
   * Met à jour le timestamp de la dernière requête de token
   */
  updateLastTokenFetch(): void {
    this.lastTokenFetch = Date.now();
  }

  /**
   * <PERSON><PERSON> le délai d'attente avant la prochaine requête
   */
  getTokenFetchDelay(): number {
    const now = Date.now();
    const timeSinceLastFetch = now - this.lastTokenFetch;
    return Math.max(0, this.MIN_TOKEN_DELAY - timeSinceLastFetch);
  }

  /**
   * Enregistre une nouvelle connexion
   */
  registerConnection(connectionId: string): void {
    this.activeConnections.add(connectionId);
  }

  /**
   * Supprime une connexion
   */
  unregisterConnection(connectionId: string): void {
    this.activeConnections.delete(connectionId);
  }

  /**
   * Vérifie s'il y a déjà une connexion active
   */
  hasActiveConnection(): boolean {
    return this.activeConnections.size > 0;
  }

  /**
   * Nettoie toutes les connexions
   */
  clearAllConnections(): void {
    this.activeConnections.clear();
  }

  /**
   * Obtient le nombre de connexions actives
   */
  getActiveConnectionsCount(): number {
    return this.activeConnections.size;
  }
}

/**
 * Délai avec backoff exponentiel
 */
export function calculateBackoffDelay(attempt: number, baseDelay: number = 1000, maxDelay: number = 30000): number {
  const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
  const jitter = delay * 0.1 * Math.random(); // Ajouter un jitter de 10%
  return Math.floor(delay + jitter);
}

/**
 * Nettoie les flags localStorage obsolètes
 */
export function cleanupSocketFlags(): void {
  const now = Date.now();
  const socketFlag = localStorage.getItem('socket_active');
  
  if (socketFlag) {
    try {
      const { timestamp } = JSON.parse(socketFlag);
      if (now - timestamp > 60000) { // 1 minute
        localStorage.removeItem('socket_active');
      }
    } catch {
      localStorage.removeItem('socket_active');
    }
  }
}

/**
 * Vérifie si le navigateur supporte les WebSockets
 */
export function isWebSocketSupported(): boolean {
  return typeof WebSocket !== 'undefined' || typeof window !== 'undefined' && 'WebSocket' in window;
} 