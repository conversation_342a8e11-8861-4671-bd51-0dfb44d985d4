import React, { useState, useEffect } from 'react';
import { Text, Transformer } from 'react-konva';
import { KonvaEventObject } from 'konva/lib/Node';
import { TextElement } from '../../../types/cardEditor';

interface TextElementProps {
  element: TextElement;
  isSelected: boolean;
  onSelect: () => void;
  onDragStart?: (e: KonvaEventObject<DragEvent>) => void;
  onDragMove?: (e: KonvaEventObject<DragEvent>) => void;
  onDragEnd: (e: KonvaEventObject<DragEvent>) => void;
  isEditable?: boolean;
  onContextMenu?: (e: any) => void;
}

const TextElementComponent: React.FC<TextElementProps> = ({
  element,
  isSelected,
  onSelect,
  onDragStart,
  onDragMove = () => {},
  onDragEnd = () => {},
  isEditable = true,
  onContextMenu
}) => {
  const textRef = React.useRef<any>(null);
  const transformerRef = React.useRef<any>(null);
  const [isHovered, setIsHovered] = useState(false);

  const { properties } = element;

  useEffect(() => {
    if (isSelected && transformerRef.current && textRef.current) {
      // Attacher le transformer au texte
      transformerRef.current.nodes([textRef.current]);
      transformerRef.current.getLayer().batchDraw();
    }
  }, [isSelected]);

  useEffect(() => {
    if (textRef.current && isEditable) {
      const node = textRef.current;
      const calculatedWidth = node.width();
      const calculatedHeight = node.height();

      if (
        Math.abs(calculatedWidth - (element.width || 0)) > 0.1 ||
        Math.abs(calculatedHeight - (element.height || 0)) > 0.1
      ) {
        const pseudoEvent = {
          target: {
            x: () => element.x,
            y: () => element.y,
            rotation: () => element.rotation || 0,
            width: () => calculatedWidth,
            height: () => calculatedHeight,
          }
        } as any;
        onDragEnd(pseudoEvent);
      }
    }
  }, [
    properties.text,
    properties.fontSize,
    properties.fontFamily,
    properties.align,
    properties.letterSpacing,
    properties.lineHeight,
    properties.padding,
    properties.wrap,
    properties.ellipsis,
    element.x, // include to reconstruct pseudoEvent if needed
    element.y,
    element.rotation,
    onDragEnd,
    isEditable
  ]);

  // Déterminer le style du texte
  const fontStyle = properties.fontStyle || 'normal';
  const textDecoration = properties.textDecoration || '';

  // Propriétés avancées
  const letterSpacing = properties.letterSpacing || 0;
  const lineHeight = properties.lineHeight || 1;
  const padding = properties.padding || 0;
  const textShadow = properties.textShadow || null;
  const opacity = properties.opacity || 1;
  const wrap = properties.wrap || 'word';
  const ellipsis = properties.ellipsis || false;

  // Gérer le survol
  const handleMouseEnter = () => {
    if (isEditable && !isSelected) {
      setIsHovered(true);
      document.body.style.cursor = 'pointer';
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    document.body.style.cursor = 'default';
  };

  // Effet de survol
  const hoverEffect = isHovered ? {
    stroke: '#0096FF',
    strokeWidth: 2,
    shadowColor: undefined,
    shadowBlur: undefined,
    shadowOffset: undefined,
    shadowOpacity: undefined,
  } : {};

  // Effet de sélection
  const selectionEffect = isSelected ? {
    shadowColor: '#0096FF',
    shadowBlur: 10,
    shadowOffset: { x: 0, y: 0 },
    shadowOpacity: 0.5
  } : {};

  const handleTransformEnd = (e: any) => {
    if (!textRef.current) return;
    const node = textRef.current;
    let newRotation = node.rotation();
    newRotation = ((newRotation % 360) + 360) % 360;
    if (onDragEnd) {
      onDragEnd({
        ...e,
        target: {
          ...node,
          rotation: () => newRotation,
          width: () => node.width(),
          height: () => node.height()
        }
      });
    }
  };

  return (
    <>
      <Text
        ref={textRef}
        x={element.x}
        y={element.y}
        text={properties.text}
        fontSize={properties.fontSize}
        fontFamily={properties.fontFamily}
        fill={properties.fill}
        stroke={properties.stroke}
        strokeWidth={properties.strokeWidth}
        align={properties.align}
        width={undefined}
        height={undefined}
        draggable={isEditable && !!onDragMove && !!onDragEnd}
        onClick={onSelect}
        onTap={onSelect}
        onDragStart={onDragStart || (() => {})}
        onDragMove={onDragMove || (() => {})}
        onDragEnd={onDragEnd || (() => {})}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        fontStyle={fontStyle}
        textDecoration={textDecoration}
        letterSpacing={letterSpacing}
        lineHeight={lineHeight}
        padding={padding}
        perfectDrawEnabled={false}
        rotation={element.rotation || 0}
        opacity={opacity}
        wrap={wrap}
        ellipsis={ellipsis}
        {...(isHovered ? hoverEffect : {})}
        {...(isSelected ? selectionEffect : {})}
        shadowColor={textShadow?.color || properties.shadowColor}
        shadowBlur={textShadow?.blur || properties.shadowBlur}
        shadowOffset={textShadow?.offset || properties.shadowOffset}
        shadowOpacity={textShadow?.opacity}
        onContextMenu={onContextMenu}
      />
      {isSelected && isEditable && (
        <Transformer
          ref={transformerRef}
          boundBoxFunc={(oldBox: any, newBox: any) => {
            // Limiter la taille minimale
            if (newBox.width < 10 || newBox.height < 10) {
              return oldBox;
            }
            return newBox;
          }}
          rotateEnabled={true}
          enabledAnchors={['middle-left', 'middle-right', 'top-center', 'bottom-center']}
          borderStroke="#0096FF"
          borderStrokeWidth={2}
          anchorFill="#FFFFFF"
          anchorStroke="#0096FF"
          anchorStrokeWidth={2}
          anchorSize={8}
          keepRatio={false}
          onTransformEnd={handleTransformEnd}
        />
      )}
    </>
  );
};

export default TextElementComponent;
