import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';

const CACHE_KEY_PREFIX = 'openrouter:pricing:';
const CACHE_DURATION = 60 * 60; // 1 heure en secondes

/**
 * Récupère tous les tarifs des modèles OpenRouter
 */
export const getAllModelPricing = async (req: Request, res: Response) => {
  try {
    // Vérifier si les résultats sont en cache
    const cacheKey = `${CACHE_KEY_PREFIX}all`;
    const cachedData = await redis.get(cacheKey);

    if (cachedData) {
      return res.status(200).json(JSON.parse(cachedData));
    }

    // Récupérer tous les tarifs
    const { data, error } = await supabase
      .from('openrouter_model_pricing')
      .select('*')
      .order('model');

    if (error) {
      logger.error('Erreur lors de la récupération des tarifs des modèles OpenRouter', {
        error
      });
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des tarifs',
        error: error.message
      });
    }

    // Formater les résultats
    const result = {
      success: true,
      data
    };

    // Mettre en cache les résultats
    await redis.set(cacheKey, JSON.stringify(result), 'EX', CACHE_DURATION);

    return res.status(200).json(result);
  } catch (error: any) {
    logger.error('Erreur lors de la récupération des tarifs des modèles OpenRouter', {
      error: error.message,
      stack: error.stack
    });
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des tarifs',
      error: error.message
    });
  }
};

/**
 * Récupère un tarif de modèle spécifique
 */
export const getModelPricing = async (req: Request, res: Response) => {
  try {
    // Décoder le nom du modèle qui peut contenir des caractères spéciaux comme '/'
    const model = decodeURIComponent(req.params.model);

    logger.info(`Récupération du tarif pour le modèle: ${model}`);

    // Vérifier si les résultats sont en cache
    const cacheKey = `${CACHE_KEY_PREFIX}${model}`;
    const cachedData = await redis.get(cacheKey);

    if (cachedData) {
      return res.status(200).json(JSON.parse(cachedData));
    }

    // Récupérer le tarif du modèle
    const { data, error } = await supabase
      .from('openrouter_model_pricing')
      .select('*')
      .eq('model', model)
      .single();

    if (error) {
      logger.error(`Erreur lors de la récupération du tarif pour le modèle ${model}`, {
        error,
        model
      });

      if (error.code === 'PGRST116') {
        return res.status(404).json({
          success: false,
          message: `Le modèle ${model} n'existe pas`,
          error: error.message
        });
      }

      return res.status(500).json({
        success: false,
        message: `Erreur lors de la récupération du tarif pour le modèle ${model}`,
        error: error.message
      });
    }

    // Formater les résultats
    const result = {
      success: true,
      data
    };

    // Mettre en cache les résultats
    await redis.set(cacheKey, JSON.stringify(result), 'EX', CACHE_DURATION);

    return res.status(200).json(result);
  } catch (error: any) {
    logger.error('Erreur lors de la récupération du tarif du modèle', {
      error: error.message,
      stack: error.stack
    });
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du tarif du modèle',
      error: error.message
    });
  }
};

/**
 * Met à jour un tarif de modèle
 */
export const updateModelPricing = async (req: Request, res: Response) => {
  try {
    // Décoder le nom du modèle qui peut contenir des caractères spéciaux comme '/'
    const model = decodeURIComponent(req.params.model);

    logger.info(`Tentative de mise à jour du tarif pour le modèle: ${model}`);

    const {
      input_price_per_million,
      output_price_per_million,
      context_window,
      max_output,
      latency_seconds,
      throughput_tokens_per_second
    } = req.body;

    // Vérifier si le modèle existe
    const { data: existingModel, error: checkError } = await supabase
      .from('openrouter_model_pricing')
      .select('id')
      .eq('model', model)
      .single();

    if (checkError) {
      logger.error(`Modèle non trouvé: ${model}`, {
        error: checkError,
        model
      });
      return res.status(404).json({
        success: false,
        message: `Le modèle ${model} n'existe pas`,
        error: checkError.message
      });
    }

    // Mettre à jour le tarif
    const { data, error } = await supabase
      .from('openrouter_model_pricing')
      .update({
        input_price_per_million,
        output_price_per_million,
        context_window,
        max_output,
        latency_seconds,
        throughput_tokens_per_second
      })
      .eq('model', model)
      .select()
      .single();

    if (error) {
      logger.error(`Erreur lors de la mise à jour du tarif pour le modèle ${model}`, {
        error,
        model,
        body: req.body
      });
      return res.status(500).json({
        success: false,
        message: `Erreur lors de la mise à jour du tarif pour le modèle ${model}`,
        error: error.message
      });
    }

    // Supprimer les caches
    await redis.del(`${CACHE_KEY_PREFIX}all`);
    await redis.del(`${CACHE_KEY_PREFIX}${model}`);

    // Formater les résultats
    const result = {
      success: true,
      data,
      message: `Tarif pour le modèle ${model} mis à jour avec succès`
    };

    return res.status(200).json(result);
  } catch (error: any) {
    logger.error('Erreur lors de la mise à jour du tarif du modèle', {
      error: error.message,
      stack: error.stack
    });
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du tarif du modèle',
      error: error.message
    });
  }
};

/**
 * Ajoute un nouveau tarif de modèle
 */
export const addModelPricing = async (req: Request, res: Response) => {
  try {
    const {
      model,
      input_price_per_million,
      output_price_per_million,
      context_window,
      max_output,
      latency_seconds,
      throughput_tokens_per_second
    } = req.body;

    // Ajouter le tarif
    const { data, error } = await supabase
      .from('openrouter_model_pricing')
      .insert({
        model,
        input_price_per_million,
        output_price_per_million,
        context_window,
        max_output,
        latency_seconds,
        throughput_tokens_per_second
      })
      .select()
      .single();

    if (error) {
      logger.error(`Erreur lors de l'ajout du tarif pour le modèle ${model}`, {
        error,
        body: req.body
      });
      return res.status(500).json({
        success: false,
        message: `Erreur lors de l'ajout du tarif pour le modèle ${model}`,
        error: error.message
      });
    }

    // Supprimer le cache
    await redis.del(`${CACHE_KEY_PREFIX}all`);

    // Formater les résultats
    const result = {
      success: true,
      data,
      message: `Tarif pour le modèle ${model} ajouté avec succès`
    };

    return res.status(201).json(result);
  } catch (error: any) {
    logger.error('Erreur lors de l\'ajout du tarif du modèle', {
      error: error.message,
      stack: error.stack
    });
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'ajout du tarif du modèle',
      error: error.message
    });
  }
};
