import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { uploadTicketAttachment, extractFilePathFromUrl } from '../services/storage';
import fs from 'fs';
import path from 'path';

// Préfixe pour les clés de cache
const CACHE_PREFIX = 'support_ticket:';

// Durée de conservation des pièces jointes (2 mois en secondes)
const ATTACHMENT_RETENTION_PERIOD = 60 * 24 * 60 * 60; // 60 jours

/**
 * Ajouter une pièce jointe à un ticket de support
 */
export const addAttachmentToTicket = async (req: Request, res: Response) => {
  try {
    const { ticketId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(401).json({ success: false, message: 'Non authentifié' });
      return;
    }

    // Vérifier l'existence du ticket
    const { data: ticket, error: ticketError } = await supabase
      .from('support_tickets')
      .select('user_id')
      .eq('id', ticketId)
      .single();

    if (ticketError || !ticket) {
      logger.error('Erreur lors de la récupération du ticket:', ticketError);
      res.status(404).json({ success: false, message: 'Ticket non trouvé' });
      return;
    }

    // Vérifier les permissions
    const isOwner = ticket.user_id === userId;
    const isStaff = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';

    if (!isOwner && !isStaff) {
      res.status(403).json({ success: false, message: 'Non autorisé' });
      return;
    }

    // Vérifier si un fichier a été envoyé
    const files = req.files?.files;
    if (!files) {
      res.status(400).json({ success: false, message: 'Aucun fichier envoyé' });
      return;
    }

    // Nettoyer les pièces jointes anciennes au passage
    await cleanupOldAttachments();

    const fileArray = Array.isArray(files) ? files : [files];
    const attachments = [];
    const now = new Date().toISOString();

    for (const file of fileArray) {
      try {
        // Vérification du type MIME
        const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp', 'application/pdf', 'text/plain'];
        if (!allowedMimeTypes.includes(file.mimetype)) {
          logger.error('Type de fichier non autorisé :', { mimetype: file.mimetype });
          continue;
        }

        // Vérification de la taille
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (file.size > maxSize) {
          logger.error('Fichier trop volumineux', { size: file.size });
          continue;
        }

        // Lire le fichier temporaire
        const fileBuffer = await new Promise<Buffer>((resolve, reject) => {
          require('fs').readFile(file.tempFilePath, (err: any, data: Buffer) => {
            if (err) reject(err);
            else resolve(data);
          });
        });

        // Upload de la pièce jointe
        const fileUrl = await uploadTicketAttachment(
          userId,
          fileBuffer,
          file.mimetype,
          ticketId,
          file.name
        );

        // Créer l'entrée dans la table support_ticket_attachments
        const { data: attachment, error: attachmentError } = await supabase
          .from('support_ticket_attachments')
          .insert({
            ticket_id: ticketId,
            storage_path: fileUrl,
            file_name: file.name,
            file_type: file.mimetype,
            file_size: file.size,
            created_at: now
          })
          .select()
          .single();

        if (attachmentError) {
          logger.error('Erreur lors de l\'enregistrement de la pièce jointe:', attachmentError);
          continue;
        }

        attachments.push(attachment);
      } catch (error) {
        logger.error('Erreur lors du traitement d\'une pièce jointe:', error);
        continue;
      }
    }

    // Invalider le cache du ticket
    await redis.del(`${CACHE_PREFIX}${ticketId}`);

    // Renvoyer les pièces jointes ajoutées
    res.status(201).json({
      success: true,
      message: `${attachments.length} pièce(s) jointe(s) ajoutée(s)`,
      attachments,
      retention_info: {
        period_days: 60,
        auto_delete: true,
        delete_on_close: true
      }
    });
    return;
  } catch (error) {
    logger.error('Erreur lors de l\'ajout de pièces jointes:', error);
    res.status(500).json({ success: false, message: 'Erreur serveur' });
  }
};

/**
 * Supprimer une pièce jointe d'un ticket
 */
export const removeAttachmentFromTicket = async (req: Request, res: Response) => {
  try {
    const { ticketId, attachmentId } = req.params;
    const userId = req.user?.userId;
    const isStaff = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';

    if (!userId) {
      res.status(401).json({ success: false, message: 'Non authentifié' });
      return;
    }

    // Récupérer l'information de la pièce jointe
    const { data: attachment, error: attachmentError } = await supabase
      .from('support_ticket_attachments')
      .select('*, ticket:support_tickets!inner(user_id)')
      .eq('id', attachmentId)
      .eq('ticket_id', ticketId)
      .single();

    if (attachmentError || !attachment) {
      logger.error('Erreur lors de la récupération de la pièce jointe:', attachmentError);
      res.status(404).json({ success: false, message: 'Pièce jointe non trouvée' });
      return;
    }

    // Vérifier les permissions
    const isOwner = attachment.ticket.user_id === userId;
    if (!isOwner && !isStaff) {
      res.status(403).json({ success: false, message: 'Non autorisé' });
      return;
    }

    // Supprimer la pièce jointe de Supabase Storage
    try {
      const bucketPath = extractFilePathFromUrl(attachment.storage_path, 'support_ticket_attachments');

      if (bucketPath) {
        await supabase.storage
          .from('support_ticket_attachments')
          .remove([bucketPath]);

        logger.info('Fichier supprimé de Supabase Storage:', {
          path: bucketPath,
          attachmentId,
          ticketId
        });
      } else {
        logger.warn('Impossible d\'extraire le chemin du fichier pour la suppression:', {
          storage_path: attachment.storage_path,
          attachmentId,
          ticketId
        });
      }
    } catch (storageError) {
      logger.error('Erreur lors de la suppression du fichier dans le stockage:', {
        error: storageError,
        storage_path: attachment.storage_path,
        attachmentId,
        ticketId
      });
      // Continuer malgré l'erreur pour supprimer au moins l'entrée dans la base de données
    }

    // Supprimer la pièce jointe de la base de données
    const { error: deleteError } = await supabase
      .from('support_ticket_attachments')
      .delete()
      .eq('id', attachmentId);

    if (deleteError) {
      logger.error('Erreur lors de la suppression de la pièce jointe:', deleteError);
      res.status(500).json({ success: false, message: 'Erreur lors de la suppression' });
      return;
    }

    // Invalider le cache du ticket
    await redis.del(`${CACHE_PREFIX}${ticketId}`);

    // Confirmer la suppression
    res.json({
      success: true,
      message: 'Pièce jointe supprimée avec succès'
    });
    return;
  } catch (error) {
    logger.error('Erreur lors de la suppression de la pièce jointe:', error);
    res.status(500).json({ success: false, message: 'Erreur serveur' });
  }
};

/**
 * Récupérer les pièces jointes d'un ticket
 */
export const getTicketAttachments = async (req: Request, res: Response) => {
  try {
    const { ticketId } = req.params;
    const userId = req.user?.userId;
    const isStaff = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';

    if (!userId) {
      res.status(401).json({ success: false, message: 'Non authentifié' });
      return;
    }

    // Vérifier l'existence du ticket et les permissions
    const { data: ticket, error: ticketError } = await supabase
      .from('support_tickets')
      .select('user_id')
      .eq('id', ticketId)
      .single();

    if (ticketError || !ticket) {
      logger.error('Erreur lors de la récupération du ticket:', ticketError);
      res.status(404).json({ success: false, message: 'Ticket non trouvé' });
      return;
    }

    // Vérifier les permissions
    const isOwner = ticket.user_id === userId;
    if (!isOwner && !isStaff) {
      res.status(403).json({ success: false, message: 'Non autorisé' });
      return;
    }

    // Récupérer les pièces jointes
    const { data: attachments, error: attachmentsError } = await supabase
      .from('support_ticket_attachments')
      .select('*')
      .eq('ticket_id', ticketId)
      .order('created_at', { ascending: false });

    if (attachmentsError) {
      logger.error('Erreur lors de la récupération des pièces jointes:', attachmentsError);
      res.status(500).json({ success: false, message: 'Erreur serveur' });
      return;
    }

    res.json({
      success: true,
      attachments: attachments || [],
      retention_info: {
        period_days: 60,
        auto_delete: true,
        delete_on_close: true
      }
    });
    return;
  } catch (error) {
    logger.error('Erreur lors de la récupération des pièces jointes:', error);
    res.status(500).json({ success: false, message: 'Erreur serveur' });
  }
};

/**
 * Nettoyer les pièces jointes anciennes (plus de 2 mois)
 */
export const cleanupOldAttachments = async () => {
  try {
    // Calculer la date limite (2 mois dans le passé)
    const twoMonthsAgo = new Date();
    twoMonthsAgo.setMonth(twoMonthsAgo.getMonth() - 2);

    // Récupérer les pièces jointes à supprimer
    const { data: oldAttachments, error: selectError } = await supabase
      .from('support_ticket_attachments')
      .select('id, storage_path, file_name, created_at')
      .lt('created_at', twoMonthsAgo.toISOString());

    if (selectError) {
      logger.error('Erreur lors de la recherche des pièces jointes anciennes:', selectError);
      return;
    }

    if (!oldAttachments || oldAttachments.length === 0) {
      logger.info('Aucune pièce jointe ancienne à supprimer');
      return;
    }

    logger.info(`${oldAttachments.length} pièces jointes anciennes trouvées à supprimer`);

    // Supprimer les fichiers du stockage
    for (const attachment of oldAttachments) {
      try {
        const bucketPath = extractFilePathFromUrl(attachment.storage_path, 'support_ticket_attachments');

        if (bucketPath) {
          await supabase.storage
            .from('support_ticket_attachments')
            .remove([bucketPath]);

          logger.info('Fichier ancien supprimé de Supabase Storage:', {
            path: bucketPath,
            fileName: attachment.file_name,
            createdAt: attachment.created_at
          });
        } else {
          logger.warn('Impossible d\'extraire le chemin du fichier pour la suppression:', {
            storage_path: attachment.storage_path,
            fileName: attachment.file_name
          });
        }
      } catch (storageError) {
        logger.error('Erreur lors de la suppression d\'un fichier ancien dans le stockage:', {
          error: storageError,
          fileName: attachment.file_name,
          storage_path: attachment.storage_path
        });
        // Continuer malgré l'erreur
      }
    }

    // Supprimer les entrées de la base de données
    const { error: deleteError } = await supabase
      .from('support_ticket_attachments')
      .delete()
      .in('id', oldAttachments.map(a => a.id));

    if (deleteError) {
      logger.error('Erreur lors de la suppression des entrées de pièces jointes anciennes:', deleteError);
      return;
    }

    logger.info(`${oldAttachments.length} pièces jointes anciennes ont été supprimées avec succès`);
  } catch (error) {
    logger.error('Erreur lors du nettoyage des pièces jointes anciennes:', error);
  }
};

/**
 * Supprimer toutes les pièces jointes d'un ticket
 */
export const deleteTicketAttachments = async (ticketId: string) => {
  try {
    // Récupérer les pièces jointes du ticket
    const { data: attachments, error: selectError } = await supabase
      .from('support_ticket_attachments')
      .select('id, storage_path, file_name')
      .eq('ticket_id', ticketId);

    if (selectError) {
      logger.error('Erreur lors de la récupération des pièces jointes du ticket:', {
        error: selectError,
        ticketId
      });
      return;
    }

    if (!attachments || attachments.length === 0) {
      logger.info('Aucune pièce jointe à supprimer pour ce ticket', { ticketId });
      return;
    }

    logger.info(`${attachments.length} pièces jointes à supprimer pour le ticket ${ticketId}`);

    // Supprimer les fichiers du stockage
    for (const attachment of attachments) {
      try {
        const bucketPath = extractFilePathFromUrl(attachment.storage_path, 'support_ticket_attachments');

        if (bucketPath) {
          await supabase.storage
            .from('support_ticket_attachments')
            .remove([bucketPath]);

          logger.info('Fichier supprimé de Supabase Storage:', {
            path: bucketPath,
            fileName: attachment.file_name,
            ticketId
          });
        } else {
          logger.warn('Impossible d\'extraire le chemin du fichier pour la suppression:', {
            storage_path: attachment.storage_path,
            fileName: attachment.file_name,
            ticketId
          });
        }
      } catch (storageError) {
        logger.error('Erreur lors de la suppression d\'un fichier dans le stockage:', {
          error: storageError,
          fileName: attachment.file_name,
          storage_path: attachment.storage_path,
          ticketId
        });
        // Continuer malgré l'erreur
      }
    }

    // Supprimer les entrées de la base de données
    const { error: deleteError } = await supabase
      .from('support_ticket_attachments')
      .delete()
      .eq('ticket_id', ticketId);

    if (deleteError) {
      logger.error('Erreur lors de la suppression des entrées de pièces jointes:', {
        error: deleteError,
        ticketId
      });
      return;
    }

    logger.info(`${attachments.length} pièces jointes du ticket ${ticketId} ont été supprimées avec succès`);
  } catch (error) {
    logger.error('Erreur lors de la suppression des pièces jointes du ticket:', {
      error,
      ticketId
    });
  }
};