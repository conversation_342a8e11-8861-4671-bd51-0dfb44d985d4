import React, { useEffect, useState } from 'react';
import {
  Box, Container, Typography, Paper, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, CircularProgress, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, Avatar, Snackbar, Alert, Tooltip, Stack, Switch, FormControlLabel, MenuItem
} from '@mui/material';
import { useParams, useNavigate } from 'react-router-dom';
import VisibilityIcon from '@mui/icons-material/Visibility';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import CloseIcon from '@mui/icons-material/Close';
import DeleteIcon from '@mui/icons-material/Delete';
import { API_CONFIG } from '@/config/api';
import { getCommonHeaders } from '@/utils/headers';
import UserProfileModal from '@/components/UserProfileModal';
import axios from 'axios';

const STATUS_COLORS: Record<string, string> = {
  'approved': '#4CAF50',
  'validé': '#4CAF50',
  'rejected': '#FF6B2C',
  'rejeté': '#FF6B2C',
  'pending': '#FFB300',
  'en attente': '#FFB300',
};

const STATUS_LABELS: Record<string, string> = {
  'approved': 'Validé',
  'validé': 'Validé',
  'rejected': 'Rejeté',
  'rejeté': 'Rejeté',
  'pending': 'En attente',
  'en attente': 'En attente',
};

const TYPE_LABELS: Record<string, string> = {
  kbis: 'Kbis',
  assurance: "Attestation d'assurance",
  identity: "Carte d'identité",
  autre: 'Autre document'
};

// Liste des motifs prédéfinis pour le refus de document
const REFUS_MOTIFS = [
  'Document illisible',
  'Document expiré/périmé',
  'Mauvais document (ex : pas un Kbis)',
  'Document non conforme',
  'Document incomplet',
  'Document non signé',
  'Document non tamponné',
  'Document trop ancien',
  'Document déjà utilisé',
  'Document non officiel',
  'Document non authentique',
  'Erreur de téléchargement',
  'Fichier corrompu',
  'Fichier non lisible (format)',
  'Nom/prénom ne correspondent pas',
  'Informations manquantes',
  'Autre (à préciser)'
];

// Ajout d'une map de couleurs pour chaque type de document (différentes des statuts)
const TYPE_COLORS: Record<string, string> = {
  kbis: '#6EC6FF', // bleu clair
  assurance: '#B39DDB', // violet pastel
  identity: '#80CBC4', // turquoise pastel
  autre: '#FFD54F', // jaune pastel
};

// Fonction utilitaire pour vérifier si les infos entreprise sont vides
const isEntrepriseInfoEmpty = (profileData: any) => {
  if (!profileData || !profileData.profil || !profileData.profil.data || profileData.profil.data.type_de_profil !== 'entreprise') return true;
  const d = profileData.profil.data;
  return !d.siren_entreprise && !d.nom_entreprise && !d.statut_entreprise && !d.code_ape_entreprise && !d.categorie_entreprise && !d.effectif_entreprise && !d.date_insee_creation_entreprise && !d.date_derniere_mise_a_jour_entreprise_insee;
};

const EntrepriseDocumentDetailPage: React.FC = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const [user, setUser] = useState<any>(null);
  const [docs, setDocs] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [profileModalOpen, setProfileModalOpen] = useState(false);
  const [comment, setComment] = useState('');
  const [commentDialog, setCommentDialog] = useState<{ open: boolean, doc: any | null } | null>(null);
  const [profileData, setProfileData] = useState<any>(null);
  const [profileLoading, setProfileLoading] = useState(false);
  const [zoomDocId, setZoomDocId] = useState<string | null>(null);
  const [confirmDialog, setConfirmDialog] = useState<{ open: boolean, action: 'accept' | 'reject' | 'delete' | null, doc: any | null }>({ open: false, action: null, doc: null });
  const [deleteAllLoading, setDeleteAllLoading] = useState(false);
  const [deleteAllDialog, setDeleteAllDialog] = useState(false);
  const [snackbar, setSnackbar] = useState<{ open: boolean, message: string, severity: 'success' | 'error' }>({ open: false, message: '', severity: 'success' });
  const [sortTypeAsc, setSortTypeAsc] = useState(true);
  const [sortBy, setSortBy] = useState<'type' | 'date' | 'statut'>('type');
  const [sortDateAsc, setSortDateAsc] = useState(true);
  const [sortStatutAsc, setSortStatutAsc] = useState(true);
  const [forceStatusDialog, setForceStatusDialog] = useState<{ open: boolean, field: string | null, value: boolean }>({ open: false, field: null, value: false });
  const [refusMotif, setRefusMotif] = useState('');
  const [refusComment, setRefusComment] = useState('');
  const [unavailableImages, setUnavailableImages] = useState<{ [id: string]: boolean }>({});
  const [showAllStatus, setShowAllStatus] = useState(false);
  const [remindDialog, setRemindDialog] = useState<{ open: boolean, doc: any | null }>({ open: false, doc: null });
  const [remindLoading, setRemindLoading] = useState(false);

  // Récupérer les documents et infos utilisateur
  const fetchDocs = async () => {
    setLoading(true);
    try {
      const headers = await getCommonHeaders();
      const res = await fetch(`${API_CONFIG.baseURL}/api/users/verification/entreprise/list`, {
        method: 'GET',
        headers,
        credentials: 'include'
      });
      const data = await res.json();
      const docsAll = data?.docs || [];
      const userDocs = docsAll.filter((doc: any) => doc.user_id === userId);
      setDocs(userDocs);
      if (userDocs.length > 0) {
        setUser({
          id: userDocs[0].user_id,
          email: userDocs[0].users?.email || '',
          nom: userDocs[0].user_profil?.nom || '',
          prenom: userDocs[0].user_profil?.prenom || '',
          profil_verifier: userDocs[0].users?.profil_verifier,
          identite_verifier: userDocs[0].users?.identite_verifier,
          entreprise_verifier: userDocs[0].users?.entreprise_verifier,
          assurance_verifier: userDocs[0].users?.assurance_verifier,
        });
      }
    } catch (e) {
      setDocs([]);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDocs();
    // eslint-disable-next-line
  }, [userId]);

  // Chargement automatique du profil complet dès que user.id est dispo
  useEffect(() => {
    const fetchProfileData = async () => {
      if (!user?.id) return;
      try {
        const headers = await getCommonHeaders();
        const slugRes = await axios.get(`${API_CONFIG.baseURL}/api/users/get-slug/${user.id}`, {
          headers,
          withCredentials: true
        });
        const slug = slugRes.data?.slug;
        if (!slug) throw new Error('Slug introuvable');
        const res = await axios.get(`${API_CONFIG.baseURL}/api/users/profil/${slug}`, {
          headers,
          withCredentials: true
        });
        setProfileData(res.data);
      } catch (e) {
        setProfileData(null);
      }
    };
    fetchProfileData();
    // eslint-disable-next-line
  }, [user?.id]);

  useEffect(() => {
    if (!user || !user.profil_verifier) return;
    if (profileData?.profil?.data?.type_de_profil === 'entreprise') {
      if (!user.identite_verifier || !user.entreprise_verifier || !user.assurance_verifier) {
        handleForceStatus('profil_verifier', false);
        setUser((prev: any) => prev ? { ...prev, profil_verifier: false } : prev);
      }
    } else if (profileData?.profil?.data?.type_de_profil === 'particulier') {
      if (!user.identite_verifier) {
        handleForceStatus('profil_verifier', false);
        setUser((prev: any) => prev ? { ...prev, profil_verifier: false } : prev);
      }
    }
    // eslint-disable-next-line
  }, [user?.identite_verifier, user?.entreprise_verifier, user?.assurance_verifier, profileData?.profil?.data?.type_de_profil]);

  const handleValidate = async (doc: any, status: 'validé' | 'rejeté', comments?: string) => {
    setActionLoading(true);
    try {
      const headers = await getCommonHeaders();
      await fetch(`${API_CONFIG.baseURL}/api/users/verification/entreprise/validate`, {
        method: 'POST',
        headers: { ...headers, 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          documentId: doc.id,
          status,
          comments: comments || '',
        })
      });
      fetchDocs();
      setOpenDialog(false);
      setCommentDialog(null);
      setComment('');
    } catch (e) {
      // Gérer l'erreur simplement
    } finally {
      setActionLoading(false);
    }
  };

  const openProfileModal = async () => {
    setProfileModalOpen(true);
    setProfileLoading(true);
    try {
      // 1. Récupérer le slug à partir de l'userId
      const headers = await getCommonHeaders();
      const slugRes = await axios.get(`${API_CONFIG.baseURL}/api/users/get-slug/${user?.id}`, {
        headers,
        withCredentials: true
      });
      const slug = slugRes.data?.slug;
      if (!slug) throw new Error('Slug introuvable');
      // 2. Charger le profil complet avec le slug
      const res = await axios.get(`${API_CONFIG.baseURL}/api/users/profil/${slug}`, {
        headers,
        withCredentials: true
      });
      setProfileData(res.data);
    } catch (e) {
      setProfileData(null);
    } finally {
      setProfileLoading(false);
    }
  };

  const handleDelete = async (doc: any) => {
    try {
      setActionLoading(true);
      const headers = await getCommonHeaders();
      await fetch(`${API_CONFIG.baseURL}/api/users/verification/entreprise/delete`, {
        method: 'POST',
        headers: { ...headers, 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ documentId: doc.id })
      });
      setZoomDocId(null);
      fetchDocs();
    } catch (e) {
      // Gérer l'erreur simplement
    } finally {
      setActionLoading(false);
    }
  };

  // Fonction suppression de tous les docs
  const handleDeleteAllDocs = async () => {
    setDeleteAllDialog(false);
    setDeleteAllLoading(true);
    try {
      const headers = await getCommonHeaders();
      const res = await fetch(`${API_CONFIG.baseURL}/api/users/verification/entreprise/delete-all`, {
        method: 'POST',
        headers: { ...headers, 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ userId })
      });
      const data = await res.json();
      if (data.success) {
        setSnackbar({ open: true, message: 'Tous les documents ont été supprimés.', severity: 'success' });
        fetchDocs();
      } else {
        setSnackbar({ open: true, message: data.error || 'Erreur lors de la suppression.', severity: 'error' });
      }
    } catch (e) {
      setSnackbar({ open: true, message: 'Erreur réseau ou serveur.', severity: 'error' });
    } finally {
      setDeleteAllLoading(false);
    }
  };

  // Fonction de tri simple sur le type, la date ou le statut
  const sortedDocs = [...docs].sort((a, b) => {
    if (sortBy === 'type') {
      const typeA = TYPE_LABELS[a.type] || a.type || '';
      const typeB = TYPE_LABELS[b.type] || b.type || '';
      if (typeA < typeB) return sortTypeAsc ? -1 : 1;
      if (typeA > typeB) return sortTypeAsc ? 1 : -1;
      return 0;
    } else if (sortBy === 'date') {
      const dateA = a.upload_date ? new Date(a.upload_date).getTime() : 0;
      const dateB = b.upload_date ? new Date(b.upload_date).getTime() : 0;
      if (dateA < dateB) return sortDateAsc ? -1 : 1;
      if (dateA > dateB) return sortDateAsc ? 1 : -1;
      return 0;
    } else {
      const statutA = STATUS_LABELS[a.status] || a.status || '';
      const statutB = STATUS_LABELS[b.status] || b.status || '';
      if (statutA < statutB) return sortStatutAsc ? -1 : 1;
      if (statutA > statutB) return sortStatutAsc ? 1 : -1;
      return 0;
    }
  });

  // Filtrage selon le switch
  const filteredDocs = showAllStatus
    ? sortedDocs
    : sortedDocs.filter(doc => doc.status === 'pending' || doc.status === 'en attente');

  // Fonction pour forcer un statut de vérification (API à créer côté backend)
  const handleForceStatus = async (field: string, value: boolean) => {
    if (!user?.id) return;
    try {
      const headers = await getCommonHeaders();
      const response = await fetch(`${API_CONFIG.baseURL}/api/users/verification/force-status`, {
        method: 'POST',
        headers: { ...headers, 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ userId: user.id, field, value })
      });
      const data = await response.json();
      fetchDocs();
      if (data && data.field && typeof data.value !== 'undefined' && data.userId) {
        setUser((prev: any) => prev ? { ...prev, [data.field]: data.value } : prev);
        setSnackbar({
          open: true,
          message: `Champ ${data.field} mis à jour à ${data.value ? 'oui' : 'non'} pour l'utilisateur ${data.userId}.`,
          severity: 'success'
        });
      } else if (data && data.error) {
        setSnackbar({
          open: true,
          message: `Erreur : ${data.error}`,
          severity: 'error'
        });
      } else {
        setSnackbar({
          open: true,
          message: `Mise à jour effectuée, mais réponse inattendue du serveur.`,
          severity: 'error'
        });
      }
    } catch (e) {
      setSnackbar({ open: true, message: `Erreur lors de la mise à jour du champ ${field}.`, severity: 'error' });
    }
  };

  const handleConfirmForceStatus = (field: string, value: boolean) => {
    setForceStatusDialog({ open: true, field, value });
  };

  const handleForceStatusConfirm = async () => {
    if (forceStatusDialog.field) {
      await handleForceStatus(forceStatusDialog.field, forceStatusDialog.value);
    }
    setForceStatusDialog({ open: false, field: null, value: false });
  };

  if (loading) {
    return <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 300 }}><CircularProgress sx={{ color: '#FF6B2C' }} /></Box>;
  }

  return (
    <Box sx={{ background: 'linear-gradient(to bottom, #FFF8F3, white)', minHeight: '100vh', py: 4 }}>
      <Container maxWidth="md"
        sx={{
          '@media (min-width:900px)': {
            maxWidth: '1480px',
          }
        }}
      >
        <Button onClick={() => navigate('/admin/entreprise-documents')} sx={{ mb: 2, color: '#FF6B2C', fontWeight: 600 }}>&larr; Retour à la liste</Button>
        <Paper sx={{ p: 3, mb: 3, borderRadius: 3, boxShadow: '0 4px 12px rgba(255,107,44,0.07)' }}>
          {user && (
            <Box sx={{ mb: 2 }}>
              <Avatar sx={{ bgcolor: '#FFE4BA', color: '#FF6B2C', fontWeight: 700, width: 56, height: 56, fontSize: 28, mb: 1 }}>
                {user.prenom?.[0] || user.nom?.[0] || user.email?.[0] || '?'}
              </Avatar>
              <Box>
                <Stack direction={{ xs: 'column', sm: 'row' }} alignItems={{ xs: 'flex-start', sm: 'center' }} spacing={{ xs: 1, sm: 2 }} sx={{ mb: 0.5, width: '100%' }}>
                  <Typography sx={{ fontWeight: 700, fontSize: 22, color: '#222' }}>{user.prenom} {user.nom}</Typography>
                  <Button 
                    size="small"
                    variant="contained"
                    sx={{
                      bgcolor: '#FF6B2C',
                      color: 'white',
                      fontWeight: 700,
                      boxShadow: '0 2px 8px rgba(255,107,44,0.18)',
                      borderRadius: 2,
                      px: 2.5,
                      py: 0.5,
                      fontSize: 15,
                      ml: { xs: 0, sm: 1 },
                      mt: { xs: 1, sm: 0 },
                      transition: 'all 0.18s',
                      '&:hover': { bgcolor: '#d35400', boxShadow: '0 4px 16px rgba(255,107,44,0.22)' }
                    }}
                    onClick={openProfileModal}
                  >
                    Voir profil
                  </Button>
                </Stack>
                <Typography sx={{ color: '#888', fontSize: 13 }}>{user.email}</Typography>
                {/* Ajout : Type de profil et dates d'expiration des documents */}
                {profileData?.profil?.data?.type_de_profil && (
                  <Typography sx={{ color: '#FF6B2C', fontWeight: 600, fontSize: 15, mt: 0.5 }}>
                    {profileData.profil.data.type_de_profil === 'particulier' ? 'Particulier' : 'Professionnel'}
                  </Typography>
                )}
                {(profileData?.profil?.data?.date_validation_document_identite || profileData?.profil?.data?.date_validation_document_entreprise || profileData?.profil?.data?.date_validation_document_assurance) && (
                  <Box sx={{ mt: 0.5 }}>
                    {profileData?.profil?.data?.date_validation_document_identite && (
                      <Typography sx={{ color: '#222', fontSize: 13 }}>
                        Identité : expire le {new Date(new Date(profileData.profil.data.date_validation_document_identite).setFullYear(new Date(profileData.profil.data.date_validation_document_identite).getFullYear() + 1)).toLocaleDateString()}
                      </Typography>
                    )}
                    {profileData?.profil?.data?.date_validation_document_entreprise && (
                      <Typography sx={{ color: '#222', fontSize: 13 }}>
                        Entreprise : expire le {new Date(new Date(profileData.profil.data.date_validation_document_entreprise).setFullYear(new Date(profileData.profil.data.date_validation_document_entreprise).getFullYear() + 1)).toLocaleDateString()}
                      </Typography>
                    )}
                    {profileData?.profil?.data?.date_validation_document_assurance && (
                      <Typography sx={{ color: '#222', fontSize: 13 }}>
                        Assurance : expire le {new Date(new Date(profileData.profil.data.date_validation_document_assurance).setFullYear(new Date(profileData.profil.data.date_validation_document_assurance).getFullYear() + 1)).toLocaleDateString()}
                      </Typography>
                    )}
                  </Box>
                )}
                <Box sx={{ mt: 1, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Chip label={user.profil_verifier ? 'Profil vérifié' : 'Profil non vérifié'} size="small" sx={{ bgcolor: user.profil_verifier ? '#4CAF50' : '#FFB300', color: 'white', fontWeight: 600 }} />
                  <Chip label={user.identite_verifier ? 'Identité vérifiée' : 'Identité non vérifiée'} size="small" sx={{ bgcolor: user.identite_verifier ? '#4CAF50' : '#FFB300', color: 'white', fontWeight: 600 }} />
                  <Chip label={user.entreprise_verifier ? 'Entreprise vérifiée' : 'Entreprise non vérifiée'} size="small" sx={{ bgcolor: user.entreprise_verifier ? '#4CAF50' : '#FFB300', color: 'white', fontWeight: 600 }} />
                  <Chip label={user.assurance_verifier ? 'Assurance vérifiée' : 'Assurance non vérifiée'} size="small" sx={{ bgcolor: user.assurance_verifier ? '#4CAF50' : '#FFB300', color: 'white', fontWeight: 600 }} />
                </Box>
                {/* Actions rapides admin pour forcer les statuts ET Informations Entreprise côte à côte */}
                <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2, alignItems: 'stretch', width: '100%', mt: 2 }}>
                  {/* Bloc Actions rapides (staff) */}
                  <Box sx={{ flex: 1, minWidth: 0 }}>
                    <Box
                      sx={{
                        p: 2.5,
                        bgcolor: '#FFF3E0',
                        borderRadius: 3,
                        border: '1.5px solid #FFB300',
                        boxShadow: '0 2px 12px rgba(255,179,0,0.07)',
                        mb: { xs: 2, md: 0 },
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        gap: 1.5,
                        position: 'relative',
                        transition: 'box-shadow 0.2s',
                        '&:hover': {
                          boxShadow: '0 4px 24px rgba(255,179,0,0.13)',
                        },
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Box sx={{
                          bgcolor: '#FFB300',
                          color: 'white',
                          borderRadius: '50%',
                          width: 32,
                          height: 32,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mr: 1.2,
                          boxShadow: '0 1px 4px rgba(255,179,0,0.13)'
                        }}>
                          <span style={{ fontSize: 20, fontWeight: 700 }}>⚡</span>
                        </Box>
                        <Typography variant="subtitle2" sx={{ color: '#FF6B2C', fontWeight: 700 }}>
                          Actions rapides (staff)
                        </Typography>
                      </Box>
                      <Box sx={{ borderBottom: '1px solid #FFE4BA', mb: 1.5 }} />
                      <Alert icon={false} severity="warning" sx={{ bgcolor: '#FFF8F3', color: '#d32f2f', fontWeight: 500, mb: 2, px: 2, py: 1.2, border: '1px solid #FFB300' }}>
                        ⚠️ À utiliser uniquement en cas d'urgence (erreur de validation).<br />
                        Ces actions ne préviennent pas l'utilisateur et ne mettent pas à jour les fichiers de validation du client.
                      </Alert>
                      <Stack direction="column" spacing={1.2}>
                        <Tooltip title={
                          profileData?.profil?.data?.type_de_profil === 'entreprise'
                            ? (user.identite_verifier && user.entreprise_verifier && user.assurance_verifier ? '' : 'Pour activer le profil vérifié, tous les autres statuts doivent être cochés.')
                            : (user.identite_verifier ? '' : 'Pour activer le profil vérifié, l\'identité doit être vérifiée.')
                        }>
                          <span>
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={!!user.profil_verifier}
                                  onChange={async (e) => await handleConfirmForceStatus('profil_verifier', e.target.checked)}
                                  disabled={
                                    profileData?.profil?.data?.type_de_profil === 'entreprise'
                                      ? !(user.identite_verifier && user.entreprise_verifier && user.assurance_verifier)
                                      : !user.identite_verifier
                                  }
                                  sx={{
                                    '& .MuiSwitch-switchBase.Mui-checked': { color: '#FF6B2C' },
                                    '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FF6B2C' },
                                    '&:hover': { boxShadow: '0 0 0 2px #FFE4BA' }
                                  }}
                                />
                              }
                              label="Profil vérifié"
                            />
                          </span>
                        </Tooltip>
                        <FormControlLabel
                          control={<Switch checked={!!user.identite_verifier} onChange={async (e) => await handleConfirmForceStatus('identite_verifier', e.target.checked)}
                            sx={{ '& .MuiSwitch-switchBase.Mui-checked': { color: '#FF6B2C' }, '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FF6B2C' }, '&:hover': { boxShadow: '0 0 0 2px #FFE4BA' } }}
                          />}
                          label="Identité vérifiée"
                        />
                        <FormControlLabel
                          control={<Switch checked={!!user.entreprise_verifier} onChange={async (e) => await handleConfirmForceStatus('entreprise_verifier', e.target.checked)}
                            sx={{ '& .MuiSwitch-switchBase.Mui-checked': { color: '#FF6B2C' }, '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FF6B2C' }, '&:hover': { boxShadow: '0 0 0 2px #FFE4BA' } }}
                          />}
                          label="Entreprise vérifiée"
                        />
                        <FormControlLabel
                          control={<Switch checked={!!user.assurance_verifier} onChange={async (e) => await handleConfirmForceStatus('assurance_verifier', e.target.checked)}
                            sx={{ '& .MuiSwitch-switchBase.Mui-checked': { color: '#FF6B2C' }, '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FF6B2C' }, '&:hover': { boxShadow: '0 0 0 2px #FFE4BA' } }}
                          />}
                          label="Assurance vérifiée"
                        />
                      </Stack>
                    </Box>
                  </Box>
                  {/* Bloc Informations Entreprise */}
                  {profileData && profileData.profil && profileData.profil.data && profileData.profil.data.type_de_profil === 'entreprise' && (
                    <Box sx={{ flex: 1, minWidth: 0 }}>
                      <Box sx={{ p: 2, bgcolor: '#FFF8F3', borderRadius: 2, border: '1px solid #FF6B2C', height: '100%' }}>
                        <Typography variant="subtitle1" sx={{ color: '#FF6B2C', fontWeight: 700, mb: 1 }}>Informations Entreprise</Typography>
                        <Typography variant="body2"><b>SIREN :</b> {profileData.profil.data.siren_entreprise || '-'}</Typography>
                        <Typography variant="body2"><b>Nom entreprise :</b> {profileData.profil.data.nom_entreprise || '-'}</Typography>
                        <Typography variant="body2"><b>Statut :</b> {profileData.profil.data.statut_entreprise || '-'}</Typography>
                        <Typography variant="body2"><b>Code APE :</b> {profileData.profil.data.code_ape_entreprise || '-'}</Typography>
                        <Typography variant="body2"><b>Catégorie :</b> {profileData.profil.data.categorie_entreprise || '-'}</Typography>
                        <Typography variant="body2"><b>Effectif :</b> {profileData.profil.data.effectif_entreprise || '-'}</Typography>
                        <Typography variant="body2"><b>Date création :</b> {profileData.profil.data.date_insee_creation_entreprise ? new Date(profileData.profil.data.date_insee_creation_entreprise).toLocaleDateString() : '-'}</Typography>
                        <Typography variant="body2"><b>Dernière mise à jour INSEE :</b> {profileData.profil.data.date_derniere_mise_a_jour_entreprise_insee ? new Date(profileData.profil.data.date_derniere_mise_a_jour_entreprise_insee).toLocaleDateString() : '-'}</Typography>
                        {isEntrepriseInfoEmpty(profileData) && (
                          <Alert severity="warning" sx={{ mt: 2, bgcolor: '#FFF3E0', color: '#FF6B2C', fontWeight: 600 }}>
                            Les informations entreprise sont vides. L'utilisateur doit les remplir pour pouvoir valider les documents.
                          </Alert>
                        )}
                      </Box>
                    </Box>
                  )}
                </Box>
              </Box>
            </Box>
          )}
          <Typography variant="h6" sx={{ color: '#FF6B2C', fontWeight: 700, mb: 2 }}>Documents à valider</Typography>
          {/* Switch pour afficher tous les statuts */}
          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={showAllStatus}
                  onChange={() => setShowAllStatus((v) => !v)}
                  sx={{
                    '& .MuiSwitch-switchBase.Mui-checked': { color: '#FF6B2C' },
                    '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FF6B2C' }
                  }}
                />
              }
              label="Afficher aussi les documents validés/rejetés"
            />
          </Box>
          {/* BOUTON SUPPRIMER TOUS LES DOCUMENTS */}
          {docs.length > 5 && (
            <Button
              variant="contained"
              color="error"
              sx={{ mb: 2, fontWeight: 700, bgcolor: '#FF6B2C', '&:hover': { bgcolor: '#d35400' } }}
              onClick={() => setDeleteAllDialog(true)}
              disabled={deleteAllLoading}
            >
              Supprimer tous les documents
            </Button>
          )}
          <TableContainer>
            <Table>
              <TableHead sx={{ bgcolor: '#FFF8F3' }}>
                <TableRow>
                  <TableCell>Miniature</TableCell>
                  <TableCell
                    onClick={() => {
                      if (sortBy === 'type') {
                        setSortTypeAsc((asc) => !asc);
                      } else {
                        setSortBy('type');
                      }
                    }}
                    sx={{ cursor: 'pointer', fontWeight: 700, color: sortBy === 'type' ? '#FF6B2C' : 'inherit', userSelect: 'none' }}
                  >
                    Type {sortBy === 'type' ? (sortTypeAsc ? '▲' : '▼') : ''}
                  </TableCell>
                  <TableCell
                    onClick={() => {
                      if (sortBy === 'date') {
                        setSortDateAsc((asc) => !asc);
                      } else {
                        setSortBy('date');
                      }
                    }}
                    sx={{ cursor: 'pointer', fontWeight: 700, color: sortBy === 'date' ? '#FF6B2C' : 'inherit', userSelect: 'none' }}
                  >
                    Date d'upload {sortBy === 'date' ? (sortDateAsc ? '▲' : '▼') : ''}
                  </TableCell>
                  <TableCell
                    onClick={() => {
                      if (sortBy === 'statut') {
                        setSortStatutAsc((asc) => !asc);
                      } else {
                        setSortBy('statut');
                      }
                    }}
                    sx={{ cursor: 'pointer', fontWeight: 700, color: sortBy === 'statut' ? '#FF6B2C' : 'inherit', userSelect: 'none' }}
                  >
                    Statut {sortBy === 'statut' ? (sortStatutAsc ? '▲' : '▼') : ''}
                  </TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredDocs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center">Aucun document à afficher.</TableCell>
                  </TableRow>
                ) : filteredDocs.map((doc: any) => (
                  <React.Fragment key={doc.id}>
                    <TableRow>
                      <TableCell>
                        {/* Miniature ou icône PDF cliquable pour lightbox */}
                        {doc.file_url && doc.file_url.match(/\.(jpg|jpeg|png|gif|webp)$/i) ? (
                          unavailableImages[doc.id] ? (
                            <Typography color="error" fontSize={15} fontWeight={600}>Plus disponible</Typography>
                          ) : (
                            <img
                              src={doc.file_url}
                              alt="miniature document"
                              style={{ height: 48, width: 'auto', marginRight: 8, borderRadius: 4, cursor: 'zoom-in', verticalAlign: 'middle', boxShadow: '0 1px 4px rgba(0,0,0,0.07)' }}
                              onClick={() => setZoomDocId(doc.id)}
                              onError={() => setUnavailableImages(prev => ({ ...prev, [doc.id]: true }))}
                            />
                          )
                        ) : doc.file_url && doc.file_url.match(/\.pdf$/i) ? (
                          <PictureAsPdfIcon
                            sx={{ color: '#FF6B2C', fontSize: 32, verticalAlign: 'middle', cursor: 'zoom-in', mr: 1 }}
                            onClick={() => setZoomDocId(doc.id)}
                          />
                        ) : (
                          <Typography color="error" fontSize={13}>plus dispo</Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={TYPE_LABELS[doc.type] || doc.type}
                          sx={{
                            bgcolor: TYPE_COLORS[doc.type] || '#FFE4BA',
                            color: '#fff',
                            fontWeight: 700,
                            borderRadius: 1,
                            minWidth: 120,
                            textTransform: 'none',
                            fontSize: 15
                          }}
                        />
                      </TableCell>
                      <TableCell>{doc.upload_date ? new Date(doc.upload_date).toLocaleDateString('fr-FR') : '-'}</TableCell>
                      <TableCell>
                        <Chip label={STATUS_LABELS[doc.status] || doc.status} sx={{ bgcolor: STATUS_COLORS[doc.status] || '#FFB300', color: 'white', fontWeight: 700 }} />
                        {((doc.status === 'approved' || doc.status === 'validé') && doc.verification_date) && (
                          <Typography variant="caption" sx={{ display: 'block', color: '#888', mt: 0.5 }}>
                            Validé le : {new Date(doc.verification_date).toLocaleDateString('fr-FR')}
                          </Typography>
                        )}
                        {((doc.status === 'rejected' || doc.status === 'rejeté') && doc.verification_date) && (
                          <Typography variant="caption" sx={{ display: 'block', color: '#888', mt: 0.5 }}>
                            Rejeté le : {new Date(doc.verification_date).toLocaleDateString('fr-FR')}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Tooltip title="Voir le document">
                          <IconButton onClick={() => setZoomDocId(doc.id)}>
                            <VisibilityIcon sx={{ color: '#FF6B2C' }} />
                          </IconButton>
                        </Tooltip>
                        {doc.status === 'pending' && (
                          profileData?.profil?.data?.type_de_profil === 'entreprise' && isEntrepriseInfoEmpty(profileData) ? (
                            <Button
                              variant="outlined"
                              color="warning"
                              sx={{ ml: 1, fontWeight: 700, borderColor: '#FFB300', color: '#FFB300' }}
                              onClick={() => setRemindDialog({ open: true, doc })}
                            >
                              Demander à remplir les infos entreprise
                            </Button>
                          ) : (
                            <>
                              <Tooltip title="Valider ce document">
                                <span>
                                  <IconButton onClick={() => setConfirmDialog({ open: true, action: 'accept', doc })} disabled={actionLoading}>
                                    <CheckCircleIcon sx={{ color: '#4CAF50' }} />
                                  </IconButton>
                                </span>
                              </Tooltip>
                              <Tooltip title="Refuser ce document">
                                <span>
                                  <IconButton onClick={() => setCommentDialog({ open: true, doc })} disabled={actionLoading}>
                                    <CancelIcon sx={{ color: '#FF6B2C' }} />
                                  </IconButton>
                                </span>
                              </Tooltip>
                            </>
                          )
                        )}
                        <Tooltip title="Supprimer définitivement ce document">
                          <span>
                            <IconButton
                              onClick={() => setConfirmDialog({ open: true, action: 'delete', doc })}
                              disabled={actionLoading}
                              sx={{ color: '#d32f2f', ml: 1, '&:hover': { color: '#b71c1c', bgcolor: 'rgba(211,47,47,0.08)' } }}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </span>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
        {/* Modale commentaire de refus */}
        <Dialog open={!!commentDialog?.open} onClose={() => { setCommentDialog(null); setRefusMotif(''); setRefusComment(''); }} maxWidth="xs" fullWidth>
          <DialogTitle sx={{ color: '#FF6B2C', fontWeight: 700 }}>
            Motif du refus
          </DialogTitle>
          <DialogContent>
            <Box sx={{ p: 2, bgcolor: '#fafafa', borderRadius: 1, border: '1px solid #e0e0e0', mt: 1 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#222', mb: 1 }}>
                Motif du refus <span style={{ color: '#d32f2f' }}>*</span>
              </Typography>
              <TextField
                select
                label=""
                value={refusMotif}
                onChange={e => setRefusMotif(e.target.value)}
                fullWidth
                required
                sx={{ mb: 1 }}
              >
                <MenuItem value="" disabled>Choisir un motif...</MenuItem>
                {REFUS_MOTIFS.map((motif, idx) => (
                  <MenuItem key={idx} value={motif}>{motif}</MenuItem>
                ))}
              </TextField>
              <Box sx={{ bgcolor: '#fafafa', border: '1px solid #eee', borderRadius: 1, p: 2, mx: 2, mb: 2 }}>
                <Typography variant="body2" sx={{ color: '#FF6B2C', fontWeight: 500, mb: 0.5 }}>
                  L'utilisateur sera averti par email et recevra une notification du refus de ce document.
                </Typography>
                <Typography variant="caption" sx={{ color: '#888', mt: 0.5, display: 'block' }}>
                  Pour supprimer définitivement sans avertir l'utilisateur, utilisez la corbeille.
                </Typography>
              </Box>
              {refusMotif === 'Autre (à préciser)' && (
                <Typography variant="caption" sx={{ color: '#d32f2f', mb: 1, display: 'block' }}>
                  Merci de préciser le motif dans le commentaire ci-dessous.
                </Typography>
              )}
              <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#222', mb: 1, mt: 2 }}>
                Commentaire complémentaire (optionnel)
              </Typography>
              <TextField
                label=""
                fullWidth
                multiline
                minRows={2}
                value={refusComment}
                onChange={e => setRefusComment(e.target.value)}
                sx={{ mt: 1 }}
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => { setCommentDialog(null); setRefusMotif(''); setRefusComment(''); }}>Annuler</Button>
            <Button
              onClick={() => {
                if (commentDialog?.doc && refusMotif) {
                  // On concatène le motif et le commentaire pour le backend
                  const motifFinal = refusComment ? `${refusMotif}\n${refusComment}` : refusMotif;
                  handleValidate(commentDialog.doc, 'rejeté', motifFinal);
                  setRefusMotif('');
                  setRefusComment('');
                }
              }}
              disabled={!refusMotif || actionLoading}
              sx={{ color: '#FF6B2C', fontWeight: 700 }}
            >Refuser</Button>
          </DialogActions>
        </Dialog>
        {/* Modale profil utilisateur */}
        {profileModalOpen && (
          profileLoading ? (
            <Dialog open={true}><DialogContent><CircularProgress sx={{ color: '#FF6B2C' }} /></DialogContent></Dialog>
          ) : profileData ? (
            <UserProfileModal isOpen={profileModalOpen} onClose={() => setProfileModalOpen(false)} userData={profileData} />
          ) : (
            <Dialog open={true}><DialogContent><Typography color="error">Impossible de charger le profil.</Typography></DialogContent></Dialog>
          )
        )}
        {zoomDocId && !confirmDialog.open && (() => {
          const doc = docs.find(d => d.id === zoomDocId);
          if (!doc || !doc.file_url) return null;
          return (
            <Box
              sx={{
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100vw',
                height: '100vh',
                bgcolor: 'rgba(0,0,0,0.45)',
                zIndex: 2000,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              onClick={() => setZoomDocId(null)}
            >
              <Paper
                sx={{
                  p: 2,
                  borderRadius: 3,
                  boxShadow: '0 8px 32px rgba(0,0,0,0.18)',
                  maxWidth: '90vw',
                  maxHeight: '90vh',
                  position: 'relative',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                }}
                onClick={e => e.stopPropagation()}
              >
                {/* Bouton Fermer en haut à droite */}
                <IconButton
                  onClick={() => setZoomDocId(null)}
                  sx={{
                    position: 'absolute',
                    top: 16,
                    right: 16,
                    color: '#FF6B2C',
                    bgcolor: 'white',
                    border: '1.5px solid #FF6B2C',
                    boxShadow: '0 1px 4px rgba(255,107,44,0.10)',
                    fontSize: '1.3rem',
                    p: '4px',
                    borderRadius: '50%',
                    zIndex: 1,
                    transition: 'all 0.18s',
                    '&:hover': {
                      color: '#d35400',
                      bgcolor: '#FFF8F3',
                      boxShadow: '0 2px 8px rgba(255,107,44,0.18)',
                      borderColor: '#d35400',
                    },
                  }}
                  size="medium"
                  aria-label="Fermer"
                >
                  <CloseIcon fontSize="inherit" />
                </IconButton>
                {doc.file_url.match(/\.(jpg|jpeg|png|gif|webp)$/i) ? (
                  unavailableImages[doc.id] ? (
                    <Typography color="error" fontSize={15} fontWeight={600}>Plus disponible</Typography>
                  ) : (
                    <img
                      src={doc.file_url}
                      alt="Document zoom"
                      style={{ maxHeight: '80vh', maxWidth: '80vw', borderRadius: 8, boxShadow: '0 2px 8px rgba(0,0,0,0.07)' }}
                      onError={() => setUnavailableImages(prev => ({ ...prev, [doc.id]: true }))}
                    />
                  )
                ) : doc.file_url.match(/\.pdf$/i) ? (
                  <iframe
                    src={doc.file_url}
                    title="Document PDF zoom"
                    style={{ width: '80vw', height: '80vh', border: 'none', borderRadius: 8, boxShadow: '0 2px 8px rgba(0,0,0,0.07)' }}
                  />
                ) : (
                  <Typography color="error">Format de document non supporté.</Typography>
                )}
                <Box sx={{ display: 'flex', gap: 2, mt: 3, mb: 1, justifyContent: 'center' }}>
                  {doc.status === 'pending' && (
                    <>
                      <Tooltip title="Valider ce document">
                        <span>
                          <IconButton onClick={() => setConfirmDialog({ open: true, action: 'accept', doc })} disabled={actionLoading}>
                            <CheckCircleIcon sx={{ color: '#4CAF50' }} />
                          </IconButton>
                        </span>
                      </Tooltip>
                      <Tooltip title="Refuser ce document">
                        <span>
                          <IconButton onClick={() => setCommentDialog({ open: true, doc })} disabled={actionLoading}>
                            <CancelIcon sx={{ color: '#FF6B2C' }} />
                          </IconButton>
                        </span>
                      </Tooltip>
                    </>
                  )}
                  <Tooltip title="Supprimer définitivement ce document">
                    <span>
                      <IconButton
                        onClick={() => handleDelete(doc)}
                        disabled={actionLoading}
                        sx={{ color: '#d32f2f', ml: 1, '&:hover': { color: '#b71c1c', bgcolor: 'rgba(211,47,47,0.08)' } }}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </span>
                  </Tooltip>
                </Box>
              </Paper>
            </Box>
          );
        })()}
        <Dialog open={confirmDialog.open} onClose={() => setConfirmDialog({ open: false, action: null, doc: null })} maxWidth="xs" fullWidth>
          <DialogTitle sx={{ color: '#FF6B2C', fontWeight: 700 }}>
            {confirmDialog.action === 'accept' && 'Valider ce document ?'}
            {confirmDialog.action === 'reject' && 'Refuser ce document ?'}
          </DialogTitle>
          {confirmDialog.action === 'accept' && confirmDialog.doc && (
            <DialogContent>
              <Box sx={{ mb: 2, p: 0 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#222', mb: 0.5 }}>
                  Type de document : <span style={{ fontWeight: 400 }}>{TYPE_LABELS[confirmDialog.doc.type] || confirmDialog.doc.type}</span>
                </Typography>
              </Box>
              <Box sx={{ p: 2, bgcolor: '#fafafa', borderRadius: 1, border: '1px solid #e0e0e0', mt: 1 }}>
                <Typography variant="body2" sx={{ color: '#444', fontStyle: 'italic' }}>
                  Après validation, tous les autres documents de type <b>{TYPE_LABELS[confirmDialog.doc.type] || confirmDialog.doc.type}</b> pour cet utilisateur seront automatiquement supprimés.
                </Typography>
              </Box>
            </DialogContent>
          )}
          {confirmDialog.action === 'delete' && confirmDialog.doc && (
            <DialogContent>
              <Box sx={{ mb: 2, p: 0 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#222', mb: 0.5 }}>
                  Type de document : <span style={{ fontWeight: 400 }}>{TYPE_LABELS[confirmDialog.doc.type] || confirmDialog.doc.type}</span>
                </Typography>
              </Box>
              <Box sx={{ p: 2, bgcolor: '#fff3e0', borderRadius: 1, border: '1px solid #ffb300', mt: 1 }}>
                <Typography variant="body2" sx={{ color: '#d32f2f', fontWeight: 500 }}>
                  ⚠️ Cette action supprimera définitivement le document du stockage et de la base de données.<br />
                  Aucun email ou notification ne sera envoyé à l'utilisateur.<br />
                  Les statuts de vérification ne seront pas modifiés.<br />
                  <b>Action irréversible.</b>
                </Typography>
              </Box>
            </DialogContent>
          )}
          <DialogActions>
            <Button onClick={() => setConfirmDialog({ open: false, action: null, doc: null })}>Annuler</Button>
            <Button
              sx={{ color: '#FF6B2C', fontWeight: 700 }}
              onClick={async () => {
                if (!confirmDialog.doc) return;
                if (confirmDialog.action === 'accept') {
                  await handleValidate(confirmDialog.doc, 'validé');
                } else if (confirmDialog.action === 'reject') {
                  setCommentDialog({ open: true, doc: confirmDialog.doc });
                } else if (confirmDialog.action === 'delete') {
                  await handleDelete(confirmDialog.doc);
                }
                setConfirmDialog({ open: false, action: null, doc: null });
                setZoomDocId(null);
              }}
              autoFocus
            >Confirmer</Button>
          </DialogActions>
        </Dialog>
        {/* Confirmation suppression tous les docs */}
        <Dialog open={deleteAllDialog} onClose={() => setDeleteAllDialog(false)} maxWidth="xs" fullWidth>
          <DialogTitle sx={{ color: '#FF6B2C', fontWeight: 700 }}>Supprimer tous les documents ?</DialogTitle>
          <DialogContent>
            <Typography>Cette action supprimera <b>tous</b> les documents de vérification entreprise de cet utilisateur. L'utilisateur sera notifié par email et notification. Continuer ?</Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteAllDialog(false)}>Annuler</Button>
            <Button onClick={handleDeleteAllDocs} color="error" variant="contained" disabled={deleteAllLoading} sx={{ fontWeight: 700, bgcolor: '#FF6B2C', '&:hover': { bgcolor: '#d35400' } }}>
              Confirmer
            </Button>
          </DialogActions>
        </Dialog>
        {/* Fenêtre de confirmation pour les switches d'urgence */}
        <Dialog open={forceStatusDialog.open} onClose={() => setForceStatusDialog({ open: false, field: null, value: false })}>
          <DialogTitle sx={{ color: '#FF6B2C', fontWeight: 700 }}>Confirmer l'action urgente</DialogTitle>
          <DialogContent>
            <Typography>
              Cette action va forcer le statut <b>{forceStatusDialog.field}</b> à <b>{forceStatusDialog.value ? 'oui' : 'non'}</b>.<br />
              <span style={{ color: '#d32f2f', fontWeight: 600 }}>Attention&nbsp;: l'utilisateur ne sera pas prévenu et les fichiers de validation ne seront pas mis à jour.</span>
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setForceStatusDialog({ open: false, field: null, value: false })}>Annuler</Button>
            <Button onClick={handleForceStatusConfirm} sx={{ color: '#FF6B2C', fontWeight: 700 }}>Confirmer</Button>
          </DialogActions>
        </Dialog>
        {/* Modale de confirmation pour demander à remplir les infos entreprise */}
        <Dialog open={remindDialog.open} onClose={() => setRemindDialog({ open: false, doc: null })} maxWidth="xs" fullWidth>
          <DialogTitle sx={{ color: '#FF6B2C', fontWeight: 700 }}>Demander à remplir les informations entreprise</DialogTitle>
          <DialogContent>
            <Typography>Voulez-vous envoyer un email à l'utilisateur pour lui demander de compléter ses informations entreprise ?</Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setRemindDialog({ open: false, doc: null })}>Annuler</Button>
            <Button
              onClick={async () => {
                setRemindLoading(true);
                try {
                  const headers = await getCommonHeaders();
                  const res = await fetch(`${API_CONFIG.baseURL}/api/users/entreprise/remind-infos`, {
                    method: 'POST',
                    headers: { ...headers, 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({ userId: user?.id })
                  });
                  const data = await res.json();
                  if (data.success) {
                    setSnackbar({ open: true, message: "Email envoyé à l'utilisateur.", severity: 'success' });
                  } else {
                    setSnackbar({ open: true, message: data.error || "Erreur lors de l'envoi de l'email.", severity: 'error' });
                  }
                } catch (e) {
                  setSnackbar({ open: true, message: "Erreur réseau ou serveur.", severity: 'error' });
                } finally {
                  setRemindLoading(false);
                  setRemindDialog({ open: false, doc: null });
                }
              }}
              color="warning"
              variant="contained"
              disabled={remindLoading}
              sx={{ fontWeight: 700, bgcolor: '#FFB300', color: '#fff', '&:hover': { bgcolor: '#FFA000' } }}
            >
              Confirmer
            </Button>
          </DialogActions>
        </Dialog>
        {/* Snackbar feedback */}
        <Snackbar open={snackbar.open} autoHideDuration={4000} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          <Alert onClose={() => setSnackbar({ ...snackbar, open: false })} severity={snackbar.severity} sx={{ width: '100%' }}>
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Container>
    </Box>
  );
};

export default EntrepriseDocumentDetailPage; 