import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// Fonction utilitaire pour le chunking des icônes MUI
const getMuiIconChunkName = (id: string) => {
  // Extraction du nom de l'icône
  const iconName = id.split('/').pop()?.split('.')[0];
  if (!iconName) return 'mui-icons-misc';

  // Groupement par catégorie
  if (['LocationOn', 'Place', 'Map', 'Navigation'].includes(iconName)) return 'mui-icons-location';
  if (['Person', 'People', 'Group', 'AccountCircle'].includes(iconName)) return 'mui-icons-people';
  if (['ShoppingCart', 'Store', 'LocalOffer', 'Payment'].includes(iconName)) return 'mui-icons-commerce';
  if (['Event', 'CalendarToday', 'Schedule', 'DateRange'].includes(iconName)) return 'mui-icons-calendar';
  if (['Star', 'Grade', 'Favorite', 'ThumbUp'].includes(iconName)) return 'mui-icons-ratings';
  if (['Warning', 'Error', 'Info', 'Help'].includes(iconName)) return 'mui-icons-alerts';
  if (['Edit', 'Delete', 'Add', 'Remove'].includes(iconName)) return 'mui-icons-actions';
  if (['Save', 'Upload', 'Download', 'Share'].includes(iconName)) return 'mui-icons-files';
  if (['Search', 'FilterList', 'Sort', 'ViewList'].includes(iconName)) return 'mui-icons-filters';
  if (['Message', 'Chat', 'Mail', 'Notifications'].includes(iconName)) return 'mui-icons-communication';

  // Autres icônes
  return 'mui-icons-misc';
};

export default defineConfig(({ mode }) => {
  // Charger les variables d'environnement
  const env = loadEnv(mode, process.cwd(), '');
  process.env = { ...process.env, ...env };

  // Log des variables pour le débogage
  console.log('Building with env:', {
    VITE_SUPABASE_URL: env.VITE_SUPABASE_URL,
    NODE_ENV: env.NODE_ENV
  });

  const envWithDefaults = {
    VITE_SUPABASE_URL: env.VITE_SUPABASE_URL || 'https://stockage.jobpartiel.fr',
    VITE_SUPABASE_ANON_KEY: env.VITE_SUPABASE_ANON_KEY,
    VITE_API_URL: env.VITE_API_URL || (env.NODE_ENV === 'production' ? 'https://api.jobpartiel.fr' : 'https://dev-api.jobpartiel.fr')
  };

  return {
    plugins: [react()],
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
        '@components': resolve(__dirname, './src/components'),
        '@pages': resolve(__dirname, './src/pages'),
        '@services': resolve(__dirname, './src/services'),
        '@contexts': resolve(__dirname, './src/contexts')
      }
    },
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@emotion/react',
        '@emotion/styled',
        '@mui/material',
        '@mui/system',
        '@mui/utils',
        'emoji-picker-react',
        '@tiptap/react',
        '@tiptap/starter-kit',
        '@tiptap/extension-color',
        '@tiptap/extension-table',
        '@tiptap/extension-table-cell',
        '@tiptap/extension-table-header',
      ],
      exclude: ['lucide-react'],
      esbuildOptions: {
        target: 'es2020'
      }
    },
    server: {
      port: parseInt(env.VITE_PORT) || 3000,
    },
    build: {
      target: 'es2020',
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: false,
      minify: 'terser',
      reportCompressedSize: true,
      chunkSizeWarningLimit: 1000,
      cssCodeSplit: true,
      modulePreload: {
        polyfill: true
      },
      rollupOptions: {
                input: {
          main: resolve(__dirname, 'index.html')
        },
        output: {
          manualChunks: {
            'react-vendor': ['react', 'react-dom', 'react-router-dom'],
            'mui-vendor': ['@mui/material', '@mui/system', '@mui/icons-material', '@emotion/react', '@emotion/styled'],
            'leaflet-vendor': ['leaflet', 'react-leaflet'],
            'emoji-picker': ['emoji-picker-react']
          },
          chunkFileNames: 'assets/[name]-[hash].js',
          entryFileNames: 'assets/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash][extname]'
        }
      },
      terserOptions: {
        compress: {
          dead_code: true,
          drop_console: true,
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.info', 'console.debug', 'console.warn'],
          passes: 3,
          toplevel: true,
          unsafe_math: true,
          unsafe_methods: true,
          reduce_vars: true,
          reduce_funcs: true,
          pure_getters: true,
          keep_infinity: true,
          // Optimisations supplémentaires
          arrows: true,
          booleans: true,
          collapse_vars: true,
          comparisons: true,
          computed_props: true,
          conditionals: true,
          evaluate: true,
          hoist_funs: true,
          hoist_props: true,
          hoist_vars: false,
          if_return: true,
          inline: true,
          join_vars: true,
          loops: true,
          negate_iife: true,
          properties: true,
          sequences: true,
          side_effects: true,
          switches: true,
          typeofs: true,
          unused: true
        },
        mangle: {
          toplevel: true,
          safari10: true
        },
        format: {
          comments: false,
          ecma: 2020
        }
      }
    },
    esbuild: {
      target: 'es2020',
      supported: {
        'top-level-await': true,
        'dynamic-import': true
      },
      jsx: 'automatic',  // Utilisation de la syntaxe "automatic"
      logOverride: { 'this-is-undefined-in-esm': 'silent' },
      treeShaking: true,
      minifyIdentifiers: true,
      minifySyntax: true,
      minifyWhitespace: true,
      drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : [] // Supprime les logs console et logger et debugger en production
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@import "./src/styles/variables.scss";'
        }
      }
    },
    define: {
      'import.meta.env': JSON.stringify({
        ...env,
        ...envWithDefaults,
        MODE: mode,
        DEV: mode === 'development',
        PROD: mode === 'production',
        VITE_API_URL: env.VITE_API_URL || (env.NODE_ENV === 'production' ? 'https://api.jobpartiel.fr' : 'https://dev-api.jobpartiel.fr')
      })
    }
  };
});
