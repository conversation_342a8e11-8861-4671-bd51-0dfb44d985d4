import axios from 'axios';
import { API_CONFIG } from '../../../config/api';
import logger from '../../../utils/logger';
import { getCommonHeaders } from '../../../utils/headers';
import { notify } from '../../../components/Notification';
import { updateJobiBalance } from '../../../utils/jobiUtils';
import { fetchCsrfToken } from '@/services/csrf';

export interface Mission {
  id: string;
  user_id: string;
  titre: string;
  description: string;
  budget: number;
  budget_defini: boolean;
  date_mission: string;
  ville: string;
  code_postal: string;
  is_urgent: boolean;
  is_closed: boolean;
  closed_at: string | null;
  statut: 'en_attente' | 'en_cours' | 'terminee' | 'annulee' | 'en_moderation';
  created_at: string;
  updated_at: string;
  category_id: string;
  subcategory_id: string;
  has_time_preference: boolean;
  time_slots: Array<{
    date: string;
    start: string;
    end: string;
  }>;
  payment_method: 'jobi_only' | 'direct_only' | 'both';
  photos: Array<{
    id: string;
    photo_url: string;
    order_index: number;
  }>;
  user_profile?: {
    nom: string;
    prenom: string;
    type_de_profil: 'particulier' | 'professionnel' | 'entreprise';
    photo_url: string;
    statut_entreprise?: string;
    slug?: string;
    profil_verifier?: boolean;
    identite_verifier?: boolean;
    is_online?: boolean;
    mode_vacance?: boolean;
    date_inscription?: string;
    assurance_verifier?: boolean;
    entreprise_verifier?: boolean;
  };
  applications_count?: number;
  last_application_time?: string;
  applications?: Array<{
    id: string;
    jobbeur_id: string;
    statut: 'en_attente' | 'acceptée' | 'refusée';
    created_at: string;
    jobbeur_profile?: {
      nom: string;
      prenom: string;
      photo_url: string;
    };
  }>;
  likes_count?: number;
  comments_count?: number;
  user_has_liked?: boolean;
  user_has_commented?: boolean;
  user_has_recommended?: boolean;
  intervention_zone?: {
    center: [number, number];
    radius: number;
    adresse?: string;
  };
  categories?: string[];
  subcategories?: string[];
  is_rejected: boolean;
}

interface JobbeurProfile {
  nom?: string;
  prenom?: string;
  photo_url?: string;
  type_de_profil?: string;
  assurance_verifier?: boolean;
  date_inscription?: string;
  entreprise_verifier?: boolean;
  identite_verifier?: boolean;
  is_online?: boolean;
  mode_vacance?: boolean;
  profil_verifier?: boolean;
  fullProfile?: any;
}

export interface Proposal {
  id: string;
  mission: Mission;
  statut: 'en_attente' | 'acceptée' | 'refusée' | 'contre_offre' | 'contre_offre_jobbeur';
  montant_propose: number;
  message: string;
  created_at: string;
  updated_at: string;
  montant_contre_offre?: number;
  message_contre_offre?: string;
  date_contre_offre?: string;
  montant_contre_offre_jobbeur?: number;
  message_contre_offre_jobbeur?: string;
  date_contre_offre_jobbeur?: string;
  jobbeur_profile?: JobbeurProfile;
  jobbeur_id?: string;
  mission_id?: string;
  time_slots?: Array<{
    date: string;
    start: string;
    end: string;
  }>;
  payment_status?: 'pending' | 'completed' | 'manual';
  payment_date?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  hasMore: boolean;
}

export interface Comment {
  id: string;
  user_id: string;
  mission_id: string;
  comment: string;
  created_at: string;
  is_private: boolean;
  parent_id: string | null;
  isOwnComment: boolean;
  canEdit: boolean;
  user: {
    id: string;
    profil_verifier: boolean;
    user_profil: Array<{
      id: string;
      user_id: string;
      nom: string | null;
      prenom: string | null;
      photo_url: string | null;
      type_de_profil?: string;
      statut_entreprise?: string;
      slug: string;
    }>;
    services: Array<{
      id: string;
      category_id: string;
      subcategory_id: string;
      statut: string;
    }>;
  };
  replies?: Comment[];
}

export interface CommentResponse {
  comments: Comment[];
  hasMore: boolean;
  total: number;
}

export interface FilterParams {
  status?: string[];
  search?: string;
  categories?: string[];
  subcategories?: string[];
  budget_types?: string[];
  payment_methods?: string[];
  is_urgent?: boolean;
  liked?: boolean;
  profile_types?: string[];
  offer_status?: string[];
  sort_by?: string;
  missionId?: string;
}

interface CategoryFilters {
  categories: string[];
  subcategories: string[];
}

interface RequestParams {
  page: number;
  limit: number;
  status?: string;
  budget_types?: string;
  payment_methods?: string;
  is_urgent?: boolean;
  liked?: boolean;
  search?: string;
  profile_types?: string;
  offer_status?: string;
  sort_by?: string;
  category_filters?: string;
  missionId?: string;
}

export async function reportComment(commentId: string, reason: string) {
  try {
    const headers = await getCommonHeaders();
    await fetchCsrfToken();
    headers['X-CSRF-Token'] = await fetchCsrfToken();
    const response = await axios.post(
      `${API_CONFIG.baseURL}/api/reported-content`,
      {
        content_type: 'comment',
        content_id: commentId,
        reason
      },
      {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      }
    );
    return response.data;
  } catch (error) {
    logger.error('Erreur lors du signalement du commentaire:', error);
    throw error;
  }
}

export async function reportMission(missionId: string, reason: string) {
  try {
    const headers = await getCommonHeaders();
    await fetchCsrfToken();
    headers['X-CSRF-Token'] = await fetchCsrfToken();
    const response = await axios.post(
      `${API_CONFIG.baseURL}/api/reported-content`,
      {
        content_type: 'mission',
        content_id: missionId,
        reason
      },
      {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      }
    );
    return response.data;
  } catch (error) {
    logger.error('Erreur lors du signalement de la mission:', error);
    throw error;
  }
}

export const missionsApi = {
  // Récupérer les missions d'un utilisateur
  getUserMissions: async (page: number = 1, limit: number = 10, filters?: FilterParams): Promise<PaginatedResponse<Mission>> => {
    try {
      const headers = await getCommonHeaders();
      logger.info('Envoi de la requête getUserMissions avec les paramètres:', {
        page,
        limit,
        filters
      });

      // Convertir les tableaux en format compatible avec l'API
      const params: Record<string, any> = {
        page,
        limit,
        ...filters,
        status: filters?.status ? Array.isArray(filters.status) ? filters.status.join(',') : filters.status : undefined,
        categories: filters?.categories ? Array.isArray(filters.categories) ? filters.categories.join(',') : filters.categories : undefined,
        subcategories: filters?.subcategories ? Array.isArray(filters.subcategories) ? filters.subcategories.join(',') : filters.subcategories : undefined,
        budget_types: filters?.budget_types ? Array.isArray(filters.budget_types) ? filters.budget_types.join(',') : filters.budget_types : undefined,
        payment_methods: filters?.payment_methods ? Array.isArray(filters.payment_methods) ? filters.payment_methods.join(',') : filters.payment_methods : undefined,
        offer_status: filters?.offer_status ? Array.isArray(filters.offer_status) ? filters.offer_status.join(',') : filters.offer_status : undefined,
        is_urgent: filters?.is_urgent ? true : undefined,
        liked: filters?.liked ? true : undefined,
        owner: true // Ajouter ce paramètre pour indiquer au backend de ne récupérer que les missions dont l'utilisateur est le propriétaire
      };

      // Supprimer les paramètres undefined
      Object.keys(params).forEach(key => {
        if (params[key] === undefined) {
          delete params[key];
        }
      });

      logger.info('Paramètres finaux de la requête:', params);

      const response = await axios.get(`${API_CONFIG.baseURL}/api/missions`, {
        headers,
        withCredentials: true,
        params
      });

      logger.info('Réponse brute de l\'API missions récupérées pour l\'utilisateur :', {
        status: response.status,
        headers: response.headers,
        data: response.data
      });

      // Si la réponse est un tableau simple, on le convertit au format paginé
      if (Array.isArray(response.data)) {
        logger.info('Conversion de la réponse en format paginé');
        return {
          data: response.data,
          hasMore: false,
          total: response.data.length
        };
      }

      // Si la réponse est déjà au format paginé, on la retourne telle quelle
      if (response.data && 'data' in response.data) {
        logger.info('Réponse déjà au format paginé:', {
          dataLength: response.data.data.length,
          hasMore: response.data.hasMore,
          total: response.data.total
        });
        return response.data;
      }

      // Si la réponse n'est pas dans un format attendu
      logger.error('Format de réponse inattendu:', response.data);
      return {
        data: [],
        hasMore: false,
        total: 0
      };
    } catch (error: any) {
      logger.error('Erreur lors de la récupération des missions:', {
        error,
        stack: error instanceof Error ? error.stack : undefined,
        response: error.response?.data
      });
      throw error;
    }
  },

  // Récupérer toutes les missions avec pagination
  getAllMissions: async (page: number = 1, limit: number = 10, filters?: FilterParams): Promise<PaginatedResponse<Mission>> => {
    try {
      const headers = await getCommonHeaders();
      const response = await axios.get(`${API_CONFIG.baseURL}/api/missions/all`, {
        headers,
        withCredentials: true,
        params: {
          page,
          limit,
          ...filters
        }
      });

      logger.info('Missions récupérées avec pagination:', response.data);

      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la récupération des missions:', error);
      throw error;
    }
  },

  // Récupérer les missions correspondant aux services de l'utilisateur
  getMatchingMissions: async (page: number = 1, showRejected: boolean = false, limit: number = 10, filters?: FilterParams): Promise<PaginatedResponse<Mission>> => {
    try {
      const headers = await getCommonHeaders();
      const response = await axios.get(`${API_CONFIG.baseURL}/api/missions/matching`, {
        headers,
        withCredentials: true,
        params: {
          page,
          limit,
          showRejected,
          ...filters
        }
      });

      logger.info('Missions correspondantes récupérées:', response.data);

      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la récupération des missions correspondantes:', error);
      throw error;
    }
  },

  // Rejeter une mission
  rejectMission: async (missionId: string): Promise<void> => {
    try {
      const headers = await getCommonHeaders();
      await fetchCsrfToken();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      logger.info('URL de la requête:', `${API_CONFIG.baseURL}/api/missions/${missionId}/reject`);
      logger.info('MissionId:', missionId);

      const response = await axios.post(`${API_CONFIG.baseURL}/api/missions/${missionId}/reject`, {}, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      }).catch(error => {
        if (error?.response?.status === 403 && error?.response?.data?.profileHidden) {
          notify(error.response.data.message, error.response.data.toastType || 'error');
          throw new Error('profile_hidden');
        }
        logger.error('Détails de l\'erreur axios:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          headers: error.response?.headers,
          config: {
            url: error.config?.url,
            method: error.config?.method,
            data: error.config?.data,
            headers: error.config?.headers
          }
        });
        throw error;
      });

      logger.info('Réponse du serveur:', response);
      notify('Mission rejetée avec succès', 'success');
    } catch (error: any) {
      if (error instanceof Error && error.message === 'profile_hidden') {
        // Toast déjà affiché
        return;
      }
      logger.error('Erreur complète lors du rejet de la mission:', error);
      logger.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace available');
      throw error;
    }
  },

  // Faire une proposition pour une mission
  makeProposal: async (missionId: string, amount: number, message: string) => {
    try {
      const headers = await getCommonHeaders();
      await fetchCsrfToken();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/missions/${missionId}/propose`,
        { amount, message },
        {
          headers: {
            ...headers,
            'Content-Type': 'application/json'
          },
          withCredentials: true
        }
      );
      return response.data;
    } catch (error: any) {
      if (error?.response?.status === 403 && error?.response?.data?.profileHidden) {
        notify(error.response.data.message, error.response.data.toastType || 'error');
        throw new Error('profile_hidden');
      }
      logger.error('Erreur lors de l\'envoi de la proposition:', error);
      throw error;
    }
  },

  // Fermer une mission
  closeMission: async (
    missionId: string,
    statut: 'terminee' | 'annulee',
    createJobiNotification?: (title: string, content: string) => Promise<boolean>
  ): Promise<any> => {
    try {
      const headers = await getCommonHeaders();
      await fetchCsrfToken();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await axios.post(`${API_CONFIG.baseURL}/api/missions/${missionId}/close`, {
        statut
      }, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });
      notify(`Mission ${statut === 'terminee' ? 'terminée' : 'annulée'} avec succès. Les nouvelles propositions sont désormais bloquées.`, 'success');

      // Si la mission est annulée, retirer les Jobi
      if (statut === 'annulee') {
        try {
          const success = await updateJobiBalance({
            montant: 1,
            operation: 'moins',
            titre: 'Mission annulée',
            description: '1 Jobi retiré suite à l\'annulation de la mission'
          });

          if (success) {
            notify('1 Jobi a été retiré suite à l\'annulation de la mission', 'info');

            try {
              // Créer une notification pour la perte de Jobi
              const { data: mission } = await axios.get(`${API_CONFIG.baseURL}/api/missions/${missionId}/details`, {
                headers: await getCommonHeaders(),
                withCredentials: true
              });

              if (createJobiNotification) {
                await createJobiNotification(
                  'Jobi retiré',
                  `Vous avez perdu 1 Jobi suite à l'annulation de la mission "${mission.titre}"`
                );
              }
            } catch (notifError) {
              logger.error('Erreur lors de la création de la notification Jobi:', notifError);
              // Ne pas bloquer le processus si la notification échoue
            }
          } else {
            notify('Erreur lors du retrait des Jobis, contactez le support impérativement', 'error');
            logger.error('Échec du retrait des Jobis lors de l\'annulation de la mission');
          }
        } catch (jobiError) {
          logger.error('Erreur lors du retrait des Jobis:', jobiError);
          notify('Erreur lors du retrait des Jobis, la mission a bien été annulée mais le retrait des Jobis a échoué', 'warning');
        }
      }

      return response.data;
    } catch (error: any) {
      if (error?.response?.status === 403 && error?.response?.data?.profileHidden) {
        notify(error.response.data.message, error.response.data.toastType || 'error');
        throw new Error('profile_hidden');
      }
      logger.error('Erreur lors de la fermeture de la mission:', error);
      throw error;
    }
  },

  // Liker une mission
  likeMission: async (missionId: string): Promise<void> => {
    try {
      const headers = await getCommonHeaders();
      await fetchCsrfToken();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      await axios.post(`${API_CONFIG.baseURL}/api/missions/${missionId}/like`, {}, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });
    } catch (error: any) {
      if (error?.response?.status === 403 && error?.response?.data?.profileHidden) {
        notify(error.response.data.message, error.response.data.toastType || 'error');
        throw new Error('profile_hidden');
      }
      logger.error('Erreur lors du like de la mission:', error);
      throw error;
    }
  },

  // Commenter une mission
  commentMission: async (missionId: string, comment: string, isPrivate: boolean = false, parentId?: string): Promise<Comment> => {
    try {
      const headers = await getCommonHeaders();
      await fetchCsrfToken();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await axios.post(`${API_CONFIG.baseURL}/api/missions/${missionId}/comment`, {
        comment,
        isPrivate,
        parentId
      }, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });
      return response.data;
    } catch (error: any) {
      if (error?.response?.status === 403 && error?.response?.data?.profileHidden) {
        notify(error.response.data.message, error.response.data.toastType || 'error');
        throw new Error('profile_hidden');
      }
      logger.error('Erreur lors du commentaire de la mission:', error);
      throw error;
    }
  },

  // Recommander une mission
  recommendMission: async (missionId: string): Promise<void> => {
    try {
      const headers = await getCommonHeaders();
      await fetchCsrfToken();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      await axios.post(`${API_CONFIG.baseURL}/api/missions/${missionId}/recommend`, {}, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });
    } catch (error: any) {
      if (error?.response?.status === 403 && error?.response?.data?.profileHidden) {
        notify(error.response.data.message, error.response.data.toastType || 'error');
        throw new Error('profile_hidden');
      }
      logger.error('Erreur lors de la recommandation de la mission:', error);
      throw error;
    }
  },

  // Récupérer les détails d'une mission
  getMissionDetails: async (missionId: string): Promise<Mission> => {
    try {
      const headers = await getCommonHeaders();
      const response = await axios.get(`${API_CONFIG.baseURL}/api/missions/${missionId}/details`, {
        headers,
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la récupération des détails de la mission:', error);
      throw error;
    }
  },

  // Récupérer les commentaires d'une mission
  getMissionComments: async (missionId: string, page: number = 1, limit: number = 30): Promise<CommentResponse> => {
    try {
      const headers = await getCommonHeaders();
      const response = await axios.get(`${API_CONFIG.baseURL}/api/missions/${missionId}/comments`, {
        headers,
        withCredentials: true,
        params: {
          page,
          limit
        }
      });
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la récupération des commentaires:', error);
      throw error;
    }
  },

  // Modifier un commentaire
  updateComment: async (missionId: string, commentId: string, comment: string): Promise<Comment> => {
    try {
      const headers = await getCommonHeaders();
      const response = await axios.put(`${API_CONFIG.baseURL}/api/missions/${missionId}/comments/${commentId}`, {
        comment
      }, {
        headers,
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la modification du commentaire:', error);
      throw error;
    }
  },

  // Supprimer un commentaire
  deleteComment: async (missionId: string, commentId: string): Promise<void> => {
    try {
      const headers = await getCommonHeaders();
      await axios.delete(`${API_CONFIG.baseURL}/api/missions/${missionId}/comments/${commentId}`, {
        headers,
        withCredentials: true
      });
    } catch (error) {
      logger.error('Erreur lors de la suppression du commentaire:', error);
      throw error;
    }
  },

  // Récupérer les propositions de l'utilisateur
  getMyProposals: async (page: number = 1, limit: number = 10, filters?: FilterParams): Promise<PaginatedResponse<Proposal>> => {
    try {
      const headers = await getCommonHeaders();

      // Structurer les filtres de catégories
      let categoryFilters: CategoryFilters | null = null;
      if (filters?.categories?.length || filters?.subcategories?.length) {
        categoryFilters = {
          categories: filters.categories || [],
          subcategories: filters.subcategories || []
        };
      }

      const params: RequestParams = {
        page,
        limit,
        status: filters?.status ? filters.status.join(',') : undefined,
        budget_types: filters?.budget_types ? filters.budget_types.join(',') : undefined,
        payment_methods: filters?.payment_methods ? filters.payment_methods.join(',') : undefined,
        is_urgent: filters?.is_urgent ? true : undefined,
        liked: filters?.liked ? true : undefined,
        search: filters?.search,
        profile_types: filters?.profile_types ? filters.profile_types.join(',') : undefined,
        offer_status: filters?.offer_status && filters.offer_status.length > 0 ? filters.offer_status.join(',') : undefined,
        sort_by: filters?.sort_by,
        missionId: (filters as any)?.missionId,
        category_filters: categoryFilters ? JSON.stringify(categoryFilters) : undefined
      };

      // Supprimer les paramètres undefined et les chaînes vides
      Object.keys(params).forEach(key => {
        if (params[key as keyof typeof params] === undefined || params[key as keyof typeof params] === '') {
          delete params[key as keyof typeof params];
        }
      });

      // logger.info('Paramètres de requête pour getMyProposals:', params);
      // logger.info('Détail des filtres offer_status:', {
        // original: filters?.offer_status,
        // joined: filters?.offer_status && filters.offer_status.length > 0 ? filters.offer_status.join(',') : undefined,
        // final: params.offer_status
      // });

      const response = await axios.get(`${API_CONFIG.baseURL}/api/missions/propositions/sent`, {
        headers,
        withCredentials: true,
        params
      });

      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la récupération des propositions:', error);
      throw error;
    }
  },

  getReceivedProposals: async (page: number = 1, limit: number = 10, filters?: FilterParams): Promise<PaginatedResponse<Proposal>> => {
    try {
      const headers = await getCommonHeaders();

      // Structurer les filtres de catégories
      let categoryFilters: CategoryFilters | null = null;
      if (filters?.categories?.length || filters?.subcategories?.length) {
        categoryFilters = {
          categories: filters.categories || [],
          subcategories: filters.subcategories || []
        };
      }

      const params: RequestParams = {
        page,
        limit,
        status: filters?.status ? filters.status.join(',') : undefined,
        budget_types: filters?.budget_types ? filters.budget_types.join(',') : undefined,
        payment_methods: filters?.payment_methods ? filters.payment_methods.join(',') : undefined,
        is_urgent: filters?.is_urgent ? true : undefined,
        liked: filters?.liked ? true : undefined,
        search: filters?.search,
        profile_types: filters?.profile_types ? filters.profile_types.join(',') : undefined,
        offer_status: filters?.offer_status && filters.offer_status.length > 0 ? filters.offer_status.join(',') : undefined,
        sort_by: filters?.sort_by,
        missionId: (filters as any)?.missionId,
        category_filters: categoryFilters ? JSON.stringify(categoryFilters) : undefined
      };

      // Supprimer les paramètres undefined et les chaînes vides
      Object.keys(params).forEach(key => {
        if (params[key as keyof typeof params] === undefined || params[key as keyof typeof params] === '') {
          delete params[key as keyof typeof params];
        }
      });

      // logger.info('Paramètres de requête pour getReceivedProposals:', params);
      // logger.info('Détail des filtres offer_status:', {
      //   original: filters?.offer_status,
      //   joined: filters?.offer_status && filters.offer_status.length > 0 ? filters.offer_status.join(',') : undefined,
      //   final: params.offer_status
      // });

      const response = await axios.get(`${API_CONFIG.baseURL}/api/missions/propositions/received`, {
        headers,
        withCredentials: true,
        params
      });

      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la récupération des propositions reçues:', error);
      throw error;
    }
  },

  // Accepter une proposition
  acceptProposal: async (missionId: string, proposalId: string) => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/missions/${missionId}/proposals/${proposalId}/accept`,
        {},
        {
          headers,
          withCredentials: true
        }
      );
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de l\'acceptation de la proposition:', error);
      throw error;
    }
  },

  // Refuser une proposition
  rejectProposal: async (missionId: string, proposalId: string) => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/missions/${missionId}/proposals/${proposalId}/reject`,
        {},
        {
          headers,
          withCredentials: true
        }
      );
      return response.data;
    } catch (error) {
      logger.error('Erreur lors du refus de la proposition:', error);
      throw error;
    }
  },

  // Faire une contre-offre
  makeCounterOffer: async (missionId: string, proposalId: string, amount: number, message: string) => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/missions/${missionId}/counter-offer`,
        {
          proposalId,
          amount,
          message
        },
        {
          headers,
          withCredentials: true
        }
      );
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de l\'envoi de la contre-offre:', error);
      throw error;
    }
  },

  // Faire une contre-offre (jobbeur)
  makeJobbeurCounterOffer: async (missionId: string, proposalId: string, amount: number, message: string) => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/missions/${missionId}/jobbeur-counter-offer`,
        {
          proposalId,
          amount,
          message
        },
        {
          headers,
          withCredentials: true
        }
      );
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de l\'envoi de la contre-offre du jobbeur:', error);
      throw error;
    }
  },

  // Récupérer les statistiques des propositions
  getProposalStats: async (isSent: boolean = true, filters?: FilterParams): Promise<{
    total: number;
    enAttente: number;
    acceptees: number;
    refusees: number;
    contreOffres: number;
    montantMoyen: number;
    montantTotal: number;
    sentProposals: number;
    receivedProposals: number;
  }> => {
    try {
      const headers = await getCommonHeaders();

      // Structurer les filtres de catégories
      let categoryFilters: CategoryFilters | null = null;
      if (filters?.categories?.length || filters?.subcategories?.length) {
        categoryFilters = {
          categories: filters.categories || [],
          subcategories: filters.subcategories || []
        };
      }

      const params: RequestParams = {
        page: 1,
        limit: 1, // On ne récupère qu'une seule proposition car on veut juste les stats
        status: filters?.status ? filters.status.join(',') : undefined,
        budget_types: filters?.budget_types ? filters.budget_types.join(',') : undefined,
        payment_methods: filters?.payment_methods ? filters.payment_methods.join(',') : undefined,
        is_urgent: filters?.is_urgent ? true : undefined,
        liked: filters?.liked ? true : undefined,
        search: filters?.search,
        profile_types: filters?.profile_types ? filters.profile_types.join(',') : undefined,
        offer_status: filters?.offer_status && filters.offer_status.length > 0 ? filters.offer_status.join(',') : undefined,
        sort_by: filters?.sort_by,
        missionId: (filters as any)?.missionId,
        category_filters: categoryFilters ? JSON.stringify(categoryFilters) : undefined
      };

      // Supprimer les paramètres undefined et les chaînes vides
      Object.keys(params).forEach(key => {
        if (params[key as keyof typeof params] === undefined || params[key as keyof typeof params] === '') {
          delete params[key as keyof typeof params];
        }
      });

      // logger.info('Récupération des statistiques des propositions avec les filtres:', params);

      const endpoint = isSent
        ? `${API_CONFIG.baseURL}/api/missions/propositions/sent/stats`
        : `${API_CONFIG.baseURL}/api/missions/propositions/received/stats`;

      const response = await axios.get(endpoint, {
        headers,
        withCredentials: true,
        params
      });

      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la récupération des statistiques des propositions:', error);
      // Retourner des statistiques par défaut en cas d'erreur
      return {
        total: 0,
        enAttente: 0,
        acceptees: 0,
        refusees: 0,
        contreOffres: 0,
        montantMoyen: 0,
        montantTotal: 0,
        sentProposals: 0,
        receivedProposals: 0
      };
    }
  },

  // Vérifier si l'utilisateur a déjà fait une offre pour une mission spécifique
  getUserProposalForMission: async (missionId: string): Promise<Proposal | null> => {
    try {
      const headers = await getCommonHeaders();
      const response = await axios.get(
        `${API_CONFIG.baseURL}/api/missions/${missionId}/user-proposal`,
        {
          headers,
          withCredentials: true
        }
      );

      return response.data.proposal || null;
    } catch (error) {
      logger.error('Erreur lors de la vérification de la proposition de l\'utilisateur:', error);
      return null;
    }
  },

  // Fonction pour mettre à jour le statut de paiement d'une proposition
  updateProposalPaymentStatus: async (missionId: string, proposalId: string, status: 'pending' | 'completed' | 'manual', montant_paiement?: number): Promise<any> => {
    try {
      const headers = await getCommonHeaders();

      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/missions/propositions/${missionId}/${proposalId}/payment-status`,
        {
          status,
          montant_paiement
        },
        {
          headers,
          withCredentials: true
        }
      );

      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la mise à jour du statut de paiement:', error);
      throw error;
    }
  },

  // Fonction pour récupérer une proposition spécifique par son ID
  getProposalById: async (missionId: string, proposalId: string): Promise<Proposal | null> => {
    try {
      const headers = await getCommonHeaders();

      const response = await axios.get(
        `${API_CONFIG.baseURL}/api/missions/propositions/${missionId}/${proposalId}`,
        {
          headers,
          withCredentials: true
        }
      );

      if (response.data.success && response.data.proposal) {
        return response.data.proposal;
      }

      return null;
    } catch (error) {
      logger.error('Erreur lors de la récupération de la proposition:', error);
      return null;
    }
  },
};