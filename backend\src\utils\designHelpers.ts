/**
 * Utilitaires pour générer des designs sophistiqués pour les cartes de visite et flyers
 *
 * AMÉLIORATIONS :
 * 1. Génération de palettes de couleurs intelligentes adaptées au secteur
 * 2. Création de formes SVG organiques, géométriques et fluides
 * 3. Génération de points de dessins (drawing) variés et naturels
 * 4. Adaptation des designs aux secteurs d'activité des utilisateurs
 * 5. Randomisation intelligente des éléments visuels
 * 6. Amélioration du contraste et de la profondeur visuelle
 * 7. Génération de prompts plus sophistiqués pour l'IA
 *
 * Ces outils permettent de:
 * - Améliorer la qualité visuelle des templates générés par l'IA
 * - Créer une plus grande variété de designs
 * - Offrir une meilleure expérience utilisateur avec des designs plus pertinents
 * - Maintenir la compatibilité avec Konva.js
 */

/**
 * Génère une palette de couleurs harmonieuse
 * @param colorScheme Type de schéma de couleurs ("monochrome", "complementary", "analogous", "triadic", "warm", "cool")
 * @param baseColor Couleur de base hexadécimale (optionnelle)
 * @returns Palette de couleurs avec couleur principale, accents et nuances
 */
export const generateColorPalette = (colorScheme?: string, baseColor?: string) => {
  // Si aucun schéma n'est spécifié, en choisir un aléatoirement
  if (!colorScheme) {
    const schemes = ["monochrome", "complementary", "analogous", "triadic", "warm", "cool", "professional"];
    colorScheme = schemes[Math.floor(Math.random() * schemes.length)];
  }

  // Couleur de JobPartiel ou couleur aléatoire
  const mainColor = baseColor || "#FF6B2C";
  const mainColorRGB = hexToRgb(mainColor);

  let colors: string[] = [];
  let nuances: string[] = [];

  switch (colorScheme) {
    case "monochrome":
      // Plusieurs teintes de la même couleur
      colors = [
        mainColor,
        shadeColor(mainColor, 20),
        shadeColor(mainColor, -20),
      ];
      nuances = [
        shadeColor(mainColor, 40),
        shadeColor(mainColor, -40),
      ];
      break;

    case "complementary":
      // Couleur opposée sur la roue chromatique
      const complementary = getComplementaryColor(mainColor);
      colors = [
        mainColor,
        complementary,
        shadeColor(mainColor, 20),
      ];
      nuances = [
        shadeColor(complementary, 20),
        shadeColor(mainColor, -20),
      ];
      break;

    case "professional":
      // Palette professionnelle plus sobre et élégante
      colors = [
        "#333333", // Couleur principale sombre
        "#0066CC", // Bleu professionnel
        "#666666", // Gris moyen
      ];
      nuances = [
        "#003366", // Bleu foncé
        "#CCCCCC", // Gris clair
      ];
      break;

    case "analogous":
      // Couleurs adjacentes sur la roue chromatique
      const analogous1 = getAnalogousColor(mainColor, 30);
      const analogous2 = getAnalogousColor(mainColor, -30);
      colors = [
        mainColor,
        analogous1,
        analogous2,
      ];
      nuances = [
        shadeColor(analogous1, 20),
        shadeColor(analogous2, -20),
      ];
      break;

    case "triadic":
      // Trois couleurs équidistantes sur la roue chromatique
      const triadic1 = getAnalogousColor(mainColor, 120);
      const triadic2 = getAnalogousColor(mainColor, 240);
      colors = [
        mainColor,
        triadic1,
        triadic2,
      ];
      nuances = [
        shadeColor(triadic1, 20),
        shadeColor(triadic2, 20),
      ];
      break;

    case "warm":
      // Palette de couleurs chaudes
      colors = [
        mainColor,
        "#FF8C42", // Orange
        "#E85D04", // Rouge orangé
      ];
      nuances = [
        "#FFC15E", // Jaune orangé
        "#D00000", // Rouge
      ];
      break;

    case "cool":
      // Palette de couleurs froides
      colors = [
        "#0077B6", // Bleu
        "#00B4D8", // Bleu clair
        "#90E0EF", // Bleu très clair
      ];
      nuances = [
        "#0096C7", // Bleu moyen
        "#48CAE4", // Bleu-vert
      ];
      break;

    default:
      // Palette aléatoire par défaut
      colors = [
        mainColor,
        getRandomColor(),
        getRandomColor(),
      ];
      nuances = [
        getRandomColor(),
        getRandomColor(),
      ];
  }

  // Génération de dégradés
  const gradient1 = `linear-gradient(45deg, ${colors[0]}, ${colors[1]})`;
  const gradient2 = `linear-gradient(135deg, ${colors[1]}, ${colors[2]})`;

  return {
    mainColor: colors[0],
    secondaryColor: colors[1],
    tertiaryColor: colors[2],
    accentColor1: nuances[0],
    accentColor2: nuances[1],
    gradient1,
    gradient2,
    backgroundLight: shadeColor("#FFFFFF", -5),
    backgroundDark: shadeColor("#333333", 10),
    colors, // Tableau complet
    nuances, // Tableau complet
  };
};

/**
 * Génère un SVG Path aléatoire pour des formes organiques et décoratives
 * @param complexity Niveau de complexité de la forme (1-5)
 * @param type Type de forme ("wave", "blob", "geometric", "spiral", "custom", "random")
 * @returns Données de chemin SVG
 */
export const generateSvgPath = (complexity: number = 3, type: string = "random") => {
  // Si type est random, choisir aléatoirement
  if (type === "random") {
    const types = ["wave", "blob", "geometric", "spiral", "custom"];
    type = types[Math.floor(Math.random() * types.length)];
  }

  complexity = Math.min(5, Math.max(1, complexity)); // Entre 1 et 5

  switch (type) {
    case "wave":
      return generateWavePath(complexity);

    case "blob":
      return generateBlobPath(complexity);

    case "geometric":
      return generateGeometricPath(complexity);

    case "spiral":
      return generateSpiralPath(complexity);

    case "custom":
      return getSvgFromLibrary();

    default:
      return generateWavePath(complexity);
  }
};

/**
 * Génère des positions aléatoires pour les éléments en évitant les zones occupées
 * @param templateWidth Largeur du template
 * @param templateHeight Hauteur du template
 * @param elementWidth Largeur de l'élément à positionner
 * @param elementHeight Hauteur de l'élément à positionner
 * @param occupiedZones Zones déjà occupées par d'autres éléments
 * @param margin Marge minimale entre les éléments
 * @returns Position {x, y} optimale
 */
export const generateSmartPosition = (
  templateWidth: number,
  templateHeight: number,
  elementWidth: number,
  elementHeight: number,
  occupiedZones: Array<{x: number, y: number, width: number, height: number}> = [],
  margin: number = 20
) => {
  const maxAttempts = 50;
  let attempts = 0;

  while (attempts < maxAttempts) {
    // Générer une position aléatoire avec marges
    const x = margin + Math.random() * (templateWidth - elementWidth - 2 * margin);
    const y = margin + Math.random() * (templateHeight - elementHeight - 2 * margin);

    // Vérifier si cette position entre en collision avec les zones occupées
    const hasCollision = occupiedZones.some(zone => {
      return !(x > zone.x + zone.width + margin ||
               x + elementWidth + margin < zone.x ||
               y > zone.y + zone.height + margin ||
               y + elementHeight + margin < zone.y);
    });

    if (!hasCollision) {
      return { x, y };
    }

    attempts++;
  }

  // Si aucune position libre trouvée, utiliser une position par défaut
  const fallbackPositions = [
    { x: margin, y: margin }, // Coin haut-gauche
    { x: templateWidth - elementWidth - margin, y: margin }, // Coin haut-droite
    { x: margin, y: templateHeight - elementHeight - margin }, // Coin bas-gauche
    { x: templateWidth - elementWidth - margin, y: templateHeight - elementHeight - margin }, // Coin bas-droite
    { x: templateWidth / 2 - elementWidth / 2, y: margin }, // Centre-haut
  ];

  return fallbackPositions[Math.floor(Math.random() * fallbackPositions.length)];
};

/**
 * Génère des layouts aléatoires pour les éléments texte
 * @param templateWidth Largeur du template
 * @param templateHeight Hauteur du template
 * @param textElements Nombre d'éléments texte à positionner
 * @returns Array de positions avec alignements
 */
export const generateRandomTextLayout = (templateWidth: number, templateHeight: number, textElements: number) => {
  const layouts = [];
  const occupiedZones: Array<{x: number, y: number, width: number, height: number}> = [];
  const alignments = ['left', 'center', 'right'];

  for (let i = 0; i < textElements; i++) {
    // Dimensions approximatives d'un élément texte
    const textWidth = 150 + Math.random() * 100; // 150-250px
    const textHeight = 30 + Math.random() * 20; // 30-50px

    // Générer une position intelligente
    const position = generateSmartPosition(templateWidth, templateHeight, textWidth, textHeight, occupiedZones);

    // Ajouter cette zone aux zones occupées
    occupiedZones.push({
      x: position.x,
      y: position.y,
      width: textWidth,
      height: textHeight
    });

    // Choisir un alignement aléatoire
    const alignment = alignments[Math.floor(Math.random() * alignments.length)];

    layouts.push({
      ...position,
      width: textWidth,
      height: textHeight,
      alignment
    });
  }

  return layouts;
};

/**
 * Génère une liste de points aléatoires pour un dessin au pinceau
 * @param width Largeur du cadre
 * @param height Hauteur du cadre
 * @param points Nombre de points à générer
 * @param style Style de dessin ("fluid", "straight", "zigzag", "random")
 * @returns Tableau de points [x1, y1, x2, y2, ...]
 */
export const generateDrawingPoints = (width: number, height: number, points: number = 10, style: string = "fluid") => {
  if (style === "random") {
    const styles = ["fluid", "straight", "zigzag"];
    style = styles[Math.floor(Math.random() * styles.length)];
  }

  const result: number[] = [];
  const startX = Math.random() * (width / 4);
  const startY = Math.random() * (height / 4);

  result.push(startX, startY);

  let prevX = startX;
  let prevY = startY;

  for (let i = 1; i < points; i++) {
    let nextX, nextY;

    switch (style) {
      case "fluid":
        // Courbes douces et organiques
        nextX = prevX + (Math.random() * 2 - 1) * (width / points) * 2;
        nextY = prevY + (Math.random() * 2 - 1) * (height / points) * 2;
        break;

      case "straight":
        // Lignes plus directes
        nextX = prevX + (width / points) * (1 + Math.random() * 0.5);
        nextY = prevY + (height / points) * (Math.random() * 0.5 - 0.25);
        break;

      case "zigzag":
        // Effet en zigzag
        nextX = prevX + (width / points) * (1 + Math.random() * 0.3);
        nextY = prevY + (height / points) * (Math.random() > 0.5 ? 1 : -1) * (0.5 + Math.random() * 0.5);
        break;

      default:
        nextX = prevX + (Math.random() * 2 - 1) * (width / points);
        nextY = prevY + (Math.random() * 2 - 1) * (height / points);
    }

    // Garder les points dans les limites
    nextX = Math.max(0, Math.min(width, nextX));
    nextY = Math.max(0, Math.min(height, nextY));

    result.push(nextX, nextY);

    prevX = nextX;
    prevY = nextY;
  }

  return result;
};

/**
 * Génère une configuration d'éléments optimale selon le secteur d'activité
 * @param sector Secteur d'activité
 * @returns Configuration adaptée au secteur
 */
export const getSectorConfig = (sector: string = "") => {
  // Normalisation du secteur (tout en minuscules, sans accents)
  sector = sector.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g, "");

  // Configurations par défaut
  const defaultConfig = {
    colorScheme: "complementary",
    shapes: ["rect", "circle", "polygon"],
    drawingStyle: "fluid",
    complexity: 3,
    svgType: Math.random() < 0.3 ? "custom" : "wave",
    includeDrawing: Math.random() < 0.4,
    includeShapes: true,
    shapeCount: 4 + Math.floor(Math.random() * 4), // 4-8
  };

  // Configurations spécifiques par secteur

  // Professionnel indépendant (nouveau)
  if (sector.includes("independant") || sector.includes("freelance") || sector.includes("consultant") ||
      sector.includes("liberale") || sector.includes("profession liberale") || sector.includes("auto entrepreneur") ||
      sector === "") { // Par défaut pour les profils vides
    return {
      colorScheme: "professional",
      shapes: ["rect", "circle", "polygon"],
      drawingStyle: "clean",
      complexity: 3,
      svgType: "geometric",
      includeDrawing: Math.random() < 0.3,
      includeShapes: true,
      shapeCount: 3 + Math.floor(Math.random() * 3), // 3-6, plus minimaliste
    };
  }

  // Beauté et Bien-être
  if (sector.includes("beaute") || sector.includes("esthetique") || sector.includes("coiffure") ||
      sector.includes("maquillage") || sector.includes("cosmetique") || sector.includes("spa") ||
      sector.includes("massage") || sector.includes("manucure")) {
    return {
      colorScheme: Math.random() > 0.5 ? "analogous" : "monochrome",
      shapes: ["circle", "ellipse", "path"],
      drawingStyle: "fluid",
      complexity: 4,
      svgType: Math.random() < 0.6 ? "custom" : "wave",
      includeDrawing: Math.random() < 0.6,
      includeShapes: true,
      shapeCount: 5 + Math.floor(Math.random() * 3), // 5-8
    };
  }

  // Jardinage et Paysagisme
  if (sector.includes("jardin") || sector.includes("paysag") || sector.includes("fleur") ||
      sector.includes("horticul") || sector.includes("botanique") || sector.includes("plant") ||
      sector.includes("agricult") || sector.includes("pepiniere")) {
    return {
      colorScheme: "analogous",
      baseColor: "#4CAF50", // Vert
      shapes: ["path", "circle", "ellipse"],
      drawingStyle: "fluid",
      complexity: 4,
      svgType: Math.random() < 0.6 ? "custom" : "blob",
      includeDrawing: Math.random() < 0.7,
      includeShapes: true,
      shapeCount: 5 + Math.floor(Math.random() * 4), // 5-9
    };
  }

  // Construction et BTP
  if (sector.includes("btp") || sector.includes("construction") || sector.includes("batiment") ||
      sector.includes("architecte") || sector.includes("maconnerie") || sector.includes("genie civil") ||
      sector.includes("charpente") || sector.includes("menuiserie") || sector.includes("toiture")) {
    return {
      colorScheme: "complementary",
      shapes: ["rect", "line", "polygon"],
      drawingStyle: "straight",
      complexity: 3,
      svgType: Math.random() < 0.6 ? "custom" : "geometric",
      includeDrawing: Math.random() < 0.3,
      includeShapes: true,
      shapeCount: 4 + Math.floor(Math.random() * 3), // 4-7
    };
  }

  // Santé et Médical
  if (sector.includes("sante") || sector.includes("medical") || sector.includes("infirm") ||
      sector.includes("pharma") || sector.includes("medecin") || sector.includes("dentiste") ||
      sector.includes("kine") || sector.includes("osteo") || sector.includes("therapie") ||
      sector.includes("psycho") || sector.includes("soins")) {
    return {
      colorScheme: "cool",
      shapes: ["circle", "path", "arc"],
      drawingStyle: "fluid",
      complexity: 2,
      svgType: Math.random() < 0.6 ? "custom" : "wave",
      includeDrawing: Math.random() < 0.3,
      includeShapes: true,
      shapeCount: 3 + Math.floor(Math.random() * 3), // 3-6
    };
  }

  // Informatique et Technologie
  if (sector.includes("info") || sector.includes("tech") || sector.includes("digital") ||
      sector.includes("web") || sector.includes("develop") || sector.includes("logiciel") ||
      sector.includes("reseau") || sector.includes("program") || sector.includes("donnee") ||
      sector.includes("data") || sector.includes("ai") || sector.includes("internet")) {
    return {
      colorScheme: "triadic",
      shapes: ["polygon", "rect", "line"],
      drawingStyle: "zigzag",
      complexity: 4,
      svgType: Math.random() < 0.6 ? "custom" : "geometric",
      includeDrawing: Math.random() < 0.5,
      includeShapes: true,
      shapeCount: 5 + Math.floor(Math.random() * 4), // 5-9
    };
  }

  // Nettoyage et Entretien
  if (sector.includes("nettoyage") || sector.includes("menage") || sector.includes("proprete") ||
      sector.includes("entretien") || sector.includes("hygiene") || sector.includes("lavage")) {
    return {
      colorScheme: "cool",
      shapes: ["circle", "ellipse", "path"],
      drawingStyle: "fluid",
      complexity: 3,
      svgType: Math.random() < 0.5 ? "custom" : "wave",
      includeDrawing: Math.random() < 0.4,
      includeShapes: true,
      shapeCount: 4 + Math.floor(Math.random() * 3), // 4-7
    };
  }

  // Artisanat et Métiers d'art
  if (sector.includes("artisan") || sector.includes("art") || sector.includes("metier d'art") ||
      sector.includes("createur") || sector.includes("bijou") || sector.includes("poterie") ||
      sector.includes("ceramique") || sector.includes("sculpture") || sector.includes("couture")) {
    return {
      colorScheme: "warm",
      shapes: ["path", "circle", "ellipse"],
      drawingStyle: "fluid",
      complexity: 5,
      svgType: Math.random() < 0.7 ? "custom" : "blob",
      includeDrawing: Math.random() < 0.7,
      includeShapes: true,
      shapeCount: 6 + Math.floor(Math.random() * 3), // 6-9
    };
  }

  // Enseignement et Formation
  if (sector.includes("enseigne") || sector.includes("formation") || sector.includes("ecole") ||
      sector.includes("professeur") || sector.includes("cours") || sector.includes("education") ||
      sector.includes("pedagogie") || sector.includes("apprentissage")) {
    return {
      colorScheme: "complementary",
      shapes: ["circle", "rect", "polygon"],
      drawingStyle: "geometric",
      complexity: 3,
      svgType: Math.random() < 0.5 ? "custom" : "geometric",
      includeDrawing: Math.random() < 0.4,
      includeShapes: true,
      shapeCount: 4 + Math.floor(Math.random() * 3), // 4-7
    };
  }

  // Transport et Logistique
  if (sector.includes("transport") || sector.includes("logistique") || sector.includes("livraison") ||
      sector.includes("demenagement") || sector.includes("chauffeur") || sector.includes("coursier")) {
    return {
      colorScheme: "complementary",
      shapes: ["line", "rect", "polygon"],
      drawingStyle: "straight",
      complexity: 3,
      svgType: Math.random() < 0.5 ? "custom" : "geometric",
      includeDrawing: Math.random() < 0.3,
      includeShapes: true,
      shapeCount: 3 + Math.floor(Math.random() * 3), // 3-6
    };
  }

  // Restauration et Alimentation
  if (sector.includes("restaur") || sector.includes("aliment") || sector.includes("cuisine") ||
      sector.includes("chef") || sector.includes("traiteur") || sector.includes("patisserie") ||
      sector.includes("boulangerie") || sector.includes("gastronomie")) {
    return {
      colorScheme: "warm",
      shapes: ["circle", "ellipse", "path"],
      drawingStyle: "fluid",
      complexity: 4,
      svgType: Math.random() < 0.6 ? "custom" : "blob",
      includeDrawing: Math.random() < 0.5,
      includeShapes: true,
      shapeCount: 4 + Math.floor(Math.random() * 3), // 4-7
    };
  }

  // Services aux animaux
  if (sector.includes("animal") || sector.includes("veterinaire") || sector.includes("chien") ||
      sector.includes("chat") || sector.includes("animalerie") || sector.includes("toilettage") ||
      sector.includes("dressage") || sector.includes("garde d'animaux") || sector.includes("promenade")) {
    return {
      colorScheme: "natural",
      shapes: ["circle", "ellipse", "path"],
      drawingStyle: "fluid",
      complexity: 4,
      svgType: Math.random() < 0.7 ? "custom" : "blob",
      includeDrawing: Math.random() < 0.6,
      includeShapes: true,
      shapeCount: 4 + Math.floor(Math.random() * 3), // 4-7
    };
  }

  // Automobile et Mécanique
  if (sector.includes("auto") || sector.includes("mecanique") || sector.includes("garage") ||
      sector.includes("carrosserie") || sector.includes("voiture") || sector.includes("reparation") ||
      sector.includes("vehicule") || sector.includes("moto")) {
    return {
      colorScheme: "industrial",
      shapes: ["rect", "polygon", "path"],
      drawingStyle: "geometric",
      complexity: 4,
      svgType: Math.random() < 0.6 ? "custom" : "geometric",
      includeDrawing: Math.random() < 0.4,
      includeShapes: true,
      shapeCount: 4 + Math.floor(Math.random() * 3), // 4-7
    };
  }

  return defaultConfig;
};

// FONCTIONS UTILITAIRES

/**
 * Convertit une couleur hexadécimale en RGB
 */
const hexToRgb = (hex: string) => {
  hex = hex.replace(/^#/, '');

  // Convertir shorthand (3 digits) en format standard (6 digits)
  if (hex.length === 3) {
    hex = hex.split('').map(char => char + char).join('');
  }

  const bigint = parseInt(hex, 16);
  const r = (bigint >> 16) & 255;
  const g = (bigint >> 8) & 255;
  const b = bigint & 255;

  return { r, g, b };
};

/**
 * Convertit RGB en hexadécimal
 */
const rgbToHex = (r: number, g: number, b: number) => {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
};

/**
 * Éclaircit ou assombrit une couleur
 * @param color Couleur hexadécimale
 * @param percent Pourcentage d'éclaircissement (positif) ou d'assombrissement (négatif)
 */
const shadeColor = (color: string, percent: number) => {
  const { r, g, b } = hexToRgb(color);

  const t = percent < 0 ? 0 : 255;
  const p = percent < 0 ? percent * -1 : percent;

  const newR = Math.round((t - r) * p / 100) + r;
  const newG = Math.round((t - g) * p / 100) + g;
  const newB = Math.round((t - b) * p / 100) + b;

  return rgbToHex(newR, newG, newB);
};

/**
 * Obtient la couleur complémentaire (opposée sur la roue chromatique)
 */
const getComplementaryColor = (hex: string) => {
  const { r, g, b } = hexToRgb(hex);
  return rgbToHex(255 - r, 255 - g, 255 - b);
};

/**
 * Obtient une couleur analogue (adjacente sur la roue chromatique)
 * @param angle Angle de décalage sur la roue chromatique (en degrés)
 */
const getAnalogousColor = (hex: string, angle: number) => {
  const { r, g, b } = hexToRgb(hex);

  // Convertir RGB en HSL
  const max = Math.max(r, g, b) / 255;
  const min = Math.min(r, g, b) / 255;
  const l = (max + min) / 2;

  let h, s;

  if (max === min) {
    h = s = 0;
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r / 255:
        h = (g / 255 - b / 255) / d + (g / 255 < b / 255 ? 6 : 0);
        break;
      case g / 255:
        h = (b / 255 - r / 255) / d + 2;
        break;
      case b / 255:
        h = (r / 255 - g / 255) / d + 4;
        break;
      default:
        h = 0;
    }

    h /= 6;
  }

  // Ajouter l'angle pour obtenir la couleur analogue
  h = (h * 360 + angle) % 360;
  if (h < 0) h += 360;
  h /= 360;

  // Convertir HSL en RGB
  let r2, g2, b2;

  if (s === 0) {
    r2 = g2 = b2 = l;
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;

    r2 = hueToRgb(p, q, h + 1/3);
    g2 = hueToRgb(p, q, h);
    b2 = hueToRgb(p, q, h - 1/3);
  }

  return rgbToHex(Math.round(r2 * 255), Math.round(g2 * 255), Math.round(b2 * 255));
};

/**
 * Fonction utilitaire pour la conversion HSL en RGB
 */
const hueToRgb = (p: number, q: number, t: number) => {
  if (t < 0) t += 1;
  if (t > 1) t -= 1;
  if (t < 1/6) return p + (q - p) * 6 * t;
  if (t < 1/2) return q;
  if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
  return p;
};

/**
 * Génère une couleur aléatoire
 */
const getRandomColor = () => {
  return `#${Math.floor(Math.random()*16777215).toString(16).padStart(6, '0')}`;
};

/**
 * Génère un chemin SVG pour une forme ondulée
 */
const generateWavePath = (complexity: number) => {
  const numWaves = 2 + complexity;
  const pathParts = [`M0,50`];

  for (let i = 1; i <= numWaves; i++) {
    const x1 = (i - 1) * (100 / numWaves);
    const y1 = 50 + Math.sin((i - 1) / numWaves * Math.PI * 2) * (10 + complexity * 5);
    const x2 = i * (100 / numWaves);
    const y2 = 50 + Math.sin(i / numWaves * Math.PI * 2) * (10 + complexity * 5);
    const cx1 = x1 + (100 / numWaves) * 0.3;
    const cy1 = y1 + (Math.random() * 2 - 1) * (5 + complexity * 3);
    const cx2 = x2 - (100 / numWaves) * 0.3;
    const cy2 = y2 + (Math.random() * 2 - 1) * (5 + complexity * 3);

    pathParts.push(`C${cx1},${cy1} ${cx2},${cy2} ${x2},${y2}`);
  }

  return pathParts.join(' ');
};

/**
 * Génère un chemin SVG pour une forme organique "blob"
 */
const generateBlobPath = (complexity: number) => {
  const points = 5 + complexity * 2;
  const radius = 40 + complexity * 5;
  const irregularity = 0.2 + complexity * 0.1;
  const centerX = 50;
  const centerY = 50;

  // Commencer le tracé
  let path = `M${centerX + radius},${centerY} `;

  for (let i = 0; i < points; i++) {
    const angle = (i / points) * Math.PI * 2;
    const variation = 1 - irregularity + Math.random() * irregularity * 2;
    const currentRadius = radius * variation;

    const x = centerX + Math.cos(angle) * currentRadius;
    const y = centerY + Math.sin(angle) * currentRadius;

    // Contrôle la courbure entre les points
    const cpAngle1 = angle - Math.PI / points;
    const cpAngle2 = angle + Math.PI / points;
    const cpRadius = currentRadius * 0.5;

    const cp1x = centerX + Math.cos(cpAngle1) * cpRadius * (1 + Math.random() * 0.2);
    const cp1y = centerY + Math.sin(cpAngle1) * cpRadius * (1 + Math.random() * 0.2);
    const cp2x = centerX + Math.cos(cpAngle2) * cpRadius * (1 + Math.random() * 0.2);
    const cp2y = centerY + Math.sin(cpAngle2) * cpRadius * (1 + Math.random() * 0.2);

    path += `C${cp1x},${cp1y} ${cp2x},${cp2y} ${x},${y} `;
  }

  path += 'Z';
  return path;
};

/**
 * Génère un chemin SVG pour une forme géométrique
 */
const generateGeometricPath = (complexity: number) => {
  const corners = 3 + complexity;
  const radius = 40;
  const centerX = 50;
  const centerY = 50;

  let path = `M${centerX + radius},${centerY} `;

  for (let i = 1; i <= corners; i++) {
    const angle = (i / corners) * Math.PI * 2;
    const x = centerX + Math.cos(angle) * radius;
    const y = centerY + Math.sin(angle) * radius;

    path += `L${x},${y} `;
  }

  path += 'Z';
  return path;
};

/**
 * Génère un chemin SVG pour une spirale
 */
const generateSpiralPath = (complexity: number) => {
  const turns = 1 + complexity * 0.5;
  const points = 20 + complexity * 10;
  const radiusStart = 5;
  const radiusEnd = 40;
  const centerX = 50;
  const centerY = 50;

  let path = `M${centerX + radiusStart},${centerY} `;

  for (let i = 1; i <= points; i++) {
    const angle = (i / points) * Math.PI * 2 * turns;
    const radius = radiusStart + (radiusEnd - radiusStart) * (i / points);

    const x = centerX + Math.cos(angle) * radius;
    const y = centerY + Math.sin(angle) * radius;

    if (i === 1) {
      path += `L${x},${y} `;
    } else {
      const prevI = i - 1;
      const prevAngle = (prevI / points) * Math.PI * 2 * turns;
      const prevRadius = radiusStart + (radiusEnd - radiusStart) * (prevI / points);

      const prevX = centerX + Math.cos(prevAngle) * prevRadius;
      const prevY = centerY + Math.sin(prevAngle) * prevRadius;

      // Point de contrôle pour courbe douce
      const cpAngle = (angle + prevAngle) / 2;
      const cpRadius = (radius + prevRadius) / 2 * 1.2;
      const cpX = centerX + Math.cos(cpAngle) * cpRadius;
      const cpY = centerY + Math.sin(cpAngle) * cpRadius;

      path += `Q${cpX},${cpY} ${x},${y} `;
    }
  }

  return path;
};

/**
 * Bibliothèque de formes SVG prédéfinies
 */
export const getSvgFromLibrary = () => {
  const svgLibrary = [
    // Feuille stylisée
    "M10,50 C30,20 40,10 50,10 C60,10 90,20 90,50 C90,80 60,90 50,90 C40,90 10,80 10,50 Z",

    // Vague élégante
    "M0,50 C20,40 20,70 40,60 C60,50 60,80 80,70 C100,60 100,90 120,80 C140,70 140,100 160,90 C180,80 180,110 200,100",

    // Spirale simple
    "M50,50 L55,45 C65,35 75,45 65,55 C55,65 35,55 45,35 C55,15 85,25 75,55 C65,85 25,75 35,45 C45,15 85,5 95,45 C105,85 65,105 25,85 C-15,65 5,15 45,5 C85,-5 115,35 105,75",

    // Forme organique
    "M50,10 C70,10 90,30 90,50 C90,70 70,90 50,90 C30,90 10,70 10,50 C10,30 30,10 50,10 Z",

    // Étoile stylisée
    "M50,0 L60,35 L95,35 L65,60 L75,95 L50,75 L25,95 L35,60 L5,35 L40,35 Z",

    // Goutte d'eau
    "M50,0 C80,30 80,60 50,90 C20,60 20,30 50,0 Z",

    // Cœur
    "M50,20 C55,10 70,10 75,20 C80,30 80,40 50,70 C20,40 20,30 25,20 C30,10 45,10 50,20 Z",

    // Nuage
    "M25,60 C10,60 10,40 25,40 C25,20 45,20 50,40 C55,20 75,20 75,40 C90,40 90,60 75,60 Z",

    // Courbes abstraites
    "M10,50 C30,30 70,80 90,60 C110,40 150,90 170,70 C190,50 230,100 250,80",

    // Hexagone arrondi
    "M50,10 C55,10 60,12 65,17 C70,22 72,27 72,32 C72,37 70,42 65,47 C60,52 55,54 50,54 C45,54 40,52 35,47 C30,42 28,37 28,32 C28,27 30,22 35,17 C40,12 45,10 50,10 Z",

    // NOUVELLES FORMES PROFESSIONNELLES MÉTIERS

    // Ciseaux (coiffeur, styliste)
    "M20,80 L40,60 L20,40 C15,35 15,25 20,20 C25,15 35,15 40,20 L60,40 L80,20 C85,15 95,15 100,20 C105,25 105,35 100,40 L80,60 L100,80 C105,85 105,95 100,100 C95,105 85,105 80,100 L60,80 L40,100 C35,105 25,105 20,100 C15,95 15,85 20,80 Z",

    // Clé (plombier, serrurier)
    "M30,70 L30,60 L20,60 L20,50 L30,50 L30,40 L20,40 L20,30 L40,30 L40,70 L30,70 M45,50 C45,30 60,15 80,15 C100,15 115,30 115,50 C115,70 100,85 80,85 C60,85 45,70 45,50 M80,50 C85,50 90,45 90,40 C90,35 85,30 80,30 C75,30 70,35 70,40 C70,45 75,50 80,50",

    // Marteau (bricoleur, construction)
    "M20,80 L40,60 L35,55 L45,45 L85,55 C90,56 95,51 94,46 L84,16 C83,11 88,6 93,7 L98,8 C103,9 108,14 107,19 L97,49 C96,54 101,59 106,58 L110,57 L100,80 L65,70 L55,80 L20,80 Z",

    // Ampoule (électricien, idée)
    "M50,10 C75,10 90,30 90,50 C90,65 80,75 70,85 L70,95 L30,95 L30,85 C20,75 10,65 10,50 C10,30 25,10 50,10 Z M30,105 L70,105 M35,115 L65,115 M40,125 L60,125",

    // Arbre (paysagiste, jardinier)
    "M50,100 L50,60 M30,100 L70,100 M50,10 C70,10 90,30 90,50 C90,60 80,70 65,70 C75,75 80,85 75,95 C70,105 55,105 50,95 C45,105 30,105 25,95 C20,85 25,75 35,70 C20,70 10,60 10,50 C10,30 30,10 50,10 Z",

    // Feuille détaillée (paysagiste, fleuriste)
    "M10,50 C10,20 40,10 50,10 C60,10 90,20 90,50 C90,80 60,90 50,90 C40,90 10,80 10,50 Z M50,10 C50,90 50,90 50,90 M30,30 C50,30 50,30 70,30 M25,50 C50,50 50,50 75,50 M30,70 C50,70 50,70 70,70",

    // Pinceau (peintre, artiste)
    "M20,80 C20,65 25,65 30,60 L60,30 C65,25 75,25 80,30 L90,40 C95,45 95,55 90,60 L60,90 C55,95 45,95 40,90 L30,80 C25,75 20,75 20,80 Z M60,30 L90,60",

    // Maison (agent immobilier, architecte)
    "M10,60 L50,20 L90,60 M20,50 L20,90 L80,90 L80,50 M45,90 L45,70 L55,70 L55,90",

    // Visage (maquilleur, esthéticien)
    "M50,10 C75,10 90,35 90,50 C90,65 75,90 50,90 C25,90 10,65 10,50 C10,35 25,10 50,10 Z M30,40 C35,35 40,35 45,40 M55,40 C60,35 65,35 70,40 M30,65 C40,75 60,75 70,65",

    // Engrenage (mécanicien, ingénieur)
    "M50,15 L55,15 L57,28 L65,22 L70,27 L62,35 L72,43 L67,48 L57,40 L50,50 L57,60 L67,52 L72,57 L62,65 L70,73 L65,78 L57,72 L55,85 L50,85 L48,72 L40,78 L35,73 L43,65 L33,57 L38,52 L48,60 L55,50 L48,40 L38,48 L33,43 L43,35 L35,27 L40,22 L48,28 L50,15 Z M50,35 C58,35 65,42 65,50 C65,58 58,65 50,65 C42,65 35,58 35,50 C35,42 42,35 50,35 Z",

    // Écran ordinateur (informaticien, designer)
    "M10,20 L90,20 L90,70 L10,70 Z M40,80 L60,80 L55,70 L45,70 Z M30,90 L70,90 L70,85 L30,85 Z",

    // Note de musique (musicien, DJ)
    "M60,10 L60,60 C60,70 40,75 40,60 C40,45 60,50 60,60 M75,20 L75,70 C75,80 55,85 55,70 C55,55 75,60 75,70",

    // Toque (chef cuisinier)
    "M30,60 L30,50 C30,30 70,30 70,50 L70,60 L30,60 Z M20,60 L80,60 L80,70 L20,70 Z M50,10 C65,10 75,20 80,30 C65,25 35,25 20,30 C25,20 35,10 50,10 Z",

    // Clef de sol (musicien)
    "M50,90 C35,90 30,80 30,75 C30,65 40,65 40,55 C40,45 35,45 35,40 C35,35 40,35 45,35 C55,35 55,40 50,45 C42,53 42,58 50,60 C55,62 60,55 60,50 C60,30 45,25 35,25 C30,25 20,30 20,40 C20,55 30,60 30,70 C30,85 40,100 50,100 C60,100 70,90 50,90 Z",

    // Caméra (photographe, vidéaste)
    "M10,30 L30,30 L35,20 L65,20 L70,30 L90,30 L90,80 L10,80 Z M50,35 C65,35 75,45 75,55 C75,65 65,75 50,75 C35,75 25,65 25,55 C25,45 35,35 50,35 Z M80,40 C85,40 85,45 80,45 C75,45 75,40 80,40 Z",

    // Verre à cocktail (barman)
    "M25,20 L75,20 L55,70 L55,90 L45,90 L45,70 L25,20 Z M35,30 L65,30",

    // Stéthoscope (médecin, infirmier)
    "M40,15 C45,15 45,25 40,25 C35,25 35,15 40,15 Z M60,15 C65,15 65,25 60,25 C55,25 55,15 60,15 Z M40,25 L40,40 L60,40 L60,25 M50,40 L50,70 C35,70 25,80 25,90 C25,95 30,100 35,100 C40,100 45,95 45,90 L55,90 C55,95 60,100 65,100 C70,100 75,95 75,90 C75,80 65,70 50,70",

    // Dent (dentiste)
    "M30,10 L70,10 L80,30 L80,60 C80,70 70,80 65,65 C60,50 55,65 50,80 C45,65 40,50 35,65 C30,80 20,70 20,60 L20,30 L30,10 Z",

    // Balance (avocat, justice)
    "M50,10 L50,30 M20,30 L80,30 M20,30 C20,45 35,45 35,30 M80,30 C80,45 65,45 65,30 M35,30 L35,80 C35,85 45,90 50,90 C55,90 65,85 65,80 L65,30",

    // Livre ouvert (écrivain, enseignant)
    "M10,30 L10,80 L50,90 L90,80 L90,30 L50,40 L10,30 Z M50,40 L50,90 M10,30 C40,40 50,40 90,30"
  ];

  return svgLibrary[Math.floor(Math.random() * svgLibrary.length)];
};