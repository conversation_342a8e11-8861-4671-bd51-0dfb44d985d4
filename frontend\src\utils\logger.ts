/**
 * Système de logging intelligent pour l'application By Domoveillance
 * 
 * IMPORTANT: En production, AUCUN log n'est affiché pour des raisons de sécurité
 * Tous les logs (même les erreurs) sont désactivés pour éviter toute fuite d'information
 * 
 * Ce fichier fournit un système de logging qui :
 * - Affiche les logs UNIQUEMENT en développement
 * - Propose 4 niveaux de logs (en dev uniquement) :
 *   - debug(): Pour le développement (console.log)
 *   - info(): Pour les informations importantes (console.info)
 *   - warn(): Pour les avertissements (console.warn)
 *   - error(): Pour les erreurs (logger.error)
 * 
 * Utilisation:
 * ```typescript
 * import { logger } from '../utils/logger';
 * 
 * // Ces logs ne seront visibles qu'en développement :
 * logger.info('Message de debug');
 * logger.info('Information');
 * logger.warn('Attention');
 * logger.error('Erreur');
 * ```
 */


const isDevelopment = () => {
    // Utiliser import.meta.env.DEV au lieu de MODE
    return process.env.NODE_ENV === 'development';
};



type LogLevel = 'debug' | 'info' | 'warn' | 'error';

class Logger {
    private static instance: Logger;
    
    private constructor() {}
    
    static getInstance(): Logger {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }

    private log(level: LogLevel, ...args: any[]) {
        // Ne logger que si on est en développement
        if (isDevelopment()) {
            switch (level) {
                case 'debug':
                    console.log(...args);
                    break;
                case 'info':
                    console.info(...args);
                    break;
                case 'warn':
                    console.warn(...args);
                    break;
                case 'error':
                    console.error(...args);
                    break;
            }
        }
    }

    debug(...args: any[]) {
        this.log('debug', ...args);
    }

    info(...args: any[]) {
        this.log('info', ...args);
    }

    warn(...args: any[]) {
        this.log('warn', ...args);
    }

    error(...args: any[]) {
        // Filtrer les erreurs 401 pour les utilisateurs non connectés
        const errorMessage = args.join(' ');
        if (errorMessage.includes('401') || errorMessage.includes('Unauthorized') || errorMessage.includes('Accès non autorisé')) {
            // Réduire le niveau de log pour les erreurs 401
            this.log('info', '[Auth] Accès non autorisé (utilisateur non connecté)');
            return;
        }
        // Les erreurs aussi ne sont visibles qu'en développement
        this.log('error', ...args);
    }

    group(...args: any[]) {
        if (isDevelopment()) {
            console.group(...args);
        }
    }

    groupEnd() {
        if (isDevelopment()) {
            console.groupEnd();
        }
    }
}

export const logger = Logger.getInstance();

// Pour la compatibilité avec le code existant
export const devLog = (...args: any[]) => {
    logger.info(...args);
};

export default logger;
