import axios from 'axios';
import { API_CONFIG } from '../config/api';
import { getCommonHeaders } from './headers';
import { notify } from '../components/Notification';
import logger from './logger';
import { fetchCsrfToken } from '../services/csrf';

export interface JobiTransaction {
  montant: number;
  operation: 'plus' | 'moins';
  titre: string;
  description: string;
}

export const updateJobiBalance = async (transaction: JobiTransaction): Promise<boolean> => {
  try {
    await fetchCsrfToken();
    const headers = await getCommonHeaders();
    headers['X-CSRF-Token'] = await fetchCsrfToken();
    await axios.post(
      `${API_CONFIG.baseURL}/api/jobi/solde`,
      transaction,
      { headers, withCredentials: true }
    );
    return true;
  } catch (error: any) {
    logger.error('Erreur lors de la mise à jour du solde Jobi:', error);
    const errorMessage = error.response?.data?.error || 'Impossible de mettre à jour votre solde Jobi';
    notify(errorMessage, 'error');
    return false;
  }
}; 