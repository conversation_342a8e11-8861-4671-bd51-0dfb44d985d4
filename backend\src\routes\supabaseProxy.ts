import { Router } from 'express';
import { supabase } from '../config/supabase';
import rateLimit from 'express-rate-limit';
import config from '../config';

const router = Router();

// Limiteur de requêtes : 100 requêtes par 15 minutes par IP
const supabaseProxyLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limite chaque IP à 100 requêtes par fenêtre
  standardHeaders: true, // Retourne les headers de rate limit standard
  legacyHeaders: false, // Désactive les headers obsolètes
});

// Proxy pour les fichiers du storage Supabase
// Exemple d'URL : /api/storage-proxy/:bucket/*
router.get(/^\/([^/]+)\/(.*)/, supabaseProxyLimiter, async (req, res): Promise<any> => {
  try {
    const bucket = req.params[0];
    const filePath = req.params[1];

    // Debug logs pour diagnostiquer le problème (uniquement en développement)
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Proxy Debug:', {
        originalUrl: req.originalUrl,
        url: req.url,
        bucket,
        filePath,
        params: req.params
      });
    }

    // SÉCURITÉ: Validation stricte des buckets autorisés
    const allowedBuckets = [
      'photo_profil',
      'galerie_realisation_client',
      'mission_photos',
      'carte_visite_et_flyer',
      'support_ticket_attachments',
      'message_attachments',
      'entreprise_verification',
      'temp_moderation',
      'avis_photos_client'
    ];

    if (!allowedBuckets.includes(bucket)) {
      if (process.env.NODE_ENV === 'development') {
        console.log('❌ Bucket non autorisé:', { bucket, allowedBuckets });
      }
      return res.status(403).json({
        error: 'Accès non autorisé à ce bucket'
      });
    }

    // SÉCURITÉ: Validation du chemin de fichier pour éviter path traversal
    if (filePath.includes('..') || filePath.includes('//') || filePath.startsWith('/')) {
      return res.status(400).json({
        error: 'Chemin de fichier invalide'
      });
    }

    // SÉCURITÉ: Limiter les extensions de fichiers autorisées
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.pdf', '.avif', '.heic', '.doc', '.docx', '.xls', '.xlsx', '.txt'];
    const fileExtension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));

    if (!allowedExtensions.includes(fileExtension)) {
      return res.status(400).json({
        error: 'Type de fichier non autorisé'
      });
    }

    const { data, error } = await supabase.storage.from(bucket).download(filePath);

    if (error || !data) {
      // Redirige vers la page 404 du frontend en utilisant l'URL configurée
      return res.redirect(`${config.frontendUrl}/404`);
    }

    // Conversion du ReadableStream en Buffer
    const arrayBuffer = await data.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // SÉCURITÉ: Headers de sécurité pour les fichiers
    res.setHeader('Content-Type', data.type || 'application/octet-stream');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('Cache-Control', 'public, max-age=3600');
    res.send(buffer);
  } catch (error) {
    console.error('Erreur dans supabaseProxy:', error);
    return res.status(500).json({
      error: 'Erreur serveur'
    });
  }
});

export default router;