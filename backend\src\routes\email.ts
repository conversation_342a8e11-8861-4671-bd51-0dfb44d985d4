import { Router } from 'express';
import { sendVerificationEmail, sendPasswordResetEmail, sendWelcomeEmail, sendSuspensionEmail, sendProfileShareEmail } from '../services/emailService';
import logger from '../utils/logger';
import { supabase } from '../config/supabase';
import { authMiddleware } from '../middleware/authMiddleware';
import { decryptProfilDataAsync, decryptUserDataAsync } from '../utils/encryption';

const router = Router();

// Route pour l'envoi de l'email de vérification
router.post('/send-verification', async (req, res) => {
  try {
    const email = req.body.email || req.body.userEmail;
    let token = req.body.verificationToken || req.body.token || '';

    if (!email) {
      res.status(400).json({
        success: false,
        message: 'Email de vérification manquant'
      });
    }

    if (!token) {
      logger.warn('Token vide, génération d\'un token temporaire');
      token = `verify-email?token=${Date.now()}`;
    }

    const result = await sendVerificationEmail(email, token);
    res.json({ success: true });
  } catch (error) {
    logger.error('Erreur détaillée dans send-verification route:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'envoi de l\'email de vérification',
      details: error instanceof Error ? {
        message: error.message,
        stack: error.stack
      } : 'Erreur inconnue'
    });
  }
});

// Route pour l'envoi de l'email de bienvenue
router.post('/send-welcome', async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      res.status(400).json({
        success: false,
        message: 'Email manquant pour l\'email de bienvenue'
      });
    }

    const result = await sendWelcomeEmail(email);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'envoi de l\'email de bienvenue'
    });
  }
});

// Route pour l'envoi de l'email de réinitialisation
router.post('/send-reset', async (req, res) => {
  try {
    const { email, token } = req.body;
    await sendPasswordResetEmail(email, token);
    res.json({ success: true });
  } catch (error) {
    logger.error('Error in send-reset route:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'envoi de l\'email de réinitialisation'
    });
  }
});

// Route pour l'envoi de l'email de suspension
router.post('/send-suspension', async (req, res) => {
  try {
    const { email, reason } = req.body;

    if (!email) {
      res.status(400).json({
        success: false,
        message: 'Email manquant pour l\'email de suspension'
      });
    }

    await sendSuspensionEmail(email, reason || 'Contactez le support pour plus d\'informations.');
    res.json({ success: true });
  } catch (error) {
    logger.error('Erreur lors de l\'envoi de l\'email de suspension:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'envoi de l\'email de suspension',
      details: error instanceof Error ? {
        message: error.message,
        stack: error.stack
      } : 'Erreur inconnue'
    });
  }
});

// Route pour l'envoi d'email de partage de profil (protégée par authentification)
router.post('/share-profile', authMiddleware.authenticateToken, async (req: any, res: any) => {
  try {
    const { recipientEmail, profileData, personalMessage } = req.body;

    if (!recipientEmail) {
      return res.status(400).json({
        success: false,
        message: 'Email du destinataire manquant'
      });
    }

    if (!profileData || !profileData.firstName || !profileData.lastName || !profileData.slug) {
      return res.status(400).json({
        success: false,
        message: 'Données du profil incomplètes'
      });
    }

    // Récupérer les informations complètes de l'utilisateur depuis la base de données
    if (!req.user?.userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    // Récupérer les données du profil depuis Supabase
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        profil:user_profil(
          prenom,
          nom,
          slug
        )
      `)
      .eq('id', req.user.userId)
      .single();

    if (userError || !userData) {
      logger.error('Erreur lors de la récupération du profil utilisateur:', userError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des informations utilisateur'
      });
    }

    // Déchiffrer les données utilisateur
    const decryptedUserData = await decryptUserDataAsync(userData);

    // Extraire les informations du profil (prendre le premier élément du tableau)
    const profilData = Array.isArray(decryptedUserData.profil) ? decryptedUserData.profil[0] : decryptedUserData.profil;
    
    // Déchiffrer les données du profil si elles existent
    const decryptedProfilData = profilData ? await decryptProfilDataAsync(profilData) : null;
    
    const senderFirstName = decryptedProfilData?.prenom || '';
    const senderLastName = decryptedProfilData?.nom || '';
    const senderSlug = decryptedProfilData?.slug || '';

    // Format du nom complet : "Jean Dupont"
    const senderDisplayName = senderFirstName && senderLastName
      ? `${senderFirstName} ${senderLastName}`
      : 'Un utilisateur JobPartiel';

    // URL du profil de l'expéditeur
    const senderProfileUrl = senderSlug
      ? `${process.env.FRONTEND_URL}/profil/${senderSlug}`
      : '';

    await sendProfileShareEmail(recipientEmail, {
      profileData,
      personalMessage: personalMessage || '',
      senderName: senderDisplayName,
      senderProfileUrl
    });

    res.json({
      success: true,
      message: 'Email de partage envoyé avec succès'
    });
  } catch (error) {
    logger.error('Erreur lors de l\'envoi de l\'email de partage de profil:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'envoi de l\'email de partage',
      details: error instanceof Error ? {
        message: error.message,
        stack: error.stack
      } : 'Erreur inconnue'
    });
  }
});

export default router;
