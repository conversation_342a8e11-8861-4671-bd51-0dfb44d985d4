import crypto from 'crypto';
import { promisify } from 'util';
import logger from './logger';
import { redis } from '../config/redis';

// Configuration du chiffrement
const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16; // Pour AES, c'est toujours 16 bytes
const SALT_LENGTH = 64; // 64 bytes pour le salt
const TAG_LENGTH = 16; // 16 bytes pour le tag d'authentification
const KEY_LENGTH = 32; // 32 bytes pour AES-256

// Cache pour les clés dérivées
const KEY_CACHE_PREFIX = 'encryption_key:';
const KEY_CACHE_TTL = 3600; // 1 heure

// Promisifier les fonctions crypto asynchrones
const scryptAsync = promisify(crypto.scrypt);

// Cache en mémoire pour la clé maître (éviter Redis pour chaque appel)
let masterKeyCache: Buffer | null = null;

// Récupérer la clé de chiffrement depuis les variables d'environnement (version asynchrone)
const getEncryptionKeyAsync = async (): Promise<Buffer> => {
  // Vérifier le cache en mémoire d'abord
  if (masterKeyCache) {
    return masterKeyCache;
  }

  const encryptionKey = process.env.ENCRYPTION_KEY;
  
  if (!encryptionKey) {
    throw new Error('ENCRYPTION_KEY non définie dans les variables d\'environnement');
  }
  
  let derivedKey: Buffer;
  
  // Si la clé est en base64, la décoder
  if (encryptionKey.length === 44 && encryptionKey.endsWith('=')) {
    derivedKey = Buffer.from(encryptionKey, 'base64');
  } else {
    // Sinon, utiliser la clé directement et la hasher pour obtenir 32 bytes (asynchrone)
    const scryptResult = await scryptAsync(encryptionKey, 'salt', KEY_LENGTH) as Buffer;
    derivedKey = scryptResult;
  }
  
  // Mettre en cache en mémoire
  masterKeyCache = derivedKey;
  
  return derivedKey;
};

// Cache pour les clés dérivées avec Redis (version asynchrone uniquement)
const getDerivedKeyAsync = async (salt: Buffer): Promise<Buffer> => {
  const saltHex = salt.toString('hex');
  const cacheKey = `${KEY_CACHE_PREFIX}${saltHex}`;
  
  try {
    // Vérifier le cache Redis
    const cachedKey = await redis.get(cacheKey);
    if (cachedKey) {
      return Buffer.from(cachedKey, 'hex');
    }
    
    // Si pas en cache, dériver la clé de manière asynchrone
    const masterKey = await getEncryptionKeyAsync();
    const derivedKey = await scryptAsync(masterKey, salt, KEY_LENGTH) as Buffer;
    
    // Mettre en cache pour 1 heure
    await redis.setex(cacheKey, KEY_CACHE_TTL, derivedKey.toString('hex'));
    
    return derivedKey;
  } catch (error) {
    logger.error('Erreur critique lors de la dérivation de clé:', error);
    throw new Error('Impossible de dériver la clé de chiffrement');
  }
};

/**
 * Initialise le système de cryptage (pré-charge la clé maître)
 * À appeler au démarrage de l'application pour optimiser les performances
 */
export const initializeEncryption = async (): Promise<void> => {
  try {
    await getEncryptionKeyAsync();
    logger.info('Système de cryptage initialisé avec succès');
  } catch (error) {
    logger.error('Erreur lors de l\'initialisation du système de cryptage:', error);
    throw error;
  }
};

/**
 * Chiffre une chaîne de caractères (version asynchrone optimisée)
 * @param text Texte à chiffrer
 * @returns Texte chiffré au format base64 avec IV, salt et tag
 */
export const encryptDataAsync = async (text: string): Promise<string> => {
  try {
    if (!text || text.trim() === '') {
      return text; // Retourner tel quel si vide
    }

    const iv = crypto.randomBytes(IV_LENGTH);
    const salt = crypto.randomBytes(SALT_LENGTH);
    
    // Dériver une clé unique avec le salt (asynchrone avec cache)
    const derivedKey = await getDerivedKeyAsync(salt);
    
    const cipher = crypto.createCipheriv(ALGORITHM, derivedKey, iv);
    cipher.setAAD(salt); // Utiliser le salt comme données additionnelles authentifiées
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const tag = cipher.getAuthTag();
    
    // Combiner IV + salt + tag + données chiffrées
    const combined = Buffer.concat([iv, salt, tag, Buffer.from(encrypted, 'hex')]);
    
    return combined.toString('base64');
  } catch (error) {
    logger.error('Erreur lors du chiffrement des données:', error);
    throw new Error('Erreur lors du chiffrement des données');
  }
};

/**
 * Vérifie si une chaîne est chiffrée (format base64 avec longueur minimale)
 * @param data Données à vérifier
 * @returns true si les données semblent chiffrées
 */
export const isEncrypted = (data: string): boolean => {
  if (!data || typeof data !== 'string') {
    return false;
  }

  // Vérifier si c'est du base64 valide
  const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
  if (!base64Regex.test(data)) {
    return false;
  }

  try {
    const buffer = Buffer.from(data, 'base64');
    // Vérifier la longueur minimale (IV + SALT + TAG + au moins 1 byte de données)
    const minLength = IV_LENGTH + SALT_LENGTH + TAG_LENGTH + 1;
    return buffer.length >= minLength;
  } catch {
    return false;
  }
};

/**
 * Déchiffre une chaîne de caractères (version asynchrone optimisée)
 * @param encryptedData Données chiffrées au format base64
 * @returns Texte déchiffré
 */
export const decryptDataAsync = async (encryptedData: string): Promise<string> => {
  try {
    if (!encryptedData || encryptedData.trim() === '') {
      return encryptedData; // Retourner tel quel si vide
    }

    // Vérifier si les données sont chiffrées
    if (!isEncrypted(encryptedData)) {
      // Les données ne sont pas chiffrées, les retourner telles quelles
      return encryptedData;
    }

    const combined = Buffer.from(encryptedData, 'base64');

    // Vérifier que le buffer a la taille minimale requise
    const minSize = IV_LENGTH + SALT_LENGTH + TAG_LENGTH + 1;
    if (combined.length < minSize) {
      logger.warn('Données trop courtes pour être chiffrées, retour tel quel:', { length: combined.length, minSize });
      return encryptedData;
    }

    // Extraire les composants
    const iv = combined.subarray(0, IV_LENGTH);
    const salt = combined.subarray(IV_LENGTH, IV_LENGTH + SALT_LENGTH);
    const tag = combined.subarray(IV_LENGTH + SALT_LENGTH, IV_LENGTH + SALT_LENGTH + TAG_LENGTH);
    const encrypted = combined.subarray(IV_LENGTH + SALT_LENGTH + TAG_LENGTH);

    // Vérifier que le tag a la bonne longueur
    if (tag.length !== TAG_LENGTH) {
      logger.warn('Tag d\'authentification invalide, retour tel quel:', { tagLength: tag.length, expectedLength: TAG_LENGTH });
      return encryptedData;
    }

    // Dériver la même clé avec le salt (asynchrone avec cache)
    const derivedKey = await getDerivedKeyAsync(salt);

    const decipher = crypto.createDecipheriv(ALGORITHM, derivedKey, iv);
    decipher.setAuthTag(tag);
    decipher.setAAD(salt);

    let decrypted = decipher.update(encrypted, undefined, 'utf8');
    decrypted += decipher.final('utf8');

    // Vérifier si le résultat déchiffré est encore crypté (double cryptage)
    if (isEncrypted(decrypted)) {
      logger.info('Double cryptage détecté, tentative de déchiffrement récursif');
      return await decryptDataAsync(decrypted); // Déchiffrement récursif asynchrone
    }

    return decrypted;
  } catch (error) {
    logger.warn('Erreur lors du déchiffrement, données probablement non chiffrées:', {
      error: error instanceof Error ? error.message : String(error),
      dataLength: encryptedData?.length || 0,
      dataPreview: encryptedData?.substring(0, 50) || 'empty'
    });
    // En cas d'erreur, retourner la donnée telle quelle (probablement non chiffrée)
    return encryptedData;
  }
};

/**
 * Fonction générique pour chiffrer des données en parallèle (version asynchrone)
 * @param data Données à chiffrer
 * @param fieldsToEncrypt Liste des champs à chiffrer
 * @param entityType Type d'entité pour les logs
 * @returns Données chiffrées
 */
const encryptDataFieldsAsync = async (data: any, fieldsToEncrypt: string[], entityType: string): Promise<any> => {
  if (!data) return data;

  const encrypted = { ...data };

  // Traitement en parallèle pour optimiser les performances
  const encryptionPromises = fieldsToEncrypt.map(async (field) => {
    if (encrypted[field] && typeof encrypted[field] === 'string' && encrypted[field].trim() !== '') {
      try {
        const value = await encryptDataAsync(encrypted[field]);
        return { field, value };
      } catch (error) {
        logger.warn(`Erreur lors du chiffrement du champ ${entityType} ${field}:`, {
          error: error instanceof Error ? error.message : String(error),
          fieldValue: encrypted[field]?.substring(0, 50) || 'empty'
        });
        // Garder la valeur originale en cas d'erreur
        return { field, value: encrypted[field] };
      }
    }
    return null;
  }).filter(Boolean);

  // Attendre tous les chiffrements en parallèle
  const results = await Promise.all(encryptionPromises);

  // Appliquer les résultats
  results.forEach(result => {
    if (result) {
      encrypted[result.field] = result.value;
    }
  });

  return encrypted;
};

/**
 * Fonction générique pour déchiffrer des données en parallèle (version asynchrone)
 * @param data Données à déchiffrer
 * @param fieldsToDecrypt Liste des champs à déchiffrer
 * @param entityType Type d'entité pour les logs
 * @returns Données déchiffrées
 */
const decryptDataFieldsAsync = async (data: any, fieldsToDecrypt: string[], entityType: string): Promise<any> => {
  if (!data) return data;

  const decrypted = { ...data };

  // Traitement en parallèle pour optimiser les performances
  const decryptionPromises = fieldsToDecrypt.map(async (field) => {
    if (decrypted[field] && typeof decrypted[field] === 'string') {
      try {
        const value = await decryptDataAsync(decrypted[field]);
        return { field, value };
      } catch (error) {
        logger.warn(`Erreur lors du déchiffrement du champ ${entityType} ${field}:`, {
          error: error instanceof Error ? error.message : String(error),
          fieldValue: decrypted[field]?.substring(0, 50) || 'empty'
        });
        // Garder la valeur originale en cas d'erreur (probablement non chiffrée)
        return { field, value: decrypted[field] };
      }
    }
    return null;
  }).filter(Boolean);

  // Attendre tous les déchiffrements en parallèle
  const results = await Promise.all(decryptionPromises);

  // Appliquer les résultats
  results.forEach(result => {
    if (result) {
      decrypted[result.field] = result.value;
    }
  });

  return decrypted;
};

// ============================================================================
// FONCTIONS SPÉCIALISÉES POUR CHAQUE TYPE DE DONNÉES
// ============================================================================

/**
 * Chiffre les données sensibles d'un profil utilisateur
 * @param profileData Données du profil à chiffrer
 * @returns Données du profil avec les champs sensibles chiffrés
 */
export const encryptProfilDataAsync = async (profileData: any): Promise<any> => {
  if (!profileData) return profileData;

  const encrypted = { ...profileData };

  // Champs à chiffrer dans user_profil
  const fieldsToEncrypt = ['nom', 'prenom', 'telephone', 'adresse', 'ville', 'code_postal'];

  // Traitement en parallèle pour optimiser les performances
  const encryptionPromises = fieldsToEncrypt.map(async (field) => {
    if (encrypted[field] && typeof encrypted[field] === 'string') {
      return { field, value: await encryptDataAsync(encrypted[field]) };
    }
    return null;
  }).filter(Boolean);

  // Chiffrer intervention_zone si c'est un objet
  if (encrypted.intervention_zone && typeof encrypted.intervention_zone === 'object') {
    encryptionPromises.push(
      encryptDataAsync(JSON.stringify(encrypted.intervention_zone)).then(value => ({ field: 'intervention_zone', value }))
    );
  }

  // Attendre tous les chiffrements en parallèle
  const results = await Promise.all(encryptionPromises);

  // Appliquer les résultats
  results.forEach(result => {
    if (result) {
      encrypted[result.field] = result.value;
    }
  });

  return encrypted;
};

/**
 * Déchiffre les données sensibles d'un profil utilisateur
 * @param profileData Données du profil chiffrées
 * @returns Données du profil avec les champs sensibles déchiffrés
 */
export const decryptProfilDataAsync = async (profileData: any): Promise<any> => {
  if (!profileData) return profileData;

  const decrypted = { ...profileData };

  // Champs à déchiffrer dans user_profil
  const fieldsToDecrypt = ['nom', 'prenom', 'telephone', 'adresse', 'ville', 'code_postal'];

  // Traitement en parallèle pour optimiser les performances
  const decryptionPromises = fieldsToDecrypt.map(async (field) => {
    if (decrypted[field] && typeof decrypted[field] === 'string') {
      try {
        const value = await decryptDataAsync(decrypted[field]);
        return { field, value };
      } catch (error) {
        logger.warn(`Erreur lors du déchiffrement du champ ${field}:`, {
          error: error instanceof Error ? error.message : String(error),
          fieldValue: decrypted[field]?.substring(0, 50) || 'empty'
        });
        // Garder la valeur originale en cas d'erreur (probablement non chiffrée)
        return { field, value: decrypted[field] };
      }
    }
    return null;
  }).filter(Boolean);

  // Déchiffrer intervention_zone si c'est une chaîne
  if (decrypted.intervention_zone && typeof decrypted.intervention_zone === 'string') {
    decryptionPromises.push(
      (async () => {
        try {
          const decryptedZone = await decryptDataAsync(decrypted.intervention_zone);
          // Essayer de parser en JSON
          try {
            return { field: 'intervention_zone', value: JSON.parse(decryptedZone) };
          } catch (jsonError) {
            // Si ce n'est pas du JSON valide, garder la chaîne déchiffrée
            return { field: 'intervention_zone', value: decryptedZone };
          }
        } catch (error) {
          logger.warn('Erreur lors du déchiffrement de intervention_zone:', {
            error: error instanceof Error ? error.message : String(error),
            fieldValue: decrypted.intervention_zone?.substring(0, 50) || 'empty'
          });
          // Garder la valeur originale en cas d'erreur (probablement non chiffrée)
          return { field: 'intervention_zone', value: decrypted.intervention_zone };
        }
      })()
    );
  }

  // Attendre tous les déchiffrements en parallèle
  const results = await Promise.all(decryptionPromises);

  // Appliquer les résultats
  results.forEach(result => {
    if (result) {
      decrypted[result.field] = result.value;
    }
  });

  return decrypted;
};

/**
 * Chiffre les données sensibles d'un utilisateur
 * @param userData Données utilisateur à chiffrer
 * @returns Données utilisateur avec les champs sensibles chiffrés
 */
export const encryptUserDataAsync = async (userData: any): Promise<any> => {
  if (!userData) return userData;

  const encrypted = { ...userData };

  // Traitement en parallèle pour optimiser les performances
  const encryptionPromises: Promise<{ field: string; value: string }>[] = [];

  // Champs à chiffrer dans users
  if (encrypted.email && typeof encrypted.email === 'string') {
    encryptionPromises.push(
      encryptDataAsync(encrypted.email).then(value => ({ field: 'email', value }))
    );
  }

  if (encrypted.temp_email && typeof encrypted.temp_email === 'string') {
    encryptionPromises.push(
      encryptDataAsync(encrypted.temp_email).then(value => ({ field: 'temp_email', value }))
    );
  }

  if (encrypted.telephone && typeof encrypted.telephone === 'string') {
    encryptionPromises.push(
      encryptDataAsync(encrypted.telephone).then(value => ({ field: 'telephone', value }))
    );
  }

  // Attendre tous les chiffrements en parallèle
  const results = await Promise.all(encryptionPromises);

  // Appliquer les résultats
  results.forEach(result => {
    encrypted[result.field] = result.value;
  });

  return encrypted;
};

/**
 * Déchiffre les données sensibles d'un utilisateur
 * @param userData Données utilisateur chiffrées
 * @returns Données utilisateur avec les champs sensibles déchiffrés
 */
export const decryptUserDataAsync = async (userData: any): Promise<any> => {
  if (!userData) return userData;

  const decrypted = { ...userData };

  // Traitement en parallèle pour optimiser les performances
  const decryptionPromises: Promise<{ field: string; value: string }>[] = [];

  // Champs à déchiffrer dans users
  if (decrypted.email && typeof decrypted.email === 'string') {
    decryptionPromises.push(
      (async () => {
        try {
          const value = await decryptDataAsync(decrypted.email);
          return { field: 'email', value };
        } catch (error) {
          logger.warn('Erreur lors du déchiffrement de l\'email:', {
            error: error instanceof Error ? error.message : String(error),
            fieldValue: decrypted.email?.substring(0, 50) || 'empty'
          });
          // Garder la valeur originale en cas d'erreur
          return { field: 'email', value: decrypted.email };
        }
      })()
    );
  }

  if (decrypted.temp_email && typeof decrypted.temp_email === 'string') {
    decryptionPromises.push(
      (async () => {
        try {
          const value = await decryptDataAsync(decrypted.temp_email);
          return { field: 'temp_email', value };
        } catch (error) {
          logger.warn('Erreur lors du déchiffrement du temp_email:', {
            error: error instanceof Error ? error.message : String(error),
            fieldValue: decrypted.temp_email?.substring(0, 50) || 'empty'
          });
          // Garder la valeur originale en cas d'erreur
          return { field: 'temp_email', value: decrypted.temp_email };
        }
      })()
    );
  }

  if (decrypted.telephone && typeof decrypted.telephone === 'string') {
    decryptionPromises.push(
      (async () => {
        try {
          const value = await decryptDataAsync(decrypted.telephone);
          return { field: 'telephone', value };
        } catch (error) {
          logger.warn('Erreur lors du déchiffrement du téléphone:', {
            error: error instanceof Error ? error.message : String(error),
            fieldValue: decrypted.telephone?.substring(0, 50) || 'empty'
          });
          // Garder la valeur originale en cas d'erreur
          return { field: 'telephone', value: decrypted.telephone };
        }
      })()
    );
  }

  // Attendre tous les déchiffrements en parallèle
  const results = await Promise.all(decryptionPromises);

  // Appliquer les résultats
  results.forEach(result => {
    decrypted[result.field] = result.value;
  });

  return decrypted;
};

/**
 * Chiffre les données sensibles d'un client de facturation
 * @param clientData Données client à chiffrer
 * @returns Données client avec les champs sensibles chiffrés
 */
export const encryptClientDataAsync = async (clientData: any): Promise<any> => {
  const fieldsToEncrypt = ['nom', 'email', 'telephone', 'adresse', 'siret', 'tva', 'notes'];
  return await encryptDataFieldsAsync(clientData, fieldsToEncrypt, 'client');
};

/**
 * Déchiffre les données sensibles d'un client de facturation
 * @param clientData Données client chiffrées
 * @returns Données client avec les champs sensibles déchiffrés
 */
export const decryptClientDataAsync = async (clientData: any): Promise<any> => {
  const fieldsToDecrypt = ['nom', 'email', 'telephone', 'adresse', 'siret', 'tva', 'notes'];
  return await decryptDataFieldsAsync(clientData, fieldsToDecrypt, 'client');
};

/**
 * Chiffre les données sensibles d'une facture/devis
 * @param invoiceData Données facture à chiffrer
 * @returns Données facture avec les champs sensibles chiffrés
 */
export const encryptInvoiceDataAsync = async (invoiceData: any): Promise<any> => {
  const fieldsToEncrypt = ['client_name', 'client_address', 'client_email', 'client_phone', 'client_siret', 'client_tva'];
  return await encryptDataFieldsAsync(invoiceData, fieldsToEncrypt, 'facture');
};

/**
 * Déchiffre les données sensibles d'une facture/devis
 * @param invoiceData Données facture chiffrées
 * @returns Données facture avec les champs sensibles déchiffrés
 */
export const decryptInvoiceDataAsync = async (invoiceData: any): Promise<any> => {
  const fieldsToDecrypt = ['client_name', 'client_address', 'client_email', 'client_phone', 'client_siret', 'client_tva'];
  return await decryptDataFieldsAsync(invoiceData, fieldsToDecrypt, 'facture');
};

/**
 * Chiffre les données sensibles des paramètres d'entreprise
 * @param companyData Données entreprise à chiffrer
 * @returns Données entreprise avec les champs sensibles chiffrés
 */
export const encryptCompanyDataAsync = async (companyData: any): Promise<any> => {
  const fieldsToEncrypt = [
    'nom', 'adresse', 'code_postal', 'ville', 'telephone', 'email', 'site_web',
    'siret', 'tva', 'rcs', 'capital', 'iban', 'bic', 'banque', 'forme_juridique', 'code_ape', 'mention_pied_page'
  ];
  return await encryptDataFieldsAsync(companyData, fieldsToEncrypt, 'entreprise');
};

/**
 * Déchiffre les données sensibles des paramètres d'entreprise
 * @param companyData Données entreprise chiffrées
 * @returns Données entreprise avec les champs sensibles déchiffrés
 */
export const decryptCompanyDataAsync = async (companyData: any): Promise<any> => {
  const fieldsToDecrypt = [
    'nom', 'adresse', 'code_postal', 'ville', 'telephone', 'email', 'site_web',
    'siret', 'tva', 'rcs', 'capital', 'iban', 'bic', 'banque', 'forme_juridique', 'code_ape', 'mention_pied_page'
  ];
  return await decryptDataFieldsAsync(companyData, fieldsToDecrypt, 'entreprise');
};

// ============================================================================
// FONCTIONS UTILITAIRES
// ============================================================================

/**
 * Crée un hash de l'email pour permettre la recherche efficace
 * @param email Email à hasher
 * @returns Hash de l'email
 */
export const hashEmail = (email: string): string => {
  if (!email || typeof email !== 'string') {
    return '';
  }

  // Normaliser l'email (minuscules, trim)
  const normalizedEmail = email.toLowerCase().trim();

  // Créer un hash SHA-256
  return crypto.createHash('sha256').update(normalizedEmail).digest('hex');
};

/**
 * Vérifie si un email correspond à un hash donné
 * @param email Email à vérifier
 * @param hash Hash à comparer
 * @returns true si l'email correspond au hash
 */
export const verifyEmailHash = (email: string, hash: string): boolean => {
  if (!email || !hash) {
    return false;
  }

  return hashEmail(email) === hash;
};