import { useState, useEffect } from 'react';
import axios from 'axios';
import { API_CONFIG } from '../config/api';
import { notify } from '../components/Notification';
import logger from '../utils/logger';
import { useNavigate } from 'react-router-dom';

interface MissionStatistics {
  total: number;
  acceptees: number;
  enAttente: number;
  enRetard: number;
  disponibles: number;
}

interface Mission {
  id: string;
  titre: string;
  date_creation: string;
  date_mission: string | null;
  evaluation?: number;
  commentaire?: string;
  statut: string;
  description: string;
  has_user_proposal?: boolean;
}

export const useStatistics = () => {
  const [statistics, setStatistics] = useState<MissionStatistics>({
    total: 0,
    acceptees: 0,
    enAttente: 0,
    enRetard: 0,
    disponibles: 0
  });
  const [myMissions, setMyMissions] = useState<Mission[]>([]);
  const [availableMissions, setAvailableMissions] = useState<Mission[]>([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  const checkUserProposal = async (mission: Mission): Promise<Mission> => {
    try {
      const response = await axios.get(`/api/missions/${mission.id}/user-proposal`, API_CONFIG);
      return {
        ...mission,
        has_user_proposal: !!response.data.proposal
      };
    } catch (error) {
      logger.error(`Erreur lors de la vérification de l'offre pour la mission ${mission.id}:`, error);
      return {
        ...mission,
        has_user_proposal: false
      };
    }
  };

  const fetchStatistics = async () => {
    try {
      // Récupérer les candidatures envoyées par l'utilisateur
      const candidaturesResponse = await axios.get('/api/missions/propositions/sent', {
        ...API_CONFIG,
        params: {
          limit: 100,
          offset: 0
        }
      });

      const candidatures = candidaturesResponse.data.data || [];

      // Récupérer les candidatures reçues par l'utilisateur
      const candidaturesRecuesResponse = await axios.get('/api/missions/propositions/received', {
        ...API_CONFIG,
        params: {
          limit: 100,
          offset: 0
        }
      });

      const candidaturesRecues = candidaturesRecuesResponse.data.data || [];

      // Logs pour le filtrage des candidatures acceptées
      const candidaturesAcceptees = candidatures.filter((c: any) => c.statut === 'acceptée');
      
      // Récupérer les missions créées par l'utilisateur (owner=true)
      const myCreatedMissionsResponse = await axios.get('/api/missions', {
        ...API_CONFIG,
        params: {
          owner: 'true',
          limit: 100,
          offset: 0
        }
      });

      const myCreatedMissions = myCreatedMissionsResponse.data.data || [];
      
      // Récupérer les missions disponibles pour l'affichage
      const matchingResponse = await axios.get('/api/missions/matching', {
        ...API_CONFIG,
        params: {
          limit: 10, // On augmente la limite pour avoir plus de missions à filtrer
          offset: 0
        }
      });

      const formattedAvailableMissions = matchingResponse.data.data.map((mission: Mission) => ({
        ...mission,
        date_creation: mission.date_creation
      }));

      // Vérifier les offres pour chaque mission disponible
      const availableMissionsWithProposals = await Promise.all(
        formattedAvailableMissions.map(checkUserProposal)
      );

      // Filtrer les missions réellement disponibles (sans proposition de l'utilisateur)
      const reallyAvailableMissions = availableMissionsWithProposals.filter(
        mission => !mission.has_user_proposal
      );

      // Calculer les statistiques
      const candidaturesEnvoyeesEnAttente = candidatures.filter((c: any) => c.statut === 'en_attente').length;
      const candidaturesRecuesEnAttente = candidaturesRecues.filter((c: any) => c.statut === 'en_attente').length;
      
      const stats = {
        total: candidatures.length,
        acceptees: candidaturesAcceptees.length,
        enAttente: candidaturesEnvoyeesEnAttente + candidaturesRecuesEnAttente,
        enRetard: 0,
        disponibles: reallyAvailableMissions.length
      };

      setStatistics(stats);
      // Pour myMissions, on prend les missions créées par l'utilisateur qui sont en cours
      setMyMissions(myCreatedMissions.slice(0, 3));
      setAvailableMissions(reallyAvailableMissions.slice(0, 3));
      setLoading(false);
    } catch (error: any) {
      logger.error('Erreur lors de la récupération des statistiques:', error);
      notify('Erreur lors de la récupération des statistiques: ' + (error.response?.data?.message || error.message), 'error');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatistics();
  }, []);

  const goToMyMissions = () => {
    navigate('/dashboard/missions', { state: { activeTab: 0 } }); // 0 correspond à l'index de l'onglet "Mes Missions"
  };

  const goToAllMissions = () => { 
    navigate('/dashboard/missions', { state: { activeTab: 1 } }); // 1 correspond à l'index de l'onglet "Toutes les missions"
  };

  const goToMatchingMissions = () => {
    navigate('/dashboard/missions', { state: { activeTab: 2 } }); // 2 correspond à l'index de l'onglet "Missions à postuler"
  };

  return { statistics, myMissions, availableMissions, loading, goToMatchingMissions, goToMyMissions, goToAllMissions };
};