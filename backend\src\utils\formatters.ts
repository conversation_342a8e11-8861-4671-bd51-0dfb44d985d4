/**
 * Formate un statut de ticket pour l'affichage
 * @param status - Le code du statut (ex: en_cours, nouveau)
 * @returns Le statut formaté pour l'affichage (ex: En cours, Nouveau)
 */
export const formatTicketStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    nouveau: 'Nouveau',
    en_attente: 'En attente',
    en_cours: 'En cours',
    resolu: 'Résolu',
    ferme: 'Fermé',
    reouvert: 'Réouvert'
  };

  return statusMap[status] || status;
};

/**
 * Formate une priorité de ticket pour l'affichage
 * @param priority - Le code de la priorité (ex: elevee, normale)
 * @returns La priorité formatée pour l'affichage (ex: Élevée, Normale)
 */
export const formatTicketPriority = (priority: string): string => {
  const priorityMap: Record<string, string> = {
    faible: 'Faible',
    normale: 'Normale',
    elevee: 'Élevée',
    urgente: 'Urgente'
  };

  return priorityMap[priority] || priority;
};

/**
 * Formate une catégorie de ticket pour l'affichage
 * @param category - Le code de la catégorie (ex: technique, facturation)
 * @returns La catégorie formatée pour l'affichage (ex: Technique, Facturation)
 */
export const formatTicketCategory = (category: string): string => {
  const categoryMap: Record<string, string> = {
    technique: 'Technique',
    facturation: 'Facturation',
    compte: 'Compte',
    mission: 'Mission',
    autre: 'Autre'
  };

  return categoryMap[category] || category;
}; 