import React, { useState } from 'react';
import { Sparkles } from 'lucide-react';
import { Box, Typography } from '@mui/material';
import AiImageGenerationModal from './AiImageGenerationModal';
import { useAiCredits } from '../../hooks/useAiCredits';
import { IMAGE_GENERATION_COST } from '../../hooks/useAiImageGeneration';

interface AiMissionImageGeneratorProps {
  onImageGenerated: (imageUrl: string, imageBase64: string) => void;
  missionTitle?: string;
  missionDescription?: string;
  isCover?: boolean;
  className?: string;
}

/**
 * Composant pour générer une image de mission avec l'IA
 */
const AiMissionImageGenerator: React.FC<AiMissionImageGeneratorProps> = ({
  onImageGenerated,
  missionTitle = '',
  missionDescription = '',
  isCover,
  className = ''
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { credits } = useAiCredits();

  // Générer un prompt par défaut basé sur le titre, la description, la catégorie et la sous-catégorie de la mission
  const generateDefaultPrompt = (): string => {
    let prompt = '';

    // Chercher le nom de la catégorie et sous-catégorie si possible
    if (typeof (missionTitle) === 'string' && missionTitle) {
      // On suppose que missionTitle n'est pas un id mais un titre, donc on ne l'utilise pas pour la catégorie
    }
    // Si la mission contient une catégorie ou sous-catégorie dans le titre ou la description, on ne peut pas le deviner ici
    // Mais on peut enrichir si on reçoit des props supplémentaires plus tard

    // Si on veut enrichir avec la catégorie, il faudrait passer category_id et subcategory_id en props
    // Pour l'instant, on se base sur le titre et la description

    if (missionTitle) {
      prompt += missionTitle;
    }

    if (missionDescription) {
      // Extraire le texte brut de la description HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = missionDescription;
      const textDescription = tempDiv.textContent || tempDiv.innerText || '';

      // Limiter la longueur de la description pour le prompt
      const maxDescriptionLength = 200;
      const truncatedDescription = textDescription.length > maxDescriptionLength
        ? textDescription.substring(0, maxDescriptionLength) + '...'
        : textDescription;

      if (prompt) {
        prompt += ': ';
      }

      prompt += truncatedDescription;
    }

    // Ajout d'un enrichissement générique pour le style d'image
    if (prompt) {
      prompt += '. Photo professionnelle, style moderne, ambiance accueillante, couleurs vives, bien éclairée.';
    }

    // Si aucun titre ni description n'est fourni, utiliser un prompt générique orienté mission
    if (!prompt) {
      prompt = "Photo professionnelle illustrant une mission de jardinage, bricolage ou garde d'animaux, style moderne, ambiance accueillante, couleurs vives, bien éclairée.";
    }

    return prompt;
  };

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleImageGenerated = (imageUrl: string, imageBase64: string) => {
    // Transmettre l'image générée au parent SANS modération immédiate !
    // La modération sera faite plus tard lors du POST de la mission (voir PostMission.tsx)
    onImageGenerated(imageUrl, imageBase64);
    setIsModalOpen(false);
  };

  return (
    <>
      <Box 
        className={`flex flex-col items-center justify-center p-4 border-2 border-dashed border-[#FFE4BA] rounded-xl hover:border-[#FF6B2C] transition-all cursor-pointer ${className}`}
        onClick={handleOpenModal}
      >
        <Sparkles className="h-6 w-6 text-[#FF6B2C] mb-2" />
        <Typography variant="body2" className="text-center font-medium mb-1">
          Générer une image avec l'IA
        </Typography>
        <Typography variant="caption" className="text-center text-gray-500">
          {credits >= IMAGE_GENERATION_COST 
            ? `Coût: ${IMAGE_GENERATION_COST} crédits IA` 
            : `Crédits insuffisants (${credits}/${IMAGE_GENERATION_COST})`}
        </Typography>
      </Box>

      <AiImageGenerationModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onImageGenerated={handleImageGenerated}
        purpose="mission_image"
        defaultPrompt={generateDefaultPrompt()}
        isCover={isCover}
        missionTitle={missionTitle}
        missionDescription={missionDescription}
      />
    </>
  );
};

export default AiMissionImageGenerator;
