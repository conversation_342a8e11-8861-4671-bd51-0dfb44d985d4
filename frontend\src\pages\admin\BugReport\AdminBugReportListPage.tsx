import React, { useState, useEffect } from 'react';
import { Box, Container, Typography, Breadcrumbs, Link as MuiLink, Card, CardContent, Grid, Paper, Divider } from '@mui/material';
import BugReportList from '../../../components/BugReport/BugReportList';
import { Link } from 'react-router-dom';
import { bugReportService } from '../../../services/bugReportService';
import { BugReportStats } from '../../../types/bugReports';
import BugReportIcon from '@mui/icons-material/BugReport';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import PendingIcon from '@mui/icons-material/Pending';
import SettingsIcon from '@mui/icons-material/Settings';
import { alpha } from '@mui/material/styles';
import logger from '@/utils/logger';

const AdminBugReportListPage: React.FC = () => {
  const [stats, setStats] = useState<BugReportStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const data = await bugReportService.getStats();
        setStats(data);
      } catch (error) {
        logger.error('Erreur lors de la récupération des statistiques:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  // Calcul des statistiques
  const getBugCount = () => {
    if (!stats?.type) return 0;
    const bugType = stats.type.find(item => item.report_type === 'bug');
    return bugType ? bugType.count : 0;
  };

  const getImprovementCount = () => {
    if (!stats?.type) return 0;
    const impType = stats.type.find(item => item.report_type === 'improvement');
    return impType ? impType.count : 0;
  };

  const getResolvedCount = () => {
    if (!stats?.status) return 0;
    const resolvedStatus = stats.status.find(item => item.status === 'resolu');
    return resolvedStatus ? resolvedStatus.count : 0;
  };

  const getPendingCount = () => {
    if (!stats?.status) return 0;
    const newStatus = stats.status.find(item => item.status === 'nouveau');
    const inProgressStatus = stats.status.find(item => item.status === 'en_cours');
    return (newStatus ? newStatus.count : 0) + (inProgressStatus ? inProgressStatus.count : 0);
  };

  return (
    <Box 
      sx={{ 
        background: `linear-gradient(to bottom, #FFF8F3, white)`,
        minHeight: '100vh'
      }}
    >
      <Container maxWidth="lg">
        <Box sx={{ py: 2 }}>
          {/* Breadcrumbs avec style amélioré */}
          <Breadcrumbs 
            sx={{ 
              mb: 3,
              '& .MuiBreadcrumbs-ol': {
                backgroundColor: alpha('#FFF8F3', 0.6),
                padding: '8px 16px',
                borderRadius: '8px',
                boxShadow: '0 1px 3px rgba(0,0,0,0.05)'
              }
            }}
          >
            <MuiLink 
              component={Link} 
              to="/admin" 
              color="inherit"
              sx={{ 
                display: 'flex', 
                alignItems: 'center',
                '&:hover': { color: '#FF6B2C' } 
              }}
            >
              <SettingsIcon fontSize="small" sx={{ mr: 0.5 }} />
              Administration
            </MuiLink>
            <Typography 
              color="text.primary" 
              sx={{ 
                fontWeight: 'medium', 
                display: 'flex', 
                alignItems: 'center' 
              }}
            >
              <BugReportIcon fontSize="small" sx={{ mr: 0.5 }} />
              Gestion des bugs
            </Typography>
          </Breadcrumbs>

          {/* En-tête avec style amélioré */}
          <Paper 
            elevation={0} 
            sx={{ 
              p: { xs: 1.5, sm: 2 }, 
              mb: 0, 
              borderLeft: { xs: '3px solid #FF6B2C', sm: '4px solid #FF6B2C' },
              background: 'linear-gradient(135deg, #FFF8F3 0%, white 100%)',
              borderRadius: { xs: '6px', sm: '8px' },
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
            }}
          >
            <Typography 
              variant="h4" 
              component="h1" 
              gutterBottom
              sx={{ 
                fontWeight: 700, 
                color: '#333',
                position: 'relative',
                display: 'inline-block',
                fontSize: { xs: '1.35rem', sm: '1.55rem', md: '1.75rem' },
                lineHeight: { xs: 1.3, sm: 1.4 },
                mb: { xs: 2, sm: 1 },
                '&:after': {
                  content: '""',
                  position: 'absolute',
                  bottom: -6,
                  left: 0,
                  width: { xs: '60px', sm: '80px' },
                  height: { xs: '3px', sm: '4px' },
                  background: 'linear-gradient(to right, #FF6B2C, #FF965E)',
                  borderRadius: '2px'
                }
              }}
            >
              Gestion des rapports de bugs et suggestions
            </Typography>
            <Typography 
              variant="body1" 
              color="text.secondary"
              sx={{ 
                maxWidth: '800px', 
                mt: { xs: 3, sm: 2 },
                fontSize: { xs: '0.875rem', sm: '1rem' },
                lineHeight: { xs: 1.5, sm: 1.6 }
              }}
            >
              Cet espace vous permet de surveiller et gérer les rapports de bugs et suggestions d'amélioration envoyés par les utilisateurs.
            </Typography>
          </Paper>

          {/* Cartes de statistiques avec style amélioré */}
          {!loading && stats && (
            <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mt: { xs: 1, sm: 2 }, mb: { xs: 3, sm: 4 } }}>
              <Grid size={{ xs: 6, sm: 6, md: 3 }}>
                <Card 
                  sx={{ 
                    height: '100%',
                    borderRadius: { xs: 2, sm: 3 },
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 8px 20px rgba(255, 107, 44, 0.15)'
                    }
                  }}
                >
                  <CardContent 
                    sx={{ 
                      display: 'flex', 
                      flexDirection: 'column', 
                      alignItems: 'center',
                      background: 'linear-gradient(135deg, #FF6B2C 0%, #FF7A35 100%)',
                      color: 'white',
                      borderRadius: { xs: 2, sm: 3 },
                      p: { xs: 1.5, sm: 2 }
                    }}
                  >
                    <Box sx={{ 
                      display: 'flex', 
                      flexDirection: 'column',
                      alignItems: 'center',
                      p: 2,
                      width: '100%'
                    }}>
                      <Box 
                        sx={{ 
                          backgroundColor: 'rgba(255, 255, 255, 0.15)',
                          borderRadius: '50%',
                          p: { xs: 1, sm: 1.5 },
                          mb: { xs: 1, sm: 2 },
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <BugReportIcon sx={{ fontSize: { xs: 24, sm: 32, md: 40 } }} />
                      </Box>
                      <Typography 
                        variant="h3" 
                        component="div"
                        sx={{ 
                          fontWeight: 'bold', 
                          mb: { xs: 0.5, sm: 1 },
                          fontSize: { xs: '1.5rem', sm: '2rem', md: '2.5rem' }
                        }}
                      >
                        {getBugCount()}
                      </Typography>
                      <Typography 
                        variant="body1" 
                        sx={{ 
                          fontWeight: 'medium',
                          fontSize: { xs: '0.875rem', sm: '1rem' }
                        }}
                      >
                        Bugs signalés
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid size={{ xs: 6, sm: 6, md: 3 }}>
                <Card 
                  sx={{ 
                    height: '100%',
                    borderRadius: { xs: 2, sm: 3 },
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 8px 20px rgba(255, 122, 53, 0.15)'
                    }
                  }}
                >
                  <CardContent 
                    sx={{ 
                      display: 'flex', 
                      flexDirection: 'column', 
                      alignItems: 'center',
                      background: 'linear-gradient(135deg, #FF6B2C 0%, #FF7A35 100%)',
                      color: 'white',
                      borderRadius: { xs: 2, sm: 3 },
                      p: { xs: 1.5, sm: 2 }
                    }}
                  >
                    <Box sx={{ 
                      display: 'flex', 
                      flexDirection: 'column',
                      alignItems: 'center',
                      p: 2,
                      width: '100%'
                    }}>
                      <Box 
                        sx={{ 
                          backgroundColor: 'rgba(255, 255, 255, 0.15)',
                          borderRadius: '50%',
                          p: { xs: 1, sm: 1.5 },
                          mb: { xs: 1, sm: 2 },
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <LightbulbIcon sx={{ fontSize: { xs: 24, sm: 32, md: 40 } }} />
                      </Box>
                      <Typography 
                        variant="h3" 
                        component="div"
                        sx={{ 
                          fontWeight: 'bold', 
                          mb: { xs: 0.5, sm: 1 },
                          fontSize: { xs: '1.5rem', sm: '2rem', md: '2.5rem' }
                        }}
                      >
                        {getImprovementCount()}
                      </Typography>
                      <Typography 
                        variant="body1" 
                        sx={{ 
                          fontWeight: 'medium',
                          fontSize: { xs: '0.875rem', sm: '1rem' }
                        }}
                      >
                        Suggestions
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid size={{ xs: 6, sm: 6, md: 3 }}>
                <Card 
                  sx={{ 
                    height: '100%',
                    borderRadius: { xs: 2, sm: 3 },
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 8px 20px rgba(255, 150, 94, 0.15)'
                    }
                  }}
                >
                  <CardContent 
                    sx={{ 
                      display: 'flex', 
                      flexDirection: 'column', 
                      alignItems: 'center',
                      background: 'linear-gradient(135deg, #FF6B2C 0%, #FF7A35 100%)',
                      color: 'white',
                      borderRadius: { xs: 2, sm: 3 },
                      p: { xs: 1.5, sm: 2 }
                    }}
                  >
                    <Box sx={{ 
                      display: 'flex', 
                      flexDirection: 'column',
                      alignItems: 'center',
                      p: 2,
                      width: '100%'
                    }}>
                      <Box 
                        sx={{ 
                          backgroundColor: 'rgba(255, 255, 255, 0.15)',
                          borderRadius: '50%',
                          p: { xs: 1, sm: 1.5 },
                          mb: { xs: 1, sm: 2 },
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <CheckCircleIcon sx={{ fontSize: { xs: 24, sm: 32, md: 40 } }} />
                      </Box>
                      <Typography 
                        variant="h3" 
                        component="div"
                        sx={{ 
                          fontWeight: 'bold', 
                          mb: { xs: 0.5, sm: 1 },
                          fontSize: { xs: '1.5rem', sm: '2rem', md: '2.5rem' }
                        }}
                      >
                        {getResolvedCount()}
                      </Typography>
                      <Typography 
                        variant="body1" 
                        sx={{ 
                          fontWeight: 'medium',
                          fontSize: { xs: '0.875rem', sm: '1rem' }
                        }}
                      >
                        Résolus
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid size={{ xs: 6, sm: 6, md: 3 }}>
                <Card 
                  sx={{ 
                    height: '100%',
                    borderRadius: { xs: 2, sm: 3 },
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 8px 20px rgba(255, 228, 186, 0.2)'
                    } 
                  }}
                >
                  <CardContent 
                    sx={{ 
                      display: 'flex', 
                      flexDirection: 'column', 
                      alignItems: 'center',
                      background: 'linear-gradient(135deg, #FF6B2C 0%, #FF7A35 100%)',
                      color: 'white',
                      borderRadius: { xs: 2, sm: 3 },
                      p: { xs: 1.5, sm: 2 }
                    }}
                  >
                    <Box sx={{ 
                      display: 'flex', 
                      flexDirection: 'column',
                      alignItems: 'center',
                      p: 2,
                      width: '100%'
                    }}>
                      <Box 
                        sx={{ 
                          backgroundColor: 'rgba(255, 255, 255, 0.15)',
                          borderRadius: '50%',
                          p: { xs: 1, sm: 1.5 },
                          mb: { xs: 1, sm: 2 },
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <PendingIcon sx={{ fontSize: { xs: 24, sm: 32, md: 40 } }} />
                      </Box>
                      <Typography 
                        variant="h3" 
                        component="div"
                        sx={{ 
                          fontWeight: 'bold', 
                          mb: { xs: 0.5, sm: 1 },
                          fontSize: { xs: '1.5rem', sm: '2rem', md: '2.5rem' }
                        }}
                      >
                        {getPendingCount()}
                      </Typography>
                      <Typography 
                        variant="body1" 
                        sx={{ 
                          fontWeight: 'medium',
                          fontSize: { xs: '0.875rem', sm: '1rem' }
                        }}
                      >
                        En attente
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* Section de liste avec un style plus cohérent */}
          <Box sx={{ 
            borderRadius: '12px', 
            overflow: 'hidden',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}>
            <BugReportList isAdmin={true} />
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default AdminBugReportListPage; 