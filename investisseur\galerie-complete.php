<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Galerie Complète - JobPartiel | Plateforme de Jobbing Révolutionnaire</title>
    <meta name="description" content="Découvrez en détail toutes les fonctionnalités de JobPartiel : IA spécialisée, système Jobi, facturation automatique, analytics avancés et bien plus encore.">
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <style>
        /* Styles pour la galerie complète simplifiée */
        #modalImage {
            max-height: none!important;
        }

        .main-gallery {
            padding: 80px 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .gallery-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .gallery-header h1 {
            font-size: 3rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .gallery-header p {
            font-size: 1.2rem;
            color: #6c757d;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Grille de galerie simplifiée */
        .gallery-masonry {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }

        .gallery-item-simple {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .gallery-item-simple:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .gallery-image-simple {
            width: 100%;
            height: 200px; /* Hauteur fixe pour cohérence visuelle */
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .gallery-item-simple:hover .gallery-image-simple {
            transform: scale(1.02);
        }

        /* Styles spécifiques pour la modal sur mobile */
        @media (max-width: 768px) {
            .gallery-masonry {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 15px;
            }

            .gallery-image-simple {
                height: 180px;
            }

            /* Corrections pour la modal sur mobile */
            .modal-content {
                width: 100vw !important;
                height: 100vh !important;
                max-width: 100vw !important;
                max-height: 100vh !important;
                border-radius: 0 !important;
                margin: 0 !important;
            }

            .modal-image-container {
                height: 100vh !important;
                width: 100% !important;
                overflow: auto !important;
                background: #000 !important;
            }

            .modal-image-zoomable {
                width: 100% !important;
                height: auto !important;
                min-height: 100vh !important;
                object-fit: contain !important;
                display: block !important;
            }

            .modal-image-zoomable.zoomed {
                width: 200% !important;
                height: auto !important;
                min-height: 200vh !important;
            }

            /* Navigation sur mobile - repositionnée en bas avec spécificité élevée */
            #imageModal .modal-navigation {
                position: fixed !important;
                top: auto !important;
                bottom: 30px !important;
                left: 0 !important;
                right: 0 !important;
                transform: none !important;
                width: 100% !important;
                display: flex !important;
                justify-content: center !important;
                gap: 40px !important;
                z-index: 10001 !important;
                padding: 0 20px !important;
                pointer-events: none !important;
            }

            #imageModal .nav-btn {
                width: 60px !important;
                height: 60px !important;
                font-size: 24px !important;
                background: rgba(0, 0, 0, 0.8) !important;
                border: 2px solid rgba(255, 255, 255, 0.3) !important;
                border-radius: 50% !important;
                color: white !important;
                pointer-events: all !important;
                transition: all 0.3s ease !important;
            }

            #imageModal .nav-btn:hover {
                background: rgba(255, 107, 44, 0.9) !important;
                border-color: rgba(255, 255, 255, 0.8) !important;
                transform: scale(1.1) !important;
            }

            /* Bouton de fermeture sur mobile */
            .modal-close {
                position: fixed !important;
                top: 20px !important;
                right: 20px !important;
                z-index: 10001 !important;
                width: 50px !important;
                height: 50px !important;
                background: rgba(0, 0, 0, 0.8) !important;
                color: white !important;
                border: 2px solid rgba(255, 255, 255, 0.3) !important;
                border-radius: 50% !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                font-size: 24px !important;
                cursor: pointer !important;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation simplifiée -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="index.php" class="nav-logo">
                <div class="logo-container">
                    <img src="logo_banniere_job_partiel_grand.webp" alt="JobPartiel Logo" style="height: 40px; width: auto;">
                    <div class="mascotte-clock" title="Notre mascotte - L'efficacité du temps">
                        <div class="clock-face">
                            <div class="clock-hand hour-hand"></div>
                            <div class="clock-hand minute-hand"></div>
                            <div class="clock-center"></div>
                        </div>
                    </div>
                </div>
            </a>
            
            <div class="nav-menu">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-arrow-left"></i> Retour au Pitch
                </a>
                <a href="index.php#contact" class="nav-link cta-button-primary">
                    <i class="fas fa-handshake"></i> Investir Maintenant
                </a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="gallery-hero">
        <div class="container">
            <div class="gallery-hero-content" data-aos="fade-up">
                <h1 class="gallery-hero-title">
                    Galerie Complète <span class="gradient-text">JobPartiel</span>
                </h1>
                <p class="gallery-hero-subtitle">
                    Explorez en détail chaque aspect de notre plateforme révolutionnaire. 
                    Plus de 80 captures d'écran démontrant notre avance technologique unique.
                </p>
                <div class="gallery-hero-stats">
                    <div class="hero-stat">
                        <span class="stat-number">80+</span>
                        <span class="stat-label">Captures détaillées</span>
                    </div>
                    <div class="hero-stat">
                        <span class="stat-number">15</span>
                        <span class="stat-label">Catégories</span>
                    </div>
                    <div class="hero-stat">
                        <span class="stat-number">1ère</span>
                        <span class="stat-label">IA Jobbing mondiale</span>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- Galerie principale -->
    <section class="main-gallery">
        <div class="container">
            <div class="gallery-header" data-aos="fade-up">
                <h1>Galerie Complète</h1>
                <p>Explorez en détail toutes les fonctionnalités de notre plateforme révolutionnaire</p>
            </div>

            <div class="gallery-masonry" id="galleryContainer">
                <!-- Les images seront ajoutées ici via JavaScript ou PHP -->
            </div>
        </div>
    </section>

    <!-- Modal pour affichage plein écran -->
    <div id="imageModal" class="image-modal">
        <div class="modal-content">
            <span class="modal-close">&times;</span>
            <div class="modal-image-container">
                <img id="modalImage" src="" alt="" class="modal-image-zoomable">
            </div>
            <div class="modal-navigation">
                <button id="prevImage" class="nav-btn">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button id="nextImage" class="nav-btn">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- CTA de retour -->
    <section class="gallery-return-cta">
        <div class="container">
            <div class="return-cta-content" data-aos="fade-up">
                <h2>Convaincu par notre Innovation ?</h2>
                <p>Rejoignez la révolution du jobbing avec notre IA spécialisée unique au monde</p>
                <div class="return-cta-buttons">
                    <a href="index.php#financement" class="cta-primary">
                        <i class="fas fa-rocket"></i> Investir 1,5M€
                    </a>
                    <a href="index.php" class="cta-secondary">
                        <i class="fas fa-arrow-left"></i> Retour au Pitch
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script>
        // Initialisation AOS
        AOS.init({
            duration: 800,
            easing: 'ease-out-cubic',
            once: true,
            offset: 100
        });

        // Configuration des images avec métadonnées
        const galleryImages = [
            // Interface principale
            {
                src: 'images_projet/JobPartiel.fr - Vos compétences, vos missions. Vos revenus, vos règles..jpg',
                title: 'Dashboard Principal',
                description: 'Interface rapide avec aperçu des informations importantes visibles dès l\'arrivée',
                category: 'interface',
                badge: 'Accueil'
            },
            {
                src: 'images_projet/Mon Profil - JobPartiel.fr.jpg',
                title: 'Gestion de Profil Avancée',
                description: 'Profil complet avec compétences, historique Jobi et analytics personnalisés',
                category: 'profil',
                badge: 'Profil'
            },
            // Captures numérotées - Interface
            {
                src: 'images_projet/Capture_complete_une.jpg',
                title: 'Dashboard Principal',
                description: 'Vue d\'ensemble avec missions, Jobi et recommandations IA en temps réel',
                category: 'interface',
                badge: 'Dashboard'
            },
            {
                src: 'images_projet/Capture_complete_deux.jpg',
                title: 'Système de Matching Intelligent',
                description: 'Algorithme Haversine et IA pour des correspondances parfaites jobbeur-mission',
                category: 'ia',
                badge: 'Matching IA'
            },
            {
                src: 'images_projet/Capture_complete_trois.jpg',
                title: 'Facturation Automatisée',
                description: 'Génération automatique de devis et factures avec intégration comptable',
                category: 'facturation',
                badge: 'Facturation'
            },
            {
                src: 'images_projet/Capture_complete_quatre.jpg',
                title: 'Analytics Avancés',
                description: 'Métriques détaillées, insights IA et tableaux de bord personnalisables',
                category: 'analytics',
                badge: 'Analytics'
            },
            {
                src: 'images_projet/Capture_complete_cinq.jpg',
                title: 'Gestion des Missions',
                description: 'Interface complète pour créer, gérer et suivre les missions',
                category: 'missions',
                badge: 'Missions'
            },
            {
                src: 'images_projet/Capture_complete_six.jpg',
                title: 'Système Jobi en Action',
                description: 'Échange de Jobi entre utilisateurs avec historique détaillé',
                category: 'jobi',
                badge: 'Jobi'
            }
        ];

        // Fonction pour charger les images
        function loadGallery() {
            const container = document.getElementById('galleryContainer');

            galleryImages.forEach((image, index) => {
                const galleryItem = document.createElement('div');
                galleryItem.className = 'gallery-item-simple';
                galleryItem.setAttribute('data-aos', 'fade-up');
                galleryItem.setAttribute('data-aos-delay', (index % 6) * 100);

                galleryItem.innerHTML = `
                    <img data-src="${image.src}"
                         alt="${image.title}"
                         class="gallery-image-simple lazy-load"
                         loading="lazy"
                         onclick="openModal(${index})">
                `;

                container.appendChild(galleryItem);
            });
        }

        // Ajouter toutes les autres images
        const additionalImages = [
            // Captures numérotées suite
            { src: 'images_projet/Capture_complete_sept.jpg', title: 'Interface de Communication', description: 'Messagerie intégrée avec notifications intelligentes', category: 'interface', badge: 'Communication' },
            { src: 'images_projet/Capture_complete_huit.jpg', title: 'Paramètres Avancés', description: 'Configuration personnalisée de l\'IA et des préférences', category: 'admin', badge: 'Paramètres' },
            { src: 'images_projet/Capture_complete_neuf.jpg', title: 'Historique des Transactions', description: 'Suivi complet des échanges Jobi et paiements euros', category: 'jobi', badge: 'Historique' },
            { src: 'images_projet/Capture_complete_dix.jpg', title: 'Recommandations IA', description: 'Suggestions personnalisées basées sur l\'apprentissage automatique', category: 'ia', badge: 'Recommandations' },
            { src: 'images_projet/Capture_complete_onze.jpg', title: 'Gestion des Compétences', description: 'Système avancé de validation et certification des compétences', category: 'profil', badge: 'Compétences' },
            { src: 'images_projet/Capture_complete_douze.jpg', title: 'Tableau de Bord Analytics', description: 'Métriques en temps réel avec visualisations interactives', category: 'analytics', badge: 'Métriques' },
            { src: 'images_projet/Capture_complete_treize.jpg', title: 'Facturation Intelligente', description: 'Génération automatique avec calculs de taxes et remises', category: 'facturation', badge: 'Facturation Pro' },
            { src: 'images_projet/Capture_complete_quatorze.jpg', title: 'Recherche Avancée', description: 'Filtres intelligents avec géolocalisation et matching IA', category: 'interface', badge: 'Recherche' },
            { src: 'images_projet/Capture_complete_quinze.jpg', title: 'Notifications Intelligentes', description: 'Système de notifications contextuelles et personnalisées', category: 'interface', badge: 'Notifications' },
            { src: 'images_projet/Capture_complete_seize.jpg', title: 'Gestion des Équipes', description: 'Outils collaboratifs pour les projets multi-jobbeurs', category: 'missions', badge: 'Équipes' }
        ];

        // Ajouter les images numérotées (17-77)
        for (let i = 17; i <= 77; i++) {
            const categories = ['interface', 'ia', 'jobi', 'facturation', 'analytics', 'profil', 'missions', 'admin'];
            const badges = ['Interface', 'IA', 'Jobi', 'Facturation', 'Analytics', 'Profil', 'Missions', 'Admin'];
            const categoryIndex = (i - 17) % categories.length;

            additionalImages.push({
                src: `images_projet/Capture_complete_${i}.jpg`,
                title: `Fonctionnalité Avancée ${i}`,
                description: `Démonstration des capacités ${badges[categoryIndex].toLowerCase()} de la plateforme JobPartiel`,
                category: categories[categoryIndex],
                badge: badges[categoryIndex]
            });
        }

        // Fusionner toutes les images
        galleryImages.push(...additionalImages);



        // Modal functions
        let currentImageIndex = 0;

        // Variables globales pour la gestion des interactions
        let modalInteractionHandlers = {
            image: {},
            container: {}
        };
        let isDragging = false;
        let hasMoved = false;
        let startX, startY, scrollLeft, scrollTop;
        let clickStartX, clickStartY;

        function openModal(index) {
            currentImageIndex = index;
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const container = document.querySelector('.modal-image-container');

            const image = galleryImages[index];
            modalImage.src = image.src;
            modalImage.classList.remove('zoomed', 'dragging');
            container.classList.remove('dragging');

            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';

            // Reset scroll position et variables
            container.scrollTop = 0;
            container.scrollLeft = 0;
            isDragging = false;
            hasMoved = false;

            // Nettoyer les anciens event listeners avant d'en ajouter de nouveaux
            cleanupImageInteraction(modalImage, container);

            // Ajouter la fonctionnalité de zoom et drag
            setupImageInteraction(modalImage, container);
        }

        function cleanupImageInteraction(image, container) {
            // Supprimer tous les event listeners existants
            if (modalInteractionHandlers.image.mousedown) {
                image.removeEventListener('mousedown', modalInteractionHandlers.image.mousedown);
            }
            if (modalInteractionHandlers.image.click) {
                image.removeEventListener('click', modalInteractionHandlers.image.click);
            }
            if (modalInteractionHandlers.container.mousedown) {
                container.removeEventListener('mousedown', modalInteractionHandlers.container.mousedown);
            }
            if (modalInteractionHandlers.container.mouseleave) {
                container.removeEventListener('mouseleave', modalInteractionHandlers.container.mouseleave);
            }
            if (modalInteractionHandlers.container.mouseup) {
                container.removeEventListener('mouseup', modalInteractionHandlers.container.mouseup);
            }
            if (modalInteractionHandlers.container.mousemove) {
                container.removeEventListener('mousemove', modalInteractionHandlers.container.mousemove);
            }
            if (modalInteractionHandlers.container.touchstart) {
                container.removeEventListener('touchstart', modalInteractionHandlers.container.touchstart);
            }
            if (modalInteractionHandlers.container.touchmove) {
                container.removeEventListener('touchmove', modalInteractionHandlers.container.touchmove);
            }
            if (modalInteractionHandlers.container.touchend) {
                container.removeEventListener('touchend', modalInteractionHandlers.container.touchend);
            }
        }

        function setupImageInteraction(image, container) {
            // Créer les handlers et les stocker pour pouvoir les supprimer plus tard
            modalInteractionHandlers.image.mousedown = function(e) {
                clickStartX = e.clientX;
                clickStartY = e.clientY;
                hasMoved = false;
            };

            modalInteractionHandlers.image.click = function(e) {
                if (!hasMoved) {
                    const wasZoomed = this.classList.contains('zoomed');
                    this.classList.toggle('zoomed');

                    if (!wasZoomed) {
                        // Zoom vers l'endroit du clic
                        const rect = container.getBoundingClientRect();
                        const imageRect = image.getBoundingClientRect();

                        // Position relative du clic dans l'image
                        const clickX = e.clientX - imageRect.left;
                        const clickY = e.clientY - imageRect.top;

                        // Calculer la position de scroll pour centrer le clic
                        const scaleRatio = 2; // Notre zoom est x2
                        const newScrollLeft = (clickX * scaleRatio) - (rect.width / 2);
                        const newScrollTop = (clickY * scaleRatio) - (rect.height / 2);

                        // Appliquer le scroll après un petit délai pour que le zoom soit appliqué
                        setTimeout(() => {
                            container.scrollLeft = Math.max(0, newScrollLeft);
                            container.scrollTop = Math.max(0, newScrollTop);
                        }, 50);
                    } else {
                        // Dézoom : retour en haut à gauche
                        container.scrollTop = 0;
                        container.scrollLeft = 0;
                    }
                }
            };

            // Drag functionality pour les images zoomées
            modalInteractionHandlers.container.mousedown = function(e) {
                if (image.classList.contains('zoomed')) {
                    isDragging = true;
                    hasMoved = false;
                    container.classList.add('dragging');
                    image.classList.add('dragging');
                    startX = e.pageX;
                    startY = e.pageY;
                    scrollLeft = container.scrollLeft;
                    scrollTop = container.scrollTop;
                    e.preventDefault();
                }
            };

            modalInteractionHandlers.container.mouseleave = function() {
                isDragging = false;
                container.classList.remove('dragging');
                image.classList.remove('dragging');
            };

            modalInteractionHandlers.container.mouseup = function() {
                isDragging = false;
                container.classList.remove('dragging');
                image.classList.remove('dragging');
            };

            modalInteractionHandlers.container.mousemove = function(e) {
                // Détecter le mouvement pour distinguer clic/glisser
                if (Math.abs(e.clientX - clickStartX) > 5 || Math.abs(e.clientY - clickStartY) > 5) {
                    hasMoved = true;
                }

                if (!isDragging || !image.classList.contains('zoomed')) return;
                e.preventDefault();

                const deltaX = e.pageX - startX;
                const deltaY = e.pageY - startY;

                container.scrollLeft = scrollLeft - deltaX;
                container.scrollTop = scrollTop - deltaY;
            };

            // Ajouter les event listeners
            image.addEventListener('mousedown', modalInteractionHandlers.image.mousedown);
            image.addEventListener('click', modalInteractionHandlers.image.click);
            container.addEventListener('mousedown', modalInteractionHandlers.container.mousedown);
            container.addEventListener('mouseleave', modalInteractionHandlers.container.mouseleave);
            container.addEventListener('mouseup', modalInteractionHandlers.container.mouseup);
            container.addEventListener('mousemove', modalInteractionHandlers.container.mousemove);

            // Touch support for mobile
            let touchStartX, touchStartY;

            modalInteractionHandlers.container.touchstart = function(e) {
                if (image.classList.contains('zoomed')) {
                    isDragging = true;
                    hasMoved = false;
                    const touch = e.touches[0];
                    touchStartX = touch.pageX;
                    touchStartY = touch.pageY;
                    startX = touch.pageX;
                    startY = touch.pageY;
                    scrollLeft = container.scrollLeft;
                    scrollTop = container.scrollTop;
                    e.preventDefault();
                }
            };

            modalInteractionHandlers.container.touchmove = function(e) {
                if (!isDragging || !image.classList.contains('zoomed')) return;
                e.preventDefault();

                const touch = e.touches[0];

                // Détecter le mouvement
                if (Math.abs(touch.pageX - touchStartX) > 10 || Math.abs(touch.pageY - touchStartY) > 10) {
                    hasMoved = true;
                }

                const deltaX = touch.pageX - startX;
                const deltaY = touch.pageY - startY;

                container.scrollLeft = scrollLeft - deltaX;
                container.scrollTop = scrollTop - deltaY;
            };

            modalInteractionHandlers.container.touchend = function() {
                isDragging = false;
            };

            // Ajouter les event listeners tactiles
            container.addEventListener('touchstart', modalInteractionHandlers.container.touchstart);
            container.addEventListener('touchmove', modalInteractionHandlers.container.touchmove);
            container.addEventListener('touchend', modalInteractionHandlers.container.touchend);
        }

        function closeModal() {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const container = document.querySelector('.modal-image-container');

            // Nettoyer les event listeners avant de fermer
            cleanupImageInteraction(modalImage, container);

            // Reset des états
            modalImage.classList.remove('zoomed', 'dragging');
            container.classList.remove('dragging');
            isDragging = false;
            hasMoved = false;

            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function nextImage() {
            currentImageIndex = (currentImageIndex + 1) % galleryImages.length;
            openModal(currentImageIndex);
        }

        function prevImage() {
            currentImageIndex = (currentImageIndex - 1 + galleryImages.length) % galleryImages.length;
            openModal(currentImageIndex);
        }

        // Lazy loading implementation
        function initLazyLoading() {
            const lazyImages = document.querySelectorAll('.lazy-load');

            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy-load');
                            img.classList.add('loaded');
                            observer.unobserve(img);
                        }
                    });
                }, {
                    rootMargin: '50px 0px',
                    threshold: 0.01
                });

                lazyImages.forEach(img => imageObserver.observe(img));
            } else {
                // Fallback pour les navigateurs plus anciens
                lazyImages.forEach(img => {
                    img.src = img.dataset.src;
                    img.classList.remove('lazy-load');
                    img.classList.add('loaded');
                });
            }
        }

        // Performance optimizations
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Filter function
        function filterGallery() {
            // Pour cette galerie simple, on n'a pas de filtres
            // Cette fonction est gardée pour éviter les erreurs
            console.log('Filter function called');
        }

        // Optimized filter function with debouncing
        const debouncedFilter = debounce(filterGallery, 150);

        // Preload critical images
        function preloadCriticalImages() {
            const criticalImages = galleryImages.slice(0, 6); // Preload first 6 images
            criticalImages.forEach(image => {
                const img = new Image();
                img.src = image.src;
            });
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            loadGallery();
            initLazyLoading();
            preloadCriticalImages();

            // Filtres avec debouncing
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const filter = btn.getAttribute('data-filter');
                    debouncedFilter(filter);
                });
            });

            // Modal events
            document.querySelector('.modal-close').addEventListener('click', closeModal);
            document.getElementById('nextImage').addEventListener('click', nextImage);
            document.getElementById('prevImage').addEventListener('click', prevImage);

            // Fermer modal avec Escape
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') closeModal();
                if (e.key === 'ArrowRight') nextImage();
                if (e.key === 'ArrowLeft') prevImage();
            });

            // Fermer modal en cliquant à l'extérieur
            document.getElementById('imageModal').addEventListener('click', (e) => {
                if (e.target.id === 'imageModal') closeModal();
            });

            // Smooth scroll pour les ancres
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
