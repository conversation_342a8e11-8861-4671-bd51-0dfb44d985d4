import React, { useState } from 'react';
import { Button, IconButton, Box, Paper, Typography, Menu, MenuItem } from '@mui/material';
import { Theme, SxProps } from '@mui/material/styles';
import BugReportIcon from '@mui/icons-material/BugReport';
import CloseIcon from '@mui/icons-material/Close';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import ConstructionIcon from '@mui/icons-material/Construction';
import BugReportForm from './BugReportForm';
import ModalPortal from '../../components/ModalPortal';
import { motion, AnimatePresence } from 'framer-motion';
import { Link as RouterLink } from 'react-router-dom';
import ListAltIcon from '@mui/icons-material/ListAlt';
import SupportIcon from '@mui/icons-material/SupportAgent';
import ProfileCompleteCheck from './ProfileCompleteCheck';
import { useAuth } from '../../contexts/AuthContext';

interface ReportBugButtonProps {
  variant?: 'fab' | 'button';
  position?: 'bottom-right' | 'top-right' | 'inline';
  color?: 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  size?: 'small' | 'medium' | 'large';
  className?: string;
  showViewBugsButton?: boolean;
}

/**
 * Bouton pour signaler un bug ou proposer une amélioration, accessible de n'importe où dans l'application
 */
const ReportBugButton: React.FC<ReportBugButtonProps> = ({
  variant = 'fab',
  position = 'bottom-right',
  size = 'small',
  className,
  showViewBugsButton = true
}) => {
  const [open, setOpen] = useState(false);
  const [reportType, setReportType] = useState<'bug' | 'improvement'>('bug');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { user } = useAuth();
  
  // Vérifier si l'utilisateur a renseigné son nom et prénom
  const isProfileComplete = () => {
    if (!user || !user.profil) return false;
    
    const { nom, prenom } = user.profil.data;
    return !!nom && !!prenom;
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };
  
  const handleMenuClose = () => {
    setAnchorEl(null);
  };
  
  const handleMenuItemClick = (type: 'bug' | 'improvement') => {
    handleMenuClose();
    if (isProfileComplete()) {
      handleOpen(type);
    } else {
      // Ouvrir le modal pour compléter le profil même si le profil n'est pas complet
      // Le composant ProfileCompleteCheck s'occupera d'afficher le message
      handleOpen(type);
    }
  };

  const handleOpen = (type: 'bug' | 'improvement') => {
    setReportType(type);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  // Définir les styles en fonction de la position
  const getPositionStyles = (): SxProps<Theme> => {
    if (position === 'bottom-right') {
      return {
        position: 'fixed',
        bottom: 0,
        right: 0,
        zIndex: 1000,
        m: 0,
        borderRadius: '0'
      };
    } else if (position === 'top-right') {
      return {
        position: 'fixed',
        top: 0,
        right: 0,
        zIndex: 1000,
        m: 0,
        borderRadius: '0'
      };
    }
    // Pour 'inline', pas de style de positionnement spécifique
    return {};
  };

  // Variantes d'animation pour le contenu modal
  const contentVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.3,
        ease: 'easeOut' 
      }
    },
    exit: { 
      opacity: 0, 
      y: 20,
      transition: { 
        duration: 0.2,
        ease: 'easeIn' 
      }
    }
  };

  return (
    <>
      {variant === 'fab' ? (
        <Box 
          sx={{ 
            display: 'flex', 
            position: 'fixed',
            ...(position === 'bottom-right' ? { bottom: 0, right: 0 } : { top: 0, right: 0 }),
            zIndex: 1000,
            overflow: 'hidden'
          }}
        >
          <IconButton
            aria-label="Signaler un bug"
            onClick={handleMenuOpen}
            sx={{
              width: 36,
              height: 36,
              borderRadius: 0,
              boxShadow: 'none',
              p: 0,
              bgcolor: '#FF6B2C',
              '&:hover': {
                bgcolor: '#FF7A35'
              }
            }}
          >
            <ConstructionIcon 
              fontSize="small" 
              sx={{ 
                color: 'white',
                transform: 'rotate(15deg)'
              }} 
            />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            transformOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
          >
            <MenuItem
              onClick={() => handleMenuItemClick('bug')}
              sx={{ 
                color: '#333',
                '&:hover': { bgcolor: 'rgba(255, 107, 44, 0.08)' }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <BugReportIcon fontSize="small" sx={{ color: '#FF6B2C' }} />
                <Typography variant="body2">Signaler un bug</Typography>
              </Box>
            </MenuItem>
            <MenuItem
              onClick={() => handleMenuItemClick('improvement')}
              sx={{ 
                color: '#333',
                '&:hover': { bgcolor: 'rgba(255, 107, 44, 0.08)' }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <LightbulbIcon fontSize="small" sx={{ color: '#FF6B2C' }} />
                <Typography variant="body2">Proposer une amélioration</Typography>
              </Box>
            </MenuItem>
            {showViewBugsButton && (
              <MenuItem
                component={RouterLink}
                to="/dashboard/support"
                sx={{ 
                  color: '#333',
                  '&:hover': { bgcolor: 'rgba(255, 107, 44, 0.08)' }
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <SupportIcon fontSize="small" sx={{ color: '#FF6B2C' }} />
                  <Typography variant="body2">Accéder au support</Typography>
                </Box>
              </MenuItem>
            )}
          </Menu>
        </Box>
      ) : (
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="contained"
            startIcon={<BugReportIcon />}
            onClick={(e) => handleMenuOpen(e)}
            size={size}
            className={className}
            sx={{
              ...(position !== 'inline' ? getPositionStyles() : {}),
              bgcolor: '#FF6B2C',
              '&:hover': {
                bgcolor: '#FF7A35'
              }
            }}
          >
            Signaler un bug
          </Button>
          {showViewBugsButton && (
            <>
              <Button
                variant="outlined"
                startIcon={<ListAltIcon />}
                component={RouterLink}
                to="/dashboard/bug-reports"
                size={size}
                className={className}
                sx={{
                  borderColor: '#FF6B2C',
                  color: '#FF6B2C',
                  '&:hover': {
                    borderColor: '#FF7A35',
                    bgcolor: 'rgba(255, 107, 44, 0.04)'
                  }
                }}
              >
                Voir les bugs
              </Button>
              <Button
                variant="outlined"
                startIcon={<SupportIcon />}
                component={RouterLink}
                to="/dashboard/support"
                size={size}
                className={className}
                sx={{
                  borderColor: '#FF6B2C',
                  color: '#FF6B2C',
                  '&:hover': {
                    borderColor: '#FF7A35',
                    bgcolor: 'rgba(255, 107, 44, 0.04)'
                  }
                }}
              >
                Support
              </Button>
            </>
          )}
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
          >
            <MenuItem
              onClick={() => handleMenuItemClick('bug')}
              sx={{ 
                color: '#333',
                '&:hover': { bgcolor: 'rgba(255, 107, 44, 0.08)' }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <BugReportIcon fontSize="small" sx={{ color: '#FF6B2C' }} />
                <Typography variant="body2">Signaler un bug</Typography>
              </Box>
            </MenuItem>
            <MenuItem
              onClick={() => handleMenuItemClick('improvement')}
              sx={{ 
                color: '#333',
                '&:hover': { bgcolor: 'rgba(255, 107, 44, 0.08)' }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <LightbulbIcon fontSize="small" sx={{ color: '#FF6B2C' }} />
                <Typography variant="body2">Proposer une amélioration</Typography>
              </Box>
            </MenuItem>
            {showViewBugsButton && (
              <MenuItem
                component={RouterLink}
                to="/dashboard/support"
                sx={{ 
                  color: '#333',
                  '&:hover': { bgcolor: 'rgba(255, 107, 44, 0.08)' }
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <SupportIcon fontSize="small" sx={{ color: '#FF6B2C' }} />
                  <Typography variant="body2">Accéder au support</Typography>
                </Box>
              </MenuItem>
            )}
          </Menu>
        </Box>
      )}

      <ModalPortal 
        isOpen={open} 
        onBackdropClick={handleClose}
        containerId="bug-report-modal-root"
      >
        <Paper
          elevation={4}
          sx={{
            width: '100%',
            maxWidth: '800px',
            borderRadius: '8px',
            overflow: 'hidden',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
            display: 'flex',
            flexDirection: 'column',
            maxHeight: '80vh',
            zIndex: 1300,
            overflowY: 'auto'
          }}
        >
          {/* En-tête du modal */}
          <Box 
            sx={{ 
              background: 'linear-gradient(135deg, rgba(255, 107, 44, 0.8) 0%, rgba(255, 122, 53, 0.7) 100%)',
              color: '#fff',
              py: 1.5,
              px: 2,
              position: 'relative',
              flexShrink: 0
            }}
          >
            <Box 
              sx={{ 
                display: 'flex', 
                flexDirection: { xs: 'column', sm: 'row' },
                justifyContent: 'space-between',
                alignItems: { xs: 'flex-start', sm: 'center' },
                gap: { xs: 1.5, sm: 1 }
              }}
            >
              <Typography 
                variant="h6" 
                component="div" 
                sx={{ 
                  fontWeight: 500, 
                  mb: 0, 
                  fontSize: { xs: '1rem', sm: '1.1rem' },
                  paddingRight: { xs: 5, sm: 0 } // Espace pour le bouton fermer sur mobile
                }}
              >
                {reportType === 'bug' ? 'Signaler un bug' : 'Proposer une amélioration'}
              </Typography>
              
              <Box 
                sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: 1,
                  width: { xs: '100%', sm: 'auto' },
                  justifyContent: { xs: 'space-between', sm: 'flex-end' }
                }}
              >
                <Button
                  component={RouterLink}
                  to="/dashboard/bug-reports"
                  size="small"
                  startIcon={<ListAltIcon fontSize="small" />}
                  onClick={handleClose}
                  sx={{
                    color: 'white',
                    fontSize: { xs: '0.7rem', sm: '0.8rem' },
                    textTransform: 'none',
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 255, 255, 0.3)'
                    },
                    borderRadius: '4px',
                    padding: { xs: '3px 8px', sm: '4px 10px' },
                    flexShrink: 0,
                    marginRight: { sm: 3 } // Ajoute de l'espace à droite sur desktop pour éviter le chevauchement avec la croix
                  }}
                >
                  {/* Texte différent selon la taille d'écran */}
                  <Box sx={{ display: { xs: 'none', sm: 'block' } }}>
                    Voir les bugs/améliorations
                  </Box>
                  <Box sx={{ display: { xs: 'block', sm: 'none' } }}>
                    Voir les bugs/améliorations
                  </Box>
                </Button>
              </Box>
            </Box>
            
            {/* Bouton de fermeture en position absolue */}
            <IconButton
              aria-label="Fermer"
              onClick={handleClose}
              size="small"
              sx={{
                position: 'absolute',
                right: 8,
                top: { xs: 8, sm: '50%' },
                transform: { sm: 'translateY(-50%)' }, // Centre verticalement sur desktop
                color: '#fff',
                p: 0.5
              }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>

          {/* Contenu du modal */}
          <Box sx={{ 
            p: { xs: 0, sm: 0, md: 0 }, 
            backgroundColor: '#ffffff',
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            minHeight: 0, /* Important pour que les enfants puissent scroller correctement */
            height: '100%'
          }}>
            <Box sx={{ 
              px: { xs: 2, sm: 3, md: 3.5 },
              pt: { xs: 1.5, sm: 2, md: 2.5 },
              height: '100%',
              flex: 1,
              display: 'flex'
            }}>
              <ProfileCompleteCheck onClose={handleClose}>
                <BugReportForm initialReportType={reportType} onClose={handleClose} />
              </ProfileCompleteCheck>
            </Box>
          </Box>
        </Paper>
      </ModalPortal>
    </>
  );
};

export default ReportBugButton; 