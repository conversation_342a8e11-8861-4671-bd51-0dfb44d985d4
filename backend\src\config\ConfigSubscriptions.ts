// Configuration des abonnements
export const subscriptions = {
  gratuit: {
    prixDeBase: 0,
    services: {
      included: 2,
      additionalCost: 10, // Coût par service supplémentaire
      description: "Nombre de sous-catégories de services que vous pouvez proposer gratuitement."
    },
    galleries: {
      included: 1,
      additionalCost: 3, // Coût par galerie supplémentaire
      description: "Nombre de galeries photos pour présenter vos réalisations."
    },
    interventionAreas: {
      included: 15, // Zone d'intervention offerte
      additionalCost: 15, // Coût par tranche de 10 zones supplémentaires
      maxDistance: 10, // Nombre de zones d'intervention maximum
      description: "Jusqu'à 15km de votre zone géographique d'intervention où vous pouvez proposer vos services.",
      franceEntiere: {
        enabled: false, // Si l'option France entière est disponible
        additionalCost: 500, // Coût supplémentaire pour l'option France entière
        description: "Option pour proposer vos services dans toute la France. Non disponible pour l'offre gratuite."
      },
    },
    conversations_messages_prives: {
      included: 2, // Nombre de conversations incluses
      additionalCost: 10, // Coût par conversation supplémentaire
      description: "Nombre de conversations privées avec des clients."
    },
    history_logs: {
      included: 20, // Nombre de logs incluses
      additionalCost: 0, // Coût par log supplémentaire
      description: "Historique des activités consultables. Limité pour l'offre gratuite."
    },
    transactions: {
      included: 15, // Nombre de transactions incluses
      additionalCost: 0, // Coût par transaction supplémentaire
      description: "Nombre historique de transactions Jobi incluses."
    },
    invoices: {
      included: 2, // Nombre de factures incluses
      additionalCost: 5, // Coût par facture supplémentaire
      description: "Nombre de factures générables gratuitement. Facturation supplémentaire au-delà."
    },
    quotes: {
      included: 2, // Nombre de devis inclus
      additionalCost: 5, // Coût par devis supplémentaire
      description: "Nombre de devis générables gratuitement. Devis supplémentaire payant."
    },
    planning_slots: {
      included: 2, // Nombre de créneaux de planning inclus
      additionalCost: 0, // Coût par créneau supplémentaire
      description: "Nombre de créneaux dans le planning pour les comptes gratuits."
    },
    missionResponses: {
      included: 5,
      additionalCost: 0,
      description: "Nombre de réponses/candidatures aux missions que vous pouvez envoyer par mois. Limité à 5 pour l'offre gratuite."
    },
    favoriteLimit: {
      included: 3,
      description: "Nombre maximum de profils favoris que vous pouvez enregistrer."
    },
    phoneVisibility: {
      enabled: false,
      description: "La visibilité du numéro de téléphone sur les profils publics est réservée aux membres premium."
    },
    aiCredits: {
      included: 1, // Nombre de crédits IA inclus par mois
      additionalCost: 1, // Prix en Euros par pack de crédits
      additionalCostJobi: 3, // Prix en Jobi par pack de crédits
      packs: 20, // Nombre de crédits par pack
      description: "Crédits IA mensuels pour générer du contenu (mission, biographie, etc).",
    },
    businessCards: {
      included: 1, // Nombre de cartes de visite incluses
      additionalCost: 0, // Coût par carte de visite supplémentaire
      description: "Nombre de cartes de visite que vous pouvez créer et gérer.",
    },
    flyers: {
      included: 1, // Nombre de flyers inclus
      additionalCost: 0, // Coût par flyer supplémentaire
      description: "Nombre de flyers que vous pouvez créer et gérer.",
    },
  },
  premium: {
    prixDeBase: 19,
    services: {
      included: 6,
      additionalCost: 10, // Coût par service supplémentaire
      description: "Nombre de sous-catégories de services proposés dans l'abonnement premium. Extension possible à coût fixe."
    },
    galleries: {
      included: 3,
      additionalCost: 3, // Coût par galerie supplémentaire
      description: "Nombre de galeries photos pour valoriser vos prestations. Extension possible."
    },
    interventionAreas: {
      included: 30, // Zone d'intervention offerte
      additionalCost: 10, // Coût par tranche de 10 zones supplémentaires
      maxDistance: 120, // Nombre de zones d'intervention maximum
      description: "Jusqu'à 30km de votre zone géographique d'intervention où vous pouvez proposer vos services. Extension possible à tarif réduit.",
      franceEntiere: {
        enabled: true, // Si l'option France entière est disponible
        additionalCost: 500, // Coût supplémentaire pour l'option France entière
        description: "Option pour proposer vos services dans toute la France. Disponible en option payante."
      },
    },
    conversations_messages_prives: {
      included: 15, // Nombre de conversations incluses
      additionalCost: 5, // Coût par conversation supplémentaire
      description: "Nombre de conversations privées par mois incluses avec les clients. Extension possible."
    },
    history_logs: {
      included: 150, // Nombre de logs incluses
      additionalCost: 0, // Coût par log supplémentaire
      description: "Historique complet des activités sur une longue période."
    },
    transactions: {
      included: 999, // Nombre de transactions incluses
      additionalCost: 0, // Coût par transaction supplémentaire
      description: "Historique de transactions Jobi illimité."
    },
    invoices: {
      included: 999, // Nombre illimité de factures
      additionalCost: 0, // Pas de coût supplémentaire
      description: "Facturation illimitée possible avec l'offre premium."
    },
    quotes: {
      included: 999, // Nombre illimité de devis
      additionalCost: 0, // Pas de coût supplémentaire
      description: "Création de devis illimitée possible avec l'offre premium."
    },
    planning_slots: {
      included: 999, // Nombre de créneaux de planning pratiquement illimité
      additionalCost: 0, // Pas de coût supplémentaire
      description: "Gestion complète du planning avec nombre de créneaux illimités."
    },
    missionResponses: {
      included: 999,
      additionalCost: 0,
      description: "Nombre de réponses/candidatures aux missions que vous pouvez envoyer par mois. Illimité pour l'offre premium."
    },
    favoriteLimit: {
      included: 60,
      description: "Nombre maximum de profils favoris que vous pouvez enregistrer."
    },
    phoneVisibility: {
      enabled: true,
      description: "Vous pouvez voir le numéro de téléphone des autres utilisateurs sur leur profil directement."
    },
    aiCredits: {
      included: 5, // Nombre de crédits IA inclus par mois
      additionalCost: 1, // Prix en Euros par pack de crédits
      additionalCostJobi: 3, // Prix en Jobi par pack de crédits
      packs: 20, // Nombre de crédits par pack
      description: "Crédits IA mensuels pour générer du contenu (mission, biographie, etc).",
    },
    businessCards: {
      included: 5, // Nombre de cartes de visite incluses
      additionalCost: 0, // Coût par carte de visite supplémentaire
      description: "Nombre de cartes de visite que vous pouvez créer et gérer.",
    },
    flyers: {
      included: 20, // Nombre de flyers inclus
      additionalCost: 0, // Coût par flyer supplémentaire
      description: "Nombre de flyers que vous pouvez créer et gérer.",
    },
  },
};

export default subscriptions;