import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Card,
  Button,
  Box,
  Tabs,
  Tab,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Chip,
  Tooltip,
  IconButton,
  styled,
  alpha,
  Menu,
  MenuItem
} from '@mui/material';
import MuiGrid from '@mui/material/Grid';
import EditorThemeProvider from '../../components/cardEditor/EditorTheme';
import {
  Add,
  Edit,
  Delete,
  AutoAwesome,
  FileCopy,
  ArrowUpward,
  ArrowDownward,
  Info
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import cardEditorService from '../../services/cardEditorService';
import { CardTemplate } from '../../types/cardEditor';
import { notify } from '../../components/Notification';
import TemplatePreview from '../../components/cardEditor/TemplatePreview';

// Composants stylisés pour les cartes
const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden',
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  borderRadius: '16px',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  position: 'relative',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 20px 40px ${alpha(theme.palette.primary.main, 0.12)}`,
    '& .preview-actions': {
      opacity: 1,
    },
    '& .card-preview': {
      transform: 'scale(1.03)',
    }
  }
}));

const CardPreview = styled(Box)(({ theme }) => ({
  height: '100%',
  width: '100%',
  transition: 'transform 0.6s cubic-bezier(0.4, 0, 0.2, 1)',
  transform: 'scale(1)',
  position: 'relative',
}));


const CardType = styled(Chip)(({ theme }) => ({
  position: 'absolute',
  top: 12,
  right: 12,
  zIndex: 3,
  fontWeight: 600,
  boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.common.white,
}));

const ElementCounter = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 12,
  left: 12,
  zIndex: 3,
  padding: '4px 8px',
  borderRadius: '99px',
  fontSize: '12px',
  fontWeight: 600,
  backgroundColor: '#FFF8F3',
  color: theme.palette.primary.main,
  boxShadow: '0 2px 6px rgba(0,0,0,0.1)',
  display: 'flex',
  alignItems: 'center',
  gap: '4px',
}));

const DateLabel = styled(Typography)(({ theme }) => ({
  position: 'absolute',
  bottom: 12,
  right: 12,
  zIndex: 3,
  fontSize: '11px',
  color: theme.palette.common.white,
  padding: '2px 6px',
  borderRadius: '99px',
  backgroundColor: alpha(theme.palette.common.black, 0.3),
  backdropFilter: 'blur(4px)',
}));

const AiChip = styled(Chip)(({ theme }) => ({
  position: 'absolute',
  top: 12,
  left: 12,
  zIndex: 3,
  fontSize: '11px',
  boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
}));

const CardFooter = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(1),
}));

const ActionButton = styled(Button)(({ theme }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  fontWeight: 600,
  padding: '6px 12px',
  boxShadow: 'none',
  '&:hover': {
    boxShadow: '0 4px 8px rgba(0,0,0,0.08)',
  }
}));

// Ajout du composant styled PageTitle (copié de Favoris.tsx)
const PageTitle = styled(Typography)(() => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: '16px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: '#FF6B2C',
    borderRadius: '2px',
  },
}));

const CardTemplatesPage: React.FC = () => {
  const navigate = useNavigate();
  const [templates, setTemplates] = useState<CardTemplate[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [tabValue, setTabValue] = useState<number>(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [templateToDelete, setTemplateToDelete] = useState<CardTemplate | null>(null);
  const [businessCardOrientationFilter, setBusinessCardOrientationFilter] = useState<'all' | 'portrait' | 'landscape'>('all');
  const [flyerOrientationFilter, setFlyerOrientationFilter] = useState<'all' | 'portrait' | 'landscape'>('all');

  // État pour le menu de création
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const openCreateMenu = Boolean(anchorEl);

  const handleCreateMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCreateMenuClose = () => {
    setAnchorEl(null);
  };

  // Charger les templates au chargement de la page (une seule fois)
  useEffect(() => {
    const initialLoad = async () => {
      setIsLoading(true);
      try {
        const userTemplates = await cardEditorService.getTemplates();
        // Charger la première page de templates publics
        // const initialPublicTemplates = await cardEditorService.getPublicTemplates(0, 20); // Charger les 20 premiers

        setTemplates(userTemplates);
        // setPublicTemplates(initialPublicTemplates);
        // setPublicTemplatesOffset(20); // Mettre à jour l'offset
        // setHasMorePublicTemplates(initialPublicTemplates.length === 20); // Indiquer s'il y a plus de pages
      } catch (error) {
        console.error('Erreur lors du chargement des templates:', error);
        notify('Erreur lors du chargement des templates', 'error');
      } finally {
        setIsLoading(false);
      }
    };
    initialLoad();
  }, []); // Dépendance vide pour exécuter une seule fois au montage

  // Changer d'onglet
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Créer un nouveau template
  const handleCreateNew = (type: 'business_card' | 'business_card_landscape' | 'flyer' | 'flyer_landscape') => {
    navigate(`/dashboard/card-editor?type=${type}`);
    handleCreateMenuClose(); // Fermer le menu après sélection
  };

  // Naviguer pour la génération IA
  const handleGenerateAI = () => {
    navigate('/dashboard/card-editor?generate=true');
    handleCreateMenuClose(); // Fermer le menu après sélection
  };

  // Éditer un template existant
  const handleEdit = (template: CardTemplate) => {
    // Vérifier si le template est désactivé
    if (template.name.startsWith('[DÉSACTIVÉ]')) {
      notify('Ce template est désactivé en raison des limitations de votre abonnement. Passez à l\'abonnement premium pour l\'activer.', 'error');
      return;
    }
    navigate(`/dashboard/card-editor/${template.id}`);
  };

  // Dupliquer un template
  const handleDuplicate = async (template: CardTemplate) => {
    // Vérifier si le template est désactivé
    if (template.name.startsWith('[DÉSACTIVÉ]')) {
      notify('Impossible de dupliquer un template désactivé. Passez à l\'abonnement premium pour activer plus de templates.', 'error');
      return;
    }

    try {
      const duplicatedTemplate = {
        ...template,
        name: `Copie de ${template.name}`,
        is_public: false
      };

      delete (duplicatedTemplate as any).id;
      delete (duplicatedTemplate as any).created_at;
      delete (duplicatedTemplate as any).updated_at;

      const newTemplate = await cardEditorService.createTemplate(duplicatedTemplate);
      if (newTemplate) {
        notify('Template dupliqué avec succès', 'success');
        setTemplates(prevTemplates => [...prevTemplates, newTemplate]);
      }
    } catch (error) {
      console.error('Erreur lors de la duplication du template:', error);
      notify('Erreur lors de la duplication du template', 'error');
    }
  };

  // Note: L'activation/désactivation des templates est gérée automatiquement par le backend
  // en fonction du type d'abonnement de l'utilisateur

  // Ouvrir la boîte de dialogue de suppression
  const handleDeleteClick = (template: CardTemplate) => {
    setTemplateToDelete(template);
    setDeleteDialogOpen(true);
  };

  // Supprimer un template
  const handleDelete = async () => {
    if (!templateToDelete) return;

    try {
      const success = await cardEditorService.deleteTemplate(templateToDelete.id);
      if (success) {
        // Mettre à jour la liste des templates
        setTemplates(templates.filter(t => t.id !== templateToDelete.id));
        notify('Template supprimé avec succès', 'success');
      }
    } catch (error) {
      console.error('Erreur lors de la suppression du template:', error);
      notify('Erreur lors de la suppression du template', 'error');
    } finally {
      setDeleteDialogOpen(false);
      setTemplateToDelete(null);
    }
  };

  // Générer un aperçu du template avec Konva (fidélité parfaite)
  const generatePreview = (template: CardTemplate) => {
    const ratio = template.template_data.width / template.template_data.height;
    return (
      <Box
        sx={{
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'flex-start',
          justifyContent: 'center',
          background: '#fff',
          borderRadius: '16px',
          boxShadow: '0 4px 24px rgba(0,0,0,0.08)',
          overflow: 'hidden',
          minHeight: 120,
          // Ajout du style pour masquer le bouton de déplacement dans la prévisualisation
          '& [aria-label="Activer le mode déplacement"]': {
            display: 'none !important', // Utiliser !important pour forcer le masquage si nécessaire
          },
        }}
      >
        <Box
          sx={{
            width: '100%',
            aspectRatio: ratio,
            borderRadius: '0px',
            overflow: 'hidden',
            background: '#fff',
            display: 'flex',
            justifyContent: 'center',
            pt: 3,
            px: 1,
            pb: 1,
            height: 'inherit',
          }}
        >
          <TemplatePreview // Adapté pour la prévisualisation afin d'éviter les requêtes API inutiles
            templateData={template.template_data}
            showProfessionalFooter={true}
            showWatermark={false}
          />
        </Box>
      </Box>
    );
  };

  // Fonction pour déplacer un template vers le haut ou le bas
  const moveTemplate = async (index: number, direction: 'up' | 'down') => {
    if ((direction === 'up' && index === 0) || (direction === 'down' && index === templates.length - 1)) return;
    const newTemplates = [...templates];
    const swapIndex = direction === 'up' ? index - 1 : index + 1;
    // Échange les deux templates
    [newTemplates[index], newTemplates[swapIndex]] = [newTemplates[swapIndex], newTemplates[index]];
    // Met à jour les order_index localement
    const reordered = newTemplates.map((tpl, idx) => ({ ...tpl, order_index: idx }));
    setTemplates(reordered);
    // Envoie la nouvelle ordre au backend
    await cardEditorService.reorderTemplates(reordered.map(tpl => ({ id: tpl.id, order_index: tpl.order_index })));
    // Recharge pour être sûr
    // Pas besoin de recharger, l'état local est déjà mis à jour
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <EditorThemeProvider>
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <PageTitle variant="h1">
          Mes Templates
        </PageTitle>

      <Box sx={{ mb: 4 }}>
        {/* Nouveau bouton "Créer un template" avec menu */}
        <Button
          id="create-template-button"
          variant="contained"
          startIcon={<Add />}
          onClick={handleCreateMenuClick}
          aria-controls={openCreateMenu ? 'create-template-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={openCreateMenu ? 'true' : undefined}
          sx={{
            borderRadius: '8px',
            textTransform: 'none',
            fontWeight: 600,
            py: 1,
            px: 3,
            mt: 3
          }}
        >
          Créer un template
        </Button>
        <Menu
          id="create-template-menu"
          anchorEl={anchorEl}
          open={openCreateMenu}
          onClose={handleCreateMenuClose}
          slotProps={{
            paper: {
              'aria-labelledby': 'create-template-button',
            }
          }}
        >
          <MenuItem onClick={() => handleCreateNew('business_card')}>Carte de visite Portrait</MenuItem>
          <MenuItem onClick={() => handleCreateNew('business_card_landscape')}>Carte de visite Paysage</MenuItem>
          <MenuItem onClick={() => handleCreateNew('flyer')}>Flyer Portrait</MenuItem>
          <MenuItem onClick={() => handleCreateNew('flyer_landscape')}>Flyer Paysage</MenuItem>
          <MenuItem onClick={handleGenerateAI} sx={{ mt: 1, borderTop: '1px solid #eee' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <AutoAwesome fontSize="small" /> Générer avec l'IA
            </Box>
          </MenuItem>
        </Menu>
      </Box>

        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            sx={{
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '1rem',
                px: 3,
              },
              '& .Mui-selected': {
                color: '#FF6B2C',
              },
              '& .MuiTabs-indicator': {
                backgroundColor: '#FF6B2C',
                height: 3,
                borderRadius: '3px 3px 0 0',
              }
            }}
          >
          <Tab label="Carte de visite" />
          <Tab label="Flyer" />
        </Tabs>
      </Box>

      {tabValue === 0 && ( // Tab for Carte de visite
        <>
          <Box sx={{
            mb: 3,
            display: 'flex',
            gap: 1,
            flexDirection: { xs: 'column', sm: 'row' },
            alignItems: { xs: 'flex-start', sm: 'center' }
          }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'text.secondary' }}>
              Filtrer par orientation :
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Button
                variant={businessCardOrientationFilter === 'all' ? 'contained' : 'outlined'}
                size="small"
                onClick={() => setBusinessCardOrientationFilter('all')}
                sx={{ borderRadius: '8px', textTransform: 'none', fontWeight: 600 }}
              >
                Tous
              </Button>
              <Button
                variant={businessCardOrientationFilter === 'portrait' ? 'contained' : 'outlined'}
                size="small"
                onClick={() => setBusinessCardOrientationFilter('portrait')}
                sx={{ borderRadius: '8px', textTransform: 'none', fontWeight: 600 }}
              >
                Portrait
              </Button>
              <Button
                variant={businessCardOrientationFilter === 'landscape' ? 'contained' : 'outlined'}
                size="small"
                onClick={() => setBusinessCardOrientationFilter('landscape')}
                sx={{ borderRadius: '8px', textTransform: 'none', fontWeight: 600 }}
              >
                Paysage
              </Button>
            </Box>
          </Box>
          {templates.length === 0 ? (
              <Box sx={{
                textAlign: 'center',
                py: 6,
                backgroundColor: '#FFF8F3',
                borderRadius: '16px',
                border: '1px dashed #FFE4BA',
              }}>
                <Typography variant="h6" color="#FF6B2C" sx={{ fontWeight: 600 }}>
                Vous n'avez pas encore de templates
              </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ mt: 1, mb: 3 }}>
                Créez votre premier template en cliquant sur les boutons ci-dessus
              </Typography>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={() => handleCreateNew('business_card_landscape')}
                  sx={{
                    borderRadius: '99px',
                    textTransform: 'none',
                    fontWeight: 600,
                    px: 4,
                    py: 1.2
                  }}
                >
                  Créer un template
                </Button>
            </Box>
          ) : (
            <MuiGrid container spacing={3}>
              {templates
                .filter(template => template.type === 'business_card' || template.type === 'business_card_landscape')
                .filter(template => {
                  if (businessCardOrientationFilter === 'all') return true;
                  if (businessCardOrientationFilter === 'portrait') return template.type === 'business_card';
                  if (businessCardOrientationFilter === 'landscape') return template.type === 'business_card_landscape';
                  return true;
                })
                .map((template, index) => (
                <MuiGrid key={template.id} size={{ xs: 12, sm: 6, md: 6, lg: 4 }} sx={{ display: 'flex' }}>
                  <StyledCard
                    sx={{
                      width: '100%',
                      opacity: template.name.startsWith('[DÉSACTIVÉ]') ? 0.7 : 1,
                      position: 'relative'
                    }}
                  >
                    {template.name.startsWith('[DÉSACTIVÉ]') && (
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          backgroundColor: 'rgba(0, 0, 0, 0.3)',
                          zIndex: 10,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          backdropFilter: 'blur(4px)',
                          borderRadius: '16px'
                        }}
                      >
                        <Typography
                          variant="body1"
                          sx={{
                            color: '#FF6B2C',
                            fontWeight: 'bold',
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            padding: '8px 16px',
                            borderRadius: '8px',
                            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                            border: '1px solid #FFE4BA'
                          }}
                        >
                          Désactivé - Limite d'abonnement
                        </Typography>
                      </Box>
                    )}
                    <Box sx={{ position: 'relative', height: template.type.includes('business_card') ? (template.type.includes('landscape') ? 280 : 220) : (template.type.includes('landscape') ? 280 : 350) }}>
                      {/* Boutons Monter/Descendre flottants */}
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 12,
                          left: 12,
                          zIndex: 3, // Assurez-vous que le zIndex est suffisant
                          display: 'flex',
                          gap: 0.5,
                        }}
                      >
                        <Tooltip title="Monter">
                          <span>
                            <IconButton
                              size="small"
                              onClick={() => moveTemplate(index, 'up')}
                              disabled={index === 0}
                              sx={{
                                color: index === 0 ? '#ccc' : '#FF6B2C',
                                background: 'white', // Fond blanc pour visibilité
                                borderRadius: '50%',
                                boxShadow: '0 2px 8px rgba(0,0,0,0.10)', // Ombre légère
                                p: 0.5,
                                transition: 'background 0.2s',
                                '&:hover': {
                                  background: '#FFE4BA', // Couleur au survol
                                },
                              }}
                            >
                              <ArrowUpward fontSize="small" />
                            </IconButton>
                          </span>
                        </Tooltip>
                        <Tooltip title="Descendre">
                          <span>
                            <IconButton
                              size="small"
                              onClick={() => moveTemplate(index, 'down')}
                              disabled={index === templates.length - 1}
                              sx={{
                                color: index === templates.length - 1 ? '#ccc' : '#FF6B2C',
                                background: 'white', // Fond blanc pour visibilité
                                borderRadius: '50%',
                                boxShadow: '0 2px 8px rgba(0,0,0,0.10)', // Ombre légère
                                p: 0.5,
                                transition: 'background 0.2s',
                                '&:hover': {
                                  background: '#FFE4BA', // Couleur au survol
                                },
                              }}
                            >
                              <ArrowDownward fontSize="small" />
                            </IconButton>
                          </span>
                        </Tooltip>
                      </Box>
                      <CardPreview className="card-preview">
                        {generatePreview(template)}
                      </CardPreview>

                      {template.is_ai_generated && (
                        <AiChip
                          icon={<AutoAwesome fontSize="small" />}
                          label="IA"
                          size="small"
                          color="secondary"
                          variant="filled"
                        />
                      )}

                      <CardType
                        label={template.type.includes('business_card') ? 'Carte' : 'Flyer'}
                        size="small"
                      />
                      {template.type.includes('landscape') && (
                        <Chip
                          label="Paysage"
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: 12,
                            right: 70,
                            zIndex: 3,
                            fontWeight: 600,
                            boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                            backgroundColor: '#FFE4BA',
                            color: '#FF6B2C',
                          }}
                        />
                      )}

                      <DateLabel variant="caption">
                        {new Date(template.created_at).toLocaleDateString()}
                      </DateLabel>
                    </Box>

                    <CardFooter>
                      <Typography variant="h6" sx={{
                        fontWeight: 700,
                        fontSize: { xs: '1.08rem', sm: '1.18rem' },
                        color: '#2D3748',
                        textAlign: 'center',
                        background: 'none',
                        boxShadow: 'none',
                        borderRadius: 0,
                        px: 0.5,
                        py: 0.5,
                        mb: 1.2,
                        mt: 1.5,
                        position: 'relative',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        whiteSpace: 'normal',
                        minHeight: 55,
                        letterSpacing: 0.01,
                        lineHeight: 1.35,
                        wordBreak: 'break-word',
                        '&::after': {
                          content: '""',
                          position: 'absolute',
                          left: '50%',
                          transform: 'translateX(-50%) rotate(-2deg)',
                          bottom: 6,
                          width: '80%',
                          height: '16px',
                          background: '#FFE4BA',
                          borderRadius: '8px',
                          opacity: 0.85,
                          zIndex: 0,
                        },
                        zIndex: 1,
                      }}>
                        <span style={{ position: 'relative', zIndex: 2 }}>{template.name}</span>
                      </Typography>
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'row',
                          gap: 1,
                          width: '100%',
                          mt: 1,
                          justifyContent: 'center',
                          flexWrap: 'wrap',
                        }}
                      >
                        <ActionButton
                          size="small"
                          variant="outlined"
                          startIcon={<Edit />}
                          onClick={() => handleEdit(template)}
                          disabled={template.name.startsWith('[DÉSACTIVÉ]')}
                          sx={{
                            opacity: template.name.startsWith('[DÉSACTIVÉ]') ? 0.6 : 1,
                            '&.Mui-disabled': {
                              color: 'text.disabled',
                              borderColor: 'rgba(0, 0, 0, 0.12)'
                            }
                          }}
                        >
                          Éditer
                        </ActionButton>
                        <ActionButton
                          size="small"
                          variant="outlined"
                          startIcon={<FileCopy />}
                          onClick={() => handleDuplicate(template)}
                          disabled={template.name.startsWith('[DÉSACTIVÉ]')}
                          sx={{
                            opacity: template.name.startsWith('[DÉSACTIVÉ]') ? 0.6 : 1,
                            '&.Mui-disabled': {
                              color: 'text.disabled',
                              borderColor: 'rgba(0, 0, 0, 0.12)'
                            }
                          }}
                        >
                          Dupliquer
                        </ActionButton>
                        <ActionButton
                          size="small"
                          variant="outlined"
                          color="error"
                          startIcon={<Delete />}
                          onClick={() => handleDeleteClick(template)}
                        >
                          Supprimer
                        </ActionButton>
                        {template.name.startsWith('[DÉSACTIVÉ]') && (
                          <Tooltip title="Ce template est désactivé en raison des limitations de votre abonnement. Passez à l'abonnement premium pour l'activer.">
                            <Chip
                              icon={<Info fontSize="small" />}
                              label="Désactivé"
                              size="small"
                              color="default"
                              sx={{
                                bgcolor: alpha('#9e9e9e', 0.1),
                                color: '#9e9e9e',
                                fontWeight: 'bold',
                                ml: 1
                              }}
                            />
                          </Tooltip>
                        )}
                      </Box>
                    </CardFooter>
                  </StyledCard>
                </MuiGrid>
              ))}
            </MuiGrid>
          )}
        </>
      )}

      {tabValue === 1 && ( // Tab for Flyer
        <>
          <Box sx={{
            mb: 3,
            display: 'flex',
            gap: 1,
            flexDirection: { xs: 'column', sm: 'row' },
            alignItems: { xs: 'flex-start', sm: 'center' }
          }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'text.secondary' }}>
              Filtrer par orientation :
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Button
                variant={flyerOrientationFilter === 'all' ? 'contained' : 'outlined'}
                size="small"
                onClick={() => setFlyerOrientationFilter('all')}
                sx={{ borderRadius: '8px', textTransform: 'none', fontWeight: 600 }}
              >
                Tous
              </Button>
              <Button
                variant={flyerOrientationFilter === 'portrait' ? 'contained' : 'outlined'}
                size="small"
                onClick={() => setFlyerOrientationFilter('portrait')}
                sx={{ borderRadius: '8px', textTransform: 'none', fontWeight: 600 }}
              >
                Portrait
              </Button>
              <Button
                variant={flyerOrientationFilter === 'landscape' ? 'contained' : 'outlined'}
                size="small"
                onClick={() => setFlyerOrientationFilter('landscape')}
                sx={{ borderRadius: '8px', textTransform: 'none', fontWeight: 600 }}
              >
                Paysage
              </Button>
            </Box>
          </Box>
          {templates.length === 0 ? (
              <Box sx={{
                textAlign: 'center',
                py: 6,
                backgroundColor: '#FFF8F3',
                borderRadius: '16px',
                border: '1px dashed #FFE4BA',
              }}>
                <Typography variant="h6" color="#FF6B2C" sx={{ fontWeight: 600 }}>
                Vous n'avez pas encore de templates
              </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ mt: 1, mb: 3 }}>
                Créez votre premier template en cliquant sur les boutons ci-dessus
              </Typography>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={() => handleCreateNew('flyer')}
                  sx={{
                    borderRadius: '99px',
                    textTransform: 'none',
                    fontWeight: 600,
                    px: 4,
                    py: 1.2
                  }}
                >
                  Créer un template
                </Button>
            </Box>
          ) : (
            <MuiGrid container spacing={3}>
              {templates
                .filter(template => template.type === 'flyer' || template.type === 'flyer_landscape')
                .filter(template => {
                  if (flyerOrientationFilter === 'all') return true;
                  if (flyerOrientationFilter === 'portrait') return template.type === 'flyer';
                  if (flyerOrientationFilter === 'landscape') return template.type === 'flyer_landscape';
                  return true;
                })
                .map((template, index) => (
                <MuiGrid key={template.id} size={{ xs: 12, sm: 6, md: 6, lg: 4 }} sx={{ display: 'flex' }}>
                  <StyledCard
                    sx={{
                      width: '100%',
                      opacity: template.name.startsWith('[DÉSACTIVÉ]') ? 0.7 : 1,
                      position: 'relative'
                    }}
                  >
                    {template.name.startsWith('[DÉSACTIVÉ]') && (
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          backgroundColor: 'rgba(0, 0, 0, 0.3)',
                          zIndex: 10,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          backdropFilter: 'blur(4px)',
                          borderRadius: '16px'
                        }}
                      >
                        <Typography
                          variant="body1"
                          sx={{
                            color: '#FF6B2C',
                            fontWeight: 'bold',
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            padding: '8px 16px',
                            borderRadius: '8px',
                            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                            border: '1px solid #FFE4BA'
                          }}
                        >
                          Désactivé - Limite d'abonnement
                        </Typography>
                      </Box>
                    )}
                    <Box sx={{ position: 'relative', height: template.type.includes('business_card') ? (template.type.includes('landscape') ? 280 : 220) : (template.type.includes('landscape') ? 280 : 350) }}>
                      {/* Boutons Monter/Descendre flottants */}
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 12,
                          left: 12,
                          zIndex: 3, // Assurez-vous que le zIndex est suffisant
                          display: 'flex',
                          gap: 0.5,
                        }}
                      >
                        <Tooltip title="Monter">
                          <span>
                            <IconButton
                              size="small"
                              onClick={() => moveTemplate(index, 'up')}
                              disabled={index === 0}
                              sx={{
                                color: index === 0 ? '#ccc' : '#FF6B2C',
                                background: 'white', // Fond blanc pour visibilité
                                borderRadius: '50%',
                                boxShadow: '0 2px 8px rgba(0,0,0,0.10)', // Ombre légère
                                p: 0.5,
                                transition: 'background 0.2s',
                                '&:hover': {
                                  background: '#FFE4BA', // Couleur au survol
                                },
                              }}
                            >
                              <ArrowUpward fontSize="small" />
                            </IconButton>
                          </span>
                        </Tooltip>
                        <Tooltip title="Descendre">
                          <span>
                            <IconButton
                              size="small"
                              onClick={() => moveTemplate(index, 'down')}
                              disabled={index === templates.length - 1}
                              sx={{
                                color: index === templates.length - 1 ? '#ccc' : '#FF6B2C',
                                background: 'white', // Fond blanc pour visibilité
                                borderRadius: '50%',
                                boxShadow: '0 2px 8px rgba(0,0,0,0.10)', // Ombre légère
                                p: 0.5,
                                transition: 'background 0.2s',
                                '&:hover': {
                                  background: '#FFE4BA', // Couleur au survol
                                },
                              }}
                            >
                              <ArrowDownward fontSize="small" />
                            </IconButton>
                          </span>
                        </Tooltip>
                      </Box>
                      <CardPreview className="card-preview">
                        {generatePreview(template)}
                      </CardPreview>

                      {template.is_ai_generated && (
                        <AiChip
                          icon={<AutoAwesome fontSize="small" />}
                          label="IA"
                          size="small"
                          color="secondary"
                          variant="filled"
                        />
                      )}

                      <CardType
                        label={template.type.includes('business_card') ? 'Carte' : 'Flyer'}
                        size="small"
                      />
                      {template.type.includes('landscape') && (
                        <Chip
                          label="Paysage"
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: 12,
                            right: 70,
                            zIndex: 3,
                            fontWeight: 600,
                            boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                            backgroundColor: '#FFE4BA',
                            color: '#FF6B2C',
                          }}
                        />
                      )}

                      <ElementCounter>
                        {template.template_data.elements.length} éléments
                      </ElementCounter>

                      <DateLabel variant="caption">
                        {new Date(template.created_at).toLocaleDateString()}
                      </DateLabel>
                    </Box>

                    <CardFooter>
                      <Typography variant="h6" sx={{
                        fontWeight: 700,
                        fontSize: { xs: '1.08rem', sm: '1.18rem' },
                        color: '#2D3748',
                        textAlign: 'center',
                        background: 'none',
                        boxShadow: 'none',
                        borderRadius: 0,
                        px: 0.5,
                        py: 0.5,
                        mb: 1.2,
                        mt: 1.5,
                        position: 'relative',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        whiteSpace: 'normal',
                        minHeight: 55,
                        letterSpacing: 0.01,
                        lineHeight: 1.35,
                        wordBreak: 'break-word',
                        '&::after': {
                          content: '""',
                          position: 'absolute',
                          left: '50%',
                          transform: 'translateX(-50%) rotate(-2deg)',
                          bottom: 6,
                          width: '80%',
                          height: '16px',
                          background: '#FFE4BA',
                          borderRadius: '8px',
                          opacity: 0.85,
                          zIndex: 0,
                        },
                        zIndex: 1,
                      }}>
                        <span style={{ position: 'relative', zIndex: 2 }}>{template.name}</span>
                      </Typography>
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'row',
                          gap: 1,
                          width: '100%',
                          mt: 1,
                          justifyContent: 'center',
                          flexWrap: 'wrap',
                        }}
                      >
                        <ActionButton
                          size="small"
                          variant="outlined"
                          startIcon={<Edit />}
                          onClick={() => handleEdit(template)}
                          disabled={template.name.startsWith('[DÉSACTIVÉ]')}
                          sx={{
                            opacity: template.name.startsWith('[DÉSACTIVÉ]') ? 0.6 : 1,
                            '&.Mui-disabled': {
                              color: 'text.disabled',
                              borderColor: 'rgba(0, 0, 0, 0.12)'
                            }
                          }}
                        >
                          Éditer
                        </ActionButton>
                        <ActionButton
                          size="small"
                          variant="outlined"
                          startIcon={<FileCopy />}
                          onClick={() => handleDuplicate(template)}
                          disabled={template.name.startsWith('[DÉSACTIVÉ]')}
                          sx={{
                            opacity: template.name.startsWith('[DÉSACTIVÉ]') ? 0.6 : 1,
                            '&.Mui-disabled': {
                              color: 'text.disabled',
                              borderColor: 'rgba(0, 0, 0, 0.12)'
                            }
                          }}
                        >
                          Dupliquer
                        </ActionButton>
                        <ActionButton
                          size="small"
                          variant="outlined"
                          color="error"
                          startIcon={<Delete />}
                          onClick={() => handleDeleteClick(template)}
                        >
                          Supprimer
                        </ActionButton>
                        {template.name.startsWith('[DÉSACTIVÉ]') && (
                          <Tooltip title="Ce template est désactivé en raison des limitations de votre abonnement. Passez à l'abonnement premium pour l'activer.">
                            <Chip
                              icon={<Info fontSize="small" />}
                              label="Désactivé"
                              size="small"
                              color="default"
                              sx={{
                                bgcolor: alpha('#9e9e9e', 0.1),
                                color: '#9e9e9e',
                                fontWeight: 'bold',
                                ml: 1
                              }}
                            />
                          </Tooltip>
                        )}
                      </Box>
                    </CardFooter>
                  </StyledCard>
                </MuiGrid>
              ))}
            </MuiGrid>
          )}
        </>
      )}

      {/* Boîte de dialogue de confirmation de suppression */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        slotProps={{
          paper: {
            sx: {
              borderRadius: '16px',
              p: 1
            }
          }
        }}
        >
          <DialogTitle sx={{ fontWeight: 600 }}>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Êtes-vous sûr de vouloir supprimer le template "{templateToDelete?.name}" ?
            Cette action est irréversible.
          </DialogContentText>
        </DialogContent>
          <DialogActions sx={{ p: 2 }}>
            <Button
              onClick={() => setDeleteDialogOpen(false)}
              sx={{ borderRadius: '8px', textTransform: 'none', fontWeight: 600 }}
            >
              Annuler
            </Button>
            <Button
              onClick={handleDelete}
              color="error"
              variant="contained"
              sx={{ borderRadius: '8px', textTransform: 'none', fontWeight: 600 }}
            >
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
    </EditorThemeProvider>
  );
};

export default CardTemplatesPage;
