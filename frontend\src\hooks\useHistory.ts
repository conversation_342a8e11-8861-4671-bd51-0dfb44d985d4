import { useState, useRef } from 'react';
import { api } from '../services/api';

export interface LoginHistory {
  id: string;
  login_date: string;
  ip_address: string;
  city?: string;
  country?: string;
  region?: string;
}

export interface ActionHistory {
  id: string;
  action_type: string;
  action_date: string;
  resource_id: string;
  resource_type: string;
  details: any;
  ip_address: string;
}

export interface LogType {
  value: string;
  label: string;
}

export interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  historyLimit?: number;
}

export interface SubscriptionInfo {
  type: string;
  historyLimit: number;
}

interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiresIn: number;
}

interface UseHistoryReturn {
  fetchLoginHistory: (page: number, limit: number) => Promise<{
    data: LoginHistory[];
    pagination: PaginationData;
    subscription: SubscriptionInfo;
  }>;
  fetchActionHistory: (page: number, limit: number, type: string) => Promise<{
    data: ActionHistory[];
    pagination: PaginationData;
    subscription: SubscriptionInfo;
  }>;
  fetchActionTypes: () => Promise<LogType[]>;
  isLoading: boolean;
  error: string | null;
  clearCache: () => void;
}

// Cache expiration en ms (30 secondes)
const CACHE_EXPIRATION = 30 * 1000;

export const useHistory = (): UseHistoryReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const cacheRef = useRef<Record<string, CacheItem<any>>>({});

  // Fonction pour vérifier si un élément du cache est valide
  const isValidCache = <T,>(cacheKey: string): CacheItem<T> | null => {
    const cacheItem = cacheRef.current[cacheKey] as CacheItem<T> | undefined;
    
    if (!cacheItem) return null;
    
    const now = Date.now();
    if (now - cacheItem.timestamp > cacheItem.expiresIn) {
      // Cache expiré, on le supprime
      delete cacheRef.current[cacheKey];
      return null;
    }
    
    return cacheItem;
  };

  // Fonction pour ajouter un élément au cache
  const addToCache = <T,>(cacheKey: string, data: T, expiresIn: number = CACHE_EXPIRATION) => {
    cacheRef.current[cacheKey] = {
      data,
      timestamp: Date.now(),
      expiresIn
    };
  };

  const clearCache = () => {
    cacheRef.current = {};
  };

  const fetchLoginHistory = async (page: number, limit: number) => {
    try {
      const cacheKey = `login_history_${page}_${limit}`;
      const cachedData = isValidCache<{
        data: LoginHistory[];
        pagination: PaginationData;
        subscription: SubscriptionInfo;
      }>(cacheKey);

      if (cachedData) {
        return cachedData.data;
      }
      
      setIsLoading(true);
      setError(null);

      if (page <= 0) page = 1;

      const response = await api.get('/api/users/login-history', {
        params: { page, limit }
      });

      if (!response.data.success) {
        throw new Error('Impossible de récupérer l\'historique des connexions');
      }

      const result = {
        data: response.data.data,
        pagination: {
          total: response.data.pagination.totalCount || 0,
          page: response.data.pagination.page || 1,
          limit: response.data.pagination.limit || 5,
          totalPages: Math.ceil((response.data.pagination.totalCount || 0) / (response.data.pagination.limit || 5)),
          historyLimit: response.data.pagination.maxHistoryCount || 30
        },
        subscription: {
          type: response.data.subscription?.type || (response.data.pagination.maxHistoryCount > 30 ? 'premium' : 'gratuit'),
          historyLimit: response.data.pagination.maxHistoryCount || 30
        }
      };

      // Mettre en cache le résultat
      addToCache(cacheKey, result);

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors du chargement de l\'historique des connexions';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const fetchActionHistory = async (page: number, limit: number, type: string) => {
    try {
      const cacheKey = `action_history_${page}_${limit}_${type}`;
      const cachedData = isValidCache<{
        data: ActionHistory[];
        pagination: PaginationData;
        subscription: SubscriptionInfo;
      }>(cacheKey);

      if (cachedData) {
        return cachedData.data;
      }
      
      setIsLoading(true);
      setError(null);

      if (page <= 0) page = 1;

      const response = await api.get('/api/users/actions-history', {
        params: { page, limit, type }
      });

      if (!response.data.success) {
        throw new Error('Impossible de récupérer l\'historique des actions');
      }

      const result = {
        data: response.data.data,
        pagination: {
          total: response.data.pagination.totalCount || 0,
          page: response.data.pagination.page || 1,
          limit: response.data.pagination.limit || 5,
          totalPages: Math.ceil((response.data.pagination.totalCount || 0) / (response.data.pagination.limit || 5)),
          historyLimit: response.data.pagination.maxHistoryCount || 30
        },
        subscription: {
          type: response.data.subscription?.type || (response.data.pagination.maxHistoryCount > 30 ? 'premium' : 'gratuit'),
          historyLimit: response.data.pagination.maxHistoryCount || 30
        }
      };

      // Mettre en cache le résultat
      addToCache(cacheKey, result);

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors du chargement de l\'historique des actions';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const fetchActionTypes = async () => {
    try {
      const cacheKey = 'action_types';
      const cachedData = isValidCache<LogType[]>(cacheKey);

      if (cachedData) {
        return cachedData.data;
      }
      
      setIsLoading(true);
      setError(null);

      const response = await api.get('/api/users/action-types');

      if (!response.data.success) {
        throw new Error('Impossible de récupérer les types d\'actions');
      }

      // Mettre en cache le résultat avec une durée plus longue (5 minutes)
      addToCache(cacheKey, response.data.data, 5 * 60 * 1000);

      return response.data.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors du chargement des types d\'actions';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    fetchLoginHistory,
    fetchActionHistory,
    fetchActionTypes,
    isLoading,
    error,
    clearCache
  };
}; 